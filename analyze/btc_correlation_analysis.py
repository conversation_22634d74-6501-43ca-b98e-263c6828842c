#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币种与BTC价格相关性分析脚本
使用过去24小时的1分钟K线数据计算相关性，从不相关到相关排序
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BTCCorrelationAnalyzer:
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.session = requests.Session()

    def get_kline_data(self, symbol, interval='1m', limit=1440):
        """获取K线数据 (1440分钟 = 24小时)"""
        for attempt in range(3):
            try:
                url = f"{self.base_url}/fapi/v1/klines"
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': limit
                }
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} K线数据失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_futures_symbols(self):
        """获取所有期货交易对"""
        for attempt in range(3):
            try:
                url = f"{self.base_url}/fapi/v1/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                data = response.json()

                symbols = []
                for symbol_info in data['symbols']:
                    if (symbol_info['status'] == 'TRADING' and
                        symbol_info['contractType'] == 'PERPETUAL' and
                        symbol_info['quoteAsset'] == 'USDT'):
                        symbols.append(symbol_info['symbol'])

                return symbols
            except Exception as e:
                print(f"获取交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return []

    def process_kline_data(self, kline_data):
        """处理K线数据，提取收盘价"""
        if not kline_data:
            return None

        prices = []
        timestamps = []

        for kline in kline_data:
            timestamp = kline[0]  # 开盘时间
            close_price = float(kline[4])  # 收盘价

            prices.append(close_price)
            timestamps.append(timestamp)

        return pd.Series(prices, index=timestamps)

    def calculate_correlation(self, price_series1, price_series2):
        """计算两个价格序列的相关性"""
        if price_series1 is None or price_series2 is None:
            return None

        if len(price_series1) == 0 or len(price_series2) == 0:
            return None

        # 确保两个序列长度相同
        min_length = min(len(price_series1), len(price_series2))
        if min_length < 100:  # 至少需要100个数据点
            return None

        series1 = price_series1.iloc[-min_length:]
        series2 = price_series2.iloc[-min_length:]

        # 计算价格变化率
        returns1 = series1.pct_change().dropna()
        returns2 = series2.pct_change().dropna()

        if len(returns1) < 50 or len(returns2) < 50:
            return None

        # 计算相关系数
        correlation = returns1.corr(returns2)

        return correlation if not pd.isna(correlation) else None

    def format_correlation(self, correlation):
        """格式化相关性数值"""
        if correlation is None:
            return "N/A"
        return f"{correlation:.4f}"

    def get_correlation_description(self, correlation):
        """获取相关性描述"""
        if correlation is None:
            return "无数据"

        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "强相关"
        elif abs_corr >= 0.6:
            return "中等相关"
        elif abs_corr >= 0.3:
            return "弱相关"
        else:
            return "不相关"

    def analyze_correlations(self):
        """分析所有币种与BTC的相关性"""
        print("正在获取交易对列表...")
        all_symbols = self.get_futures_symbols()

        if not all_symbols:
            print("获取交易对列表失败")
            return []

        # 过滤掉BTC相关的交易对，避免重复
        symbols = [s for s in all_symbols if s != 'BTCUSDT']

        print(f"找到 {len(symbols)} 个交易对，开始获取BTC数据...")

        # 获取BTC数据
        btc_kline = self.get_kline_data('BTCUSDT')
        btc_prices = self.process_kline_data(btc_kline)

        if btc_prices is None:
            print("获取BTC数据失败")
            return []

        print(f"BTC数据获取成功，共 {len(btc_prices)} 个数据点")
        print("开始分析各币种相关性...")

        results = []
        total_symbols = len(symbols)

        for i, symbol in enumerate(symbols):
            if i % 20 == 0:
                print(f"进度: {i+1}/{total_symbols} ({(i+1)/total_symbols*100:.1f}%) - 当前处理: {symbol}")

            # 获取币种数据
            kline_data = self.get_kline_data(symbol)
            price_series = self.process_kline_data(kline_data)

            if price_series is None:
                print(f"  跳过 {symbol}: 数据获取失败")
                continue

            # 计算相关性
            correlation = self.calculate_correlation(btc_prices, price_series)

            if correlation is not None:
                base_asset = symbol.replace('USDT', '')
                results.append({
                    'symbol': symbol,
                    'base_asset': base_asset,
                    'correlation': correlation,
                    'abs_correlation': abs(correlation),
                    'correlation_str': self.format_correlation(correlation),
                    'description': self.get_correlation_description(correlation),
                    'data_points': len(price_series)
                })
            else:
                print(f"  跳过 {symbol}: 相关性计算失败")

        # 按相关性绝对值从低到高排序（从不相关到相关）
        results.sort(key=lambda x: x['abs_correlation'])

        print(f"\n分析完成！成功分析了 {len(results)} 个币种")
        return results

    def display_results(self, results):
        """显示分析结果"""
        if not results:
            print("没有可显示的结果")
            return

        print(f"\n币种与BTC相关性分析结果 (基于过去24小时1分钟K线数据)")
        print("=" * 80)
        print(f"{'排名':<4} {'交易对':<15} {'基础币种':<10} {'相关系数':<10} {'相关性':<10} {'数据点数':<8}")
        print("=" * 80)

        for i, item in enumerate(results, 1):
            print(f"{i:<4} {item['symbol']:<15} {item['base_asset']:<10} "
                  f"{item['correlation_str']:<10} {item['description']:<10} {item['data_points']:<8}")

    def save_to_csv(self, results, filename="btc_correlation_analysis.csv"):
        """保存结果到CSV文件"""
        if not results:
            return

        df = pd.DataFrame(results)
        # 重新排列列的顺序
        columns_order = ['symbol', 'base_asset', 'correlation', 'abs_correlation',
                        'description', 'data_points']
        df = df[columns_order]

        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到 {filename}")

    def show_summary_stats(self, results):
        """显示统计摘要"""
        if not results:
            return

        correlations = [r['correlation'] for r in results]
        abs_correlations = [r['abs_correlation'] for r in results]

        print(f"\n统计摘要:")
        print(f"总分析币种数: {len(results)}")
        print(f"平均相关系数: {np.mean(correlations):.4f}")
        print(f"平均绝对相关系数: {np.mean(abs_correlations):.4f}")
        print(f"最低相关系数: {min(correlations):.4f}")
        print(f"最高相关系数: {max(correlations):.4f}")

        # 按相关性强度分类统计
        strong_corr = len([r for r in results if r['abs_correlation'] >= 0.8])
        medium_corr = len([r for r in results if 0.6 <= r['abs_correlation'] < 0.8])
        weak_corr = len([r for r in results if 0.3 <= r['abs_correlation'] < 0.6])
        no_corr = len([r for r in results if r['abs_correlation'] < 0.3])

        print(f"\n相关性分布:")
        print(f"强相关 (≥0.8): {strong_corr} 个")
        print(f"中等相关 (0.6-0.8): {medium_corr} 个")
        print(f"弱相关 (0.3-0.6): {weak_corr} 个")
        print(f"不相关 (<0.3): {no_corr} 个")


def main():
    analyzer = BTCCorrelationAnalyzer()

    try:
        # 分析所有有永续合约的币种与BTC的相关性
        results = analyzer.analyze_correlations()

        if results:
            analyzer.display_results(results)
            analyzer.show_summary_stats(results)
            analyzer.save_to_csv(results)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()
