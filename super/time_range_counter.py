import time
from collections import deque

class TimeRangeCounter:
    def __init__(self, window_seconds=5):
        self.timestamps = deque()
        self.window = window_seconds

    def record(self):
        now = time.time()
        self.timestamps.append(now)
        self._trim_old(now)

    def count_recent(self):
        now = time.time()
        self._trim_old(now)
        return len(self.timestamps)

    def _trim_old(self, now):
        while self.timestamps and now - self.timestamps[0] > self.window:
            self.timestamps.popleft()

if __name__ == "__main__":
    tracker = TimeRangeCounter()

    # 模拟改单
    tracker.record()
    time.sleep(1)
    tracker.record()
    time.sleep(2)
    tracker.record()
    tracker.record()
    tracker.record()
    tracker.record()

    # 查询过去 5 秒内改单次数
    print("过去5秒改单次数:", tracker.count_recent())
