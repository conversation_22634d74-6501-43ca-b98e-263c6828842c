import datetime
import math  # 求平方根

import numpy as np
import pandas as pd
import talib

from main.config import *
from main.hanshu import *

"""MA"""
def GetMa(r, len, maName):
    if len < 2:
        print("长度应当 > 1", maName, len)
        return r
        
    if maName == 'sma':
        return talib.SMA(r, len)

    if maName == 'rma':
        return RMA(r, len)

    if maName == 'ema':
        return talib.EMA(r, len)

    if maName == 'lsma':
        return talib.LINEARREG(r, timeperiod=len)


"""字典转数组"""
def GetSrc(r, name):
    if name == 'o+h+l+c':
        src = [ (v['Open'] + v['High'] + v['Low'] + v['Close']) / 4 for v in r ]

    elif name == 'h+l+c':
        src = [ (v['High'] + v['Low'] + v['Close']) / 3 for v in r ]

    elif name == 'h+l':
        src = [ (v['High'] + v['Low']) / 2 for v in r ]

    else:
        src = [ v[name] for v in r ] if name else r

    return src



"""快速MACD"""
def cal_kuai_macd(df, fast_length=12, slow_length=26, signal_length=9):
    
    df['fast_ma1'] = talib.EMA(df['Close'], fast_length)
    df['fast_ma2'] = talib.EMA(df['fast_ma1'], fast_length)
    df['fast_ma'] = df['fast_ma1'] * 2 - df['fast_ma2']

    df['slow_ma1'] = talib.EMA(df['Close'], slow_length)
    df['slow_ma2'] = talib.EMA(df['slow_ma1'], slow_length)
    df['slow_ma'] = df['slow_ma1'] * 2 - df['slow_ma2']

    df['macd'] = df['fast_ma'] - df['slow_ma']


    df['signal1'] = talib.EMA(df['macd'], signal_length)
    df['signal2'] = talib.EMA(df['signal1'], signal_length)
    df['signal'] = df['signal1'] * 2 - df['signal2']

    df['hist'] = df['macd'] - df['signal']

    return df['macd'], df['signal'], df['hist'] 


"""珊瑚趋势
sm = 平滑周期
cd = 常数D
"""

def ShanHu(r, sm, cd, name=0):
    
    src = [ v[name] for v in r ] if name else r
    emas = src.copy()  # 创造一个和cps一样大小的集合


    di = (sm - 1.0) / 2.0 + 1.0
    c1 = 2 / (di + 1.0)
    c2 = 1 - c1
    c3 = 3.0 * (cd * cd + cd * cd * cd)
    c4 = -3.0 * (2.0 * cd * cd + cd + cd * cd * cd)
    c5 = 3.0 * cd + 1.0 + cd * cd * cd + 3.0 * cd * cd

    pi1,pi2,pi3,pi4,pi5,pi6 = 0,0,0,0,0,0
    for i in range(len(src)):

        i1 = c1*src[i] + c2*pi1
        i2 = c1*i1 + c2*pi2
        i3 = c1*i2 + c2*pi3
        i4 = c1*i3 + c2*pi4
        i5 = c1*i4 + c2*pi5
        i6 = c1*i5 + c2*pi6

        pi1,pi2,pi3,pi4,pi5,pi6 = i1,i2,i3,i4,i5,i6
        bfr = -cd*cd*cd*i6 + c3*(i5) + c4*(i4) + c5*(i3)
        emas[i] = bfr

    return emas

#移动平均线
def SMA(r, days, name=0):
    cps = GetSrc(r, name)
    if days < 2:
        return cps
    emas = [0 for i in range(len(cps))]  # 创造一个和cps一样大小的集合

    for i in range(len(cps)):
        if i > days:
            ma = 0
            for i2 in range(i-days,i):
                i2 += 1
                ma += cps[i2]
            emas[i] = ma / days
    return emas

#RSI中使用的移动平均线。 它是指数加权移动平均线，alpha加权值 = 1 /长度
def RMA(r, days, name=0):
    cps = [ v[name] for v in r ] if name else r
    rmas = [0 for i in range(len(cps))]  # 创造一个和cps一样大小的集合
    alpha = 1 / days

    for i in range(len(cps)):
        if i > days-1:
            if rmas[i-1] and not np.isnan(rmas[i-1]):
                rmas[i] = alpha * cps[i] + (1 - alpha) * rmas[i-1]
            else:
                ma = 0
                for i2 in range(i-days,i):  #求平均值
                    ma += cps[i2+1]
                rmas[i] = ma / days

    return rmas
    

def ATR(r, days, ma='sma'):
    tr = [0]
    for i in range(len(r)):
        if i:
            tr.append(max(r[i]['High']-r[i]['Low'], abs(r[i]['High'] - r[i-1]['Close']), abs(r[i]['Low'] - r[i-1]['Close'])))

    if ma:
        if ma != 'rma':
            tr = np.array(tr)
        return GetMa(tr, days, ma)
    
    else:
        return tr


"""另一种ATR"""
def ATR2(r, days):
    atr = [0]
    for i in range(len(r)):
        if i:
            tr = max(r[i]['High']-r[i]['Low'], abs(r[i]['High'] - r[i-1]['Close']), abs(r[i]['Low'] - r[i-1]['Close']))
            atr.append(atr[i-1] + (tr - atr[i-1]) / days)
    return atr

def SMMA(r, days, name=0):
    cps = [ v[name] for v in r ] if name else r
    emas = [0 for i in range(len(cps))]  # 创造一个和cps一样大小的集合
    ma = 0
    for i in range(len(cps)):
        if i < days:
            ma += cps[i]
            emas[i] = 0
        
        else:
            if emas[i-1] == 0:
                emas[i] = ma / days
            else:
                emas[i] = (emas[i-1] * (days - 1) + cps[i]) / days

    return emas if emas else [0]


"""指数平滑"""
def EMA(r, days, name=0):
    cps = GetSrc(r, name)

    emas = [0 for i in range(len(cps))]  # 创造一个和cps一样大小的集合
    for i in range(len(cps)):
        if i > days:

            if not emas[i - 1] or np.isnan(emas[i - 1]):
                ma = 0
                for i2 in range(i-days,i):
                    i2 += 1
                    ma += cps[i2]
                emas[i] = ma / days

            else:
                emas[i] = ((days - 1) * emas[i - 1] + 2 * cps[i]) / (days + 1)

    return emas


"""三重指数平滑"""
def TEMA(r, days, name='Close'):
    v2 = EMA(r, days, name)
    v3 = EMA(v2, days)
    v4 = EMA(v3, days)
    
    emas = []
    for i in range(len(v2)):
        ok = 3 * (v2[i] - v3[i]) + v4[i]
        emas.append(ok)
    return emas

"""三重WMA"""
def HMA3(r, days, name=0):
    cps = [ v[name] for v in r ] if name else r
    p = days / 2
    emas = talib.WMA( talib.WMA(cps, p/3)*3 - talib.WMA(cps, p/2) - talib.WMA(cps, p) , p)
    return emas

"""HullMa"""
def HULLMA(r, sm, name=0):
    src = GetSrc(r, name)

    src = np.array(src)
    #return talib.WMA( 2*wma(src,sm/2) - wma(src,sm), round(math.sqrt(sm)))
    return talib.WMA( 2*talib.WMA(src, sm/2) - talib.WMA(src, sm), round(math.sqrt(sm)))


"""DI"""
def DI(r, days):
    df = pd.DataFrame(r)
    df['up'] = df['High'] - df['High'].shift(1)
    df['down'] = -(df['Low'] - df['Low'].shift(1))
    df['truerange'] = RMA(ATR(r, days=0, ma=0), days)
    df['plus'] = RMA([df['up'][i] if df['up'][i] > df['down'][i] and df['up'][i]> 0 else 0 for i in range(len(df['up']))], days) / df['truerange']
    df['minus'] = RMA([df['down'][i] if df['down'][i] > df['up'][i] and df['down'][i]> 0 else 0 for i in range(len(df['up']))], days) / df['truerange']

    df['plus'] = df['plus'].apply(float) * 100
    df['minus'] = df['minus'].apply(float) * 100
    return df['plus'], df['minus']

"""ADX趋向指标"""
def ADX(r, diDays, adxDays):
    df = pd.DataFrame(r)
    df['plus'], df['minus'] = DI(r, diDays)
    df['sum'] = df['plus'] + df['minus']
    df['adx'] = [abs(df['plus'][i] - df['minus'][i]) / (1 if df['sum'][i] == 0 else df['sum'][i]) for i in range(len(df['sum']))]
    df['adx'] = RMA(df['adx'].values, adxDays)
    df['adx'] = df['adx'].apply(float) * 100
    
    return df['adx']

"""vwma函数返回过去y线的x成交量加权移动均线。 它与以下相同： sma(x * volume, y) / sma(volume, y)"""
def VWMA(r, days, name="Close"):
    src = GetSrc(r, name)
    volume = GetSrc(r, 'Volume')
    src_vol = np.array(src) * np.array(volume)

    return np.array(SMA(src_vol, days)) / np.array(SMA(volume, days))

def STDEV_isZero(val, eps):
    return abs(val) <= eps

def STDEV_SUM(fst, snd):
    res = fst + snd
    if STDEV_isZero(res, 1e-10):
        res = 0
    elif STDEV_isZero(res, 1e-4):
        res = 15
    return res

def STDEV(r, days, name=0):
    src = GetSrc(r, name)
    avg = SMA(src, days)
    stdev = [0 for i in range(len(src))]
    for i in range(len(src)):
        if i > days:
            stdev[i] = STDEV_SUM(src[i], -avg[i])

            sumOfSquareDeviations = 0
            for i2 in range(days):
                i3 = i-i2
                sum = STDEV_SUM(src[i3], -avg[i])
                sumOfSquareDeviations = sumOfSquareDeviations + sum * sum
            stdev[i] = math.sqrt(sumOfSquareDeviations / days)

    return stdev

"""成交量加权平均线"""
def VWAP(r, dayTime='08:00', name='h+l+c', weekday=0):
    src = GetSrc(r, name)
    emas = [0 for i in range(len(src))]  # 创造一个和cps一样大小的集合

    srcVol,Vol = 0, 0
    for i in range(len(src)):
        #print(GetTime(r[i]['Time'], "%m-%d %H:%M"), datetime.datetime.fromtimestamp(r[0]['Time']/1000).weekday())
        if GetTime(r[i]['Time'], "%H:%M") == dayTime \
        and (not weekday or datetime.datetime.fromtimestamp(r[i]['Time']/1000).weekday() == 0):       #每天早上八点重置
            Vol = r[i]['Volume']
            srcVol = src[i] * r[i]['Volume']
        else:
            Vol += r[i]['Volume']
            srcVol += src[i] * r[i]['Volume']
        emas[i] = srcVol / Vol
    
    return emas


""" 随机指标"""
def STOCH(df, length):
    df['LowEst'] = df['Low'].rolling(length).min()     #过去最低
    df['HighEst'] = df['High'].rolling(length).max()   #过期最高
    return 100 * (df['Close'] - df['LowEst']) / (df['HighEst'] - df['LowEst'])


""" 百分比R 和KDJ相差一点 """
def STOCH_R(df, length):
    df['LowEst'] = df['Low'].rolling(length).min()     #过去最低
    df['HighEst'] = df['High'].rolling(length).max()   #过期最高
    return 100 * (df['Close'] - df['HighEst']) / (df['HighEst'] - df['LowEst'])


"""枢轴点 最高价"""
def PivotHigh(df, left, right=0):
    #right = right if right else left
    df['rolling'] = df['High'].rolling(left+right+1).max()
    df['pivot'] = 0.0
    for i in range(len(df)):
        if i >= left+right:
            # rolling = df['High'][i-right-left:i+1].values
            # m = max(rolling)
            m = df['rolling'][i]
            #print(GetTime(df['Time'][i], "%m-%d %H:%M"), df['High'][i-right], m, rolling)
            if df['High'][i-right] == m:
                df['pivot'].values[i] = m
    return df['pivot']


"""枢轴点 最低价"""
def PivotLow(df, left, right=0):
    #right = right if right else left
    df['rolling'] = df['Low'].rolling(left+right+1).min()
    df['pivot'] = 0.0
    for i in range(len(df)):
        if i >= left+right:
            #rolling = df['Low'][i-right-left:i+1].values
            m = df['rolling'][i]#min(rolling)
            if df['Low'][i-right] == m:
                df['pivot'].values[i] = m
    return df['pivot']


"""
isZero(val, eps) => abs(val) <= eps

SUM(fst, snd) =>
    EPS = 1e-10
    res = fst + snd
    if isZero(res, EPS)
        res := 0
    else
        if not isZero(res, 1e-4)
            res := res
        else
            15

pine_stdev(src, length) =>
    avg = sma(src, length)
    sumOfSquareDeviations = 0.0
    for i = 0 to length - 1
        sum = SUM(src[i], -avg)
        sumOfSquareDeviations := sumOfSquareDeviations + sum * sum

    stdev = sqrt(sumOfSquareDeviations / length)
plot(pine_stdev(close, 5))
"""

"""
    合并K线
    targetCycle = 1000 * 60 * 5 = 5m
"""
def GetNewR(sourceRecords, targetCycle):
    ret = []

    # 首先获取源K线数据的周期
    if not sourceRecords or len(sourceRecords) < 2 : 
        return None

    sourceLen = len(sourceRecords)
    #print(sourceRecords[-1])
    sourceCycle = sourceRecords[-1]["endTime"] - sourceRecords[-1]["Time"] + 1

    if targetCycle % sourceCycle != 0:
        print("targetCycle:", targetCycle)
        print("sourceCycle:", sourceCycle)
        print("%:", targetCycle % sourceCycle)
        raise "targetCycle 不是 sourceCycle 的整数倍。"

    if (1000 * 60 * 60) % targetCycle != 0 and (1000 * 60 * 60 * 24) % targetCycle != 0 : 
        print("targetCycle:", targetCycle)
        print("sourceCycle:", sourceCycle)
        print((1000 * 60 * 60) % targetCycle, (1000 * 60 * 60 * 24) % targetCycle)
        raise "targetCycle cannot complete the cycle."
    
    multiple = targetCycle / sourceCycle

    isBegin = False
    count = 0 
    barHigh = 0 
    barLow = 0 
    barOpen = 0
    barClose = 0 
    barTime = 0 
    barVol = 0 

    for i in range(sourceLen) : 
        # 获取时区偏移数值
        n = time.altzone        

        #if ((1000 * 60 * 60 * 24) - (sourceRecords[i]["Time"] * 1000) % (1000 * 60 * 60 * 24) + (n * 1000)) % targetCycle == 0 :
        isBegin = True

        if isBegin : 
            if count == 0 : 
                barHigh = sourceRecords[i]["High"]
                barLow = sourceRecords[i]["Low"]
                barOpen = sourceRecords[i]["Open"]
                barClose = sourceRecords[i]["Close"]
                barTime = sourceRecords[i]["Time"]
                barVol = sourceRecords[i]["Volume"]
                count += 1
            elif count < multiple : 
                barHigh = max(barHigh, sourceRecords[i]["High"])
                barLow = min(barLow, sourceRecords[i]["Low"])
                barClose = sourceRecords[i]["Close"]
                barVol += sourceRecords[i]["Volume"]
                count += 1

            if count == multiple or i == sourceLen - 1 :
                ret.append({
                    "High" : barHigh,
                    "Low" : barLow,
                    "Open" : barOpen,
                    "Close" : barClose,
                    "Time" : barTime,
                    "Volume" : barVol,
                })
                count = 0

    return ret
