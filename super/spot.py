from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from moneyV2.symbol import *

import random

def DeleteOrder(self, symbol, msg=''):
    if self.preJia:
        self.bnb.DeleteAllOrder(symbol, msg=msg)
        self.preJia = 0
        self.preJia2 = 0
        self.orderJia = 0
        self.takerCount = 0
        self.orderId = []



""" 撤销第一个挂单"""
def delPreOrder(self, symbol, chu):
    if self.takerCount >= chu and chu > 1:
        try:
            log('----撤销首次挂单', self.orderId[0])
            self.bnb.DeleteOrder(symbol, self.orderId[0])
            del self.orderId[0]
        except:
            pass

""" 所有币都还款"""
def <PERSON><PERSON><PERSON>uan(self):
    jiekuan = self.bnb.GetYuer(p=0, jiekaun=1)
    for jie in jiekuan:
        pos  = GetPos(jie[0]+'USDT', self.ws.pos, 'SELL')
        if pos:
            jie[1] -= pos['liang']
            jie[1] = <PERSON>(jie[1], 6)
        if jie[1]:
            self.bnb.<PERSON><PERSON>(jie[1], jie[0])


""" 现货买入卖出"""
def Spot_Maker(self, symbol, data, isbnb=0):


    symbol2 = symbol.replace('USDT', '')
    J, J2, L, Max, Min, MZ         = self.symbol['J'], self.symbol['J2'], self.symbol['L'], self.symbol['M']*0.99, self.symbol['Min'], self.symbol['Z']

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])
    mid = (bid + ask) * 0.5

    chu = 2           #分几轮

    """
    如果一个Tick低于万2，那么价格掉到第4个Tick再挂买单
    """
    updateTickCount = 1# if J2/mid > 0.00019 else 2

    """ 查询杠杆和借币状态"""
    posAll = self.ws.pos.copy()
    pos  = GetNewPos(posAll, Exchange, symbol, '', bid, MZ, L)

    """ 查看挂单状态"""
    usdt = self.usdt
    if self.orderId:
        for orderId in self.orderId:
            if orderId in self.ws.orders:
                order = self.ws.orders[orderId]

                #订单已经成交
                if order['status'] in ['FILLED', 'EXPIRED']:
                    log('----删除已成交订单', orderId)
                    for i in range(0, len(self.orderId)):
                        if self.orderId[i] == orderId:
                            del self.orderId[i]
                            break

                    if not self.orderId:
                        DeleteOrder(self, symbol)

                if order['status'] in ['PARTIALLY_FILLED']:
                    posAll = changePos(self.ws, symbol, order['side'], order['jiage'], order['liang'], ret=1)


    pos  = GetNewPos(posAll, Exchange, symbol, '', bid, MZ, L)


    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, self.kejie)

    if tlog('重置何用余额...', 0, 3*60) or not self.usdt:
        DeleteOrder(self, symbol)
        if not self.usdt:
            HuanKuan(self)
            time.sleep(1)

        self.usdt = self.bnb.GetYuer(p=0, bi=symbol)['keyong'] + self.bnb.GetKejie() * 0.9

        print('调试', self.bnb.GetYuer(p=0, bi=symbol)['keyong'], self.bnb.GetKejie(), pos)

        self.kejie = self.bnb.GetKejie(symbol2)*0.95

        """ 增加已经借了的金额"""
        jiekuan = self.bnb.GetYuer(p=0, jiekaun=1)
        for jie in jiekuan:
            if jie[0]+'USDT' == symbol:
                pos  = GetPos(jie[0]+'USDT', self.ws.pos, 'SELL')
                if pos:
                    print(pos)
                    jie[1] -= pos['liang']
                    jie[1] = Si(jie[1], L)
                self.kejie += jie[1] * 0.999
                self.kejie = Si(self.kejie, L)

        """ 尝试借币 尝试出可以下单"""
        # if kejie:
        #     if self.bnb.JieKuan(symbol2, N(kejie, L)): #尝试借100%
        #         self.kejie = kejie

        #     else:
        #         kejie2 = 0
        #         for i in range(10):  #首次尝试借30% 每次尝试借10%
        #             changshi = N(kejie*0.1, L) if i else N(kejie*0.3, L)
        #             print('')
        #             log(f'第 {i+1} 次尝试借币 {symbol2} 本次金额', U(changshi*bid), '总', U(kejie*bid))
        #             if self.bnb.JieKuan(symbol2, changshi):
        #                 kejie2 += changshi
        #             else:
        #                 break
        #         self.kejie = kejie2

        if self.kejie > Max:
            self.kejie = Max

        log('可以借U', U(self.usdt), '可以借币', self.kejie, U(self.kejie*bid))


    """ 持仓价值"""
    usdt = self.usdt
    jiazhi = 0
    if pos:
        jiazhi = round(pos['liang']*ask, 2)
        usdt -= jiazhi
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1)], 3)
            self.prePos = pos['liang']

    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    # usdt = 50000
    if 'open' in self.side:

        side = 'BUY' if 'Buy' in self.side else 'SELL'
        if jiazhi >= self.pos_limit and pos['side'] == side:
            DeleteOrder(self, symbol)
            tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(usdt), '目标', U(self.pos_limit)], 3)
            return

        if self.stopOpen:
            return tlog('操控趋势已经完成', ['停机'], 60)

        """ 重置"""
        if self.preSide and self.preSide != side:
            DeleteOrder(self, symbol)

        if self.orderJia == ask or self.orderJia == bid:
            DeleteOrder(self, symbol)
            return tlog('差点成交了!!!!!', '', 3)

        if not tlog('差点成交了!!!!!', '', 3, xs=0, look=1):
            return 0

        """ 监控价格是否超过阈值"""
        if self.preJia:

            bili = (abs(mid-self.startJia)) / mid * 100
            # if bili >= self.bili:
            #     DeleteOrder(self, symbol)
            #     log('超过阈值！！！！！！！！！！！！！！！ 停机', StrBi(bili/100))
            #     self.stopOpen = 1
            #     return

            if mid != self.preJia2:
                log('趋势操控中', '--->', StrBi(bili/100))
                self.preJia2 = mid

        if side == 'BUY':
            if not self.preJia or bid > self.preJia + J2*updateTickCount:

                """ 撤销第一个挂单"""
                delPreOrder(self, symbol, chu)
                print('下单前余额：', usdt)
                liang = N(usdt * 0.95 / bid, L)
                if liang > Max:
                    liang = N(Max, L)
                    log('超过最大下单量', Max, U(Max*bid))

                if liang*mid > 15:
                    print('')
                    log(f'第 {self.takerCount+1} 次下单 本次下单', U(liang*bid), '每个Tick', StrBi(J2/mid, 4))
                    order_id, msg = self.bnb.PostOrder(symbol, 'BUY', N(bid-J2, J), liang, 'post_only')
                    log("现货挂单成功: ", order_id)
                    if order_id:
                        self.preJia = bid
                        self.takerCount += 1
                        self.preSide = 'BUY'
                        self.orderId.append(order_id)
                        self.orderJia = N(bid-J2, J)
                        if not self.startJia:
                            self.preJia2 = mid
                            self.startJia = mid
                    else:
                        DeleteOrder(self, symbol)
                        uploadLog(isExit=1)
                        # time.sleep(1)

                else:
                    tlog('U不足...', U(usdt), 5*60)


        else:
            if not self.preJia or ask < self.preJia - J2*updateTickCount:

                """ 撤销第一个挂单"""
                delPreOrder(self, symbol, chu)

                liang = self.kejie
                if pos and pos['side'] == 'BUY':
                    liang += pos['liang']*0.999
                liang = N(liang / chu, L)
                if liang > Max:
                    liang = N(Max, L)
                    log('超过最大下单量', Max, U(Max*ask))

                if liang*mid > 15:
                    print('')
                    log(f'第 {self.takerCount+1} 次下单 本次下单', U(liang*ask), '每个Tick', StrBi(J2/mid, 4))
                    order_id, msg = self.bnb.PostOrder(symbol, 'SELL', N(ask+J2, J), liang, 'post_only')
                    if order_id:
                        self.preJia = ask
                        self.takerCount += 1
                        self.preSide = 'SELL'
                        self.orderId.append(order_id)
                        self.orderJia = N(ask+J2, J)
                        if not self.startJia:
                            self.preJia2 = mid
                            self.startJia = mid
                    else:
                        DeleteOrder(self, symbol)
                        uploadLog(isExit=1)
                        # time.sleep(1)

                else:
                    tlog('货不足...', U(usdt), 5*60)

        duishou = askQty * ask if 'Buy' in self.side else bidQty * bid
        isFuck = duishou < 600 and NowTime_ms() - self.preTime > 200

        if isFuck or NowTime_ms() >= self.SujiTime:
            if isFuck:
                liang = duishou*1.1
            else:
                liang = random.randint(50, 500)

            liang = max(liang, 20)
            print('')
            print('干死你！！！！！ 对手', N(mid, J), U(duishou), '下单量', U(liang), '持仓', U(jiazhi, 0))
            print('')

            order_id, msg = self.bnb.PostOrder(symbol, side, bid, N(liang / bid, L), 'normal')
            if order_id:
                self.orderId.append(order_id)
                if side == 'BUY':
                    self.usdt -= liang
                else:
                    self.kejie -= liang
            else:
                os._exit(0)

            waitTime = random.randint(4800, 10000)
            self.SujiTime = NowTime_ms() + waitTime
            self.preTime = NowTime_ms()

    elif 'stop' == self.side:
        DeleteOrder(self, symbol)
        if tlog(f'{Name} 策略已暂停...', '', 5*60):
            HuanKuan(self)
            time.sleep(0.5)
            self.usdt = 0

    else:

        """ 清理所有仓位"""
        if pos and pos['liang'] * bid > 11:

            if 'closeAll' == self.side:
                """ 一次性"""
                # 撤销上次挂单
                DeleteOrder(self, symbol)

                x = 0.0015
                jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                jiage = N(jiage, J)
                jiage2 = bid  if pos['side'] == 'BUY' else ask

                log(pos['side'], jiazhi, '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                liang = pos['liang']
                if liang * jiage > 200000:
                    liang = N(200000 / jiage, L)

                self.bnb.PostOrder(symbol, pos['side2'], jiage, liang, 'limit', 1)
                self.preJia = bid

                Sleep(5000)
                upPos(self, self.bnb, self.ws, set=1)

            else:
                #最大砸盘金额
                maxTaker = 10000

                def limitSell():

                    """ 挂卖一"""
                    if jiazhi > maxTaker+100:
                        limitJiazhi = min(jiazhi-maxTaker, random.randint(10000, 20000)) #每次挂单不超过1wu
                        liang = N(limitJiazhi / bid * 0.99, L)
                        if liang * bid > 100:
                            if liang > Max:
                                liang = N(Max, L)
                                log('超过最大下单量', Max, U(Max*bid))

                            DeleteOrder(self, symbol)
                            order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], ask, liang, 'post_only')
                            if order_id:
                                self.preJia = bid

                                Sleep(random.randint(800, 2200))
                                DeleteOrder(self, symbol)

                """ 每砸盘3次，触发一次挂单"""
                if self.takerCount % 10 == 0 and tlog('挂卖一', '', 30) :
                    t = threading.Thread(target=limitSell)
                    t.setDaemon(True)
                    t.start()


                duishou = bidQty * bid
                isFuck = duishou < 300
                """ 缓慢砸盘"""
                if NowTime_ms() >= self.SujiTime or isFuck:
                    liang = random.randint(600, 1500)
                    duishou = bidQty * bid  if pos['side'] == 'BUY' else askQty * ask

                    """ 每6次吃一次对手盘"""
                    if self.takerCount and self.takerCount % 6 == 0:
                        liang = max(duishou*1.3, random.randint(1900, 3000))
                        liang = min(liang, maxTaker)

                    print(f'--------第 {self.takerCount} 次--------')
                    print('对手', U(duishou),
                        ' ',
                        '持仓', U(jiazhi),
                            '-------> 本次砸盘', U(min(jiazhi, liang)),'\n')

                    liang = N(liang / bid, L)
                    liang = min(liang, pos['liang'])

                    DeleteOrder(self, symbol)
                    order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], bid, liang, 'normal')
                    if order_id:
                        self.takerCount += 1

                    waitTime = random.randint(1500, 4800)
                    self.SujiTime = NowTime_ms() + waitTime
                    print('等待', round(waitTime / 1000, 1), 's\n')

                    # Sleep(500)
                    # upPos(self, set=1)


        else:
            if tlog('平仓完毕', pos, 60):
                delPos(self, symbol, 'BUY')
                delPos(self, symbol, 'SELL')
                time.sleep(5)
                upPos(self, self.bnb, self.ws, set=1)




""" 现货Taker买入卖出"""
def Spot_Taker(self, symbol, data, isbnb=0):

    J, L, Max, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['M']*0.99, self.symbol['Min'], self.symbol['Z']

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])

    pankou = [
        symbol, '延迟', NowTime_ms()-data['t'], '',
        'bid', str(bid)+' ('+str(bidQty)+')'+' ('+U(bidQty*bid*MZ)+')', 'ask', str(ask)+' ('+str(askQty)+')'+' ('+U(askQty*ask*MZ)+')'
    ]

    """ 持仓价值"""
    jiazhi = 0
    posAll = self.ws.pos.copy()
    pos  = GetNewPos(posAll, Exchange, symbol, '', bid, MZ, L)
    if pos:
        jiazhi = round(pos['liang']*ask, 2)
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1)], 3)
            self.prePos = pos['liang']

    if not self.usdt:
        DeleteOrder(self, symbol)
        # HuanKuan(self)

        self.usdt = self.bnb.GetYuer(p=0, bi=symbol)['keyong'] + self.bnb.GetKejie()
        if pos:
            self.usdt += pos['liang'] * bid
            usdt = self.usdt


        """ 尝试借币"""
        # if kejie:
        #     if self.bnb.JieKuan(symbol2, N(kejie, L)): #尝试借100%
        #         self.kejie = kejie

        #     else:
        #         kejie2 = 0
        #         for i in range(10):  #首次尝试借30% 每次尝试借10%
        #             changshi = N(kejie*0.1, L) if i else N(kejie*0.3, L)
        #             print('')
        #             log(f'第 {i+1} 次尝试借币 {symbol2} 本次金额', U(changshi*bid), '总', U(kejie*bid))
        #             if self.bnb.JieKuan(symbol2, changshi):
        #                 kejie2 += changshi
        #             else:
        #                 break
        #         self.kejie = kejie2

        if symbol == 'THETAUSDT':
            self.usdt = 90000
            self.kejie = 90000 / mid

        log('可以借U', U(self.usdt), '可以借币', self.kejie, U(self.kejie*bid))

    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, 0)

    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        if tlog(f'{Name} 策略已暂停...', '', 60):
            jiekuan = self.bnb.GetYuer(p=0, jiekaun=1)
            for jie in jiekuan:
                self.bnb.HuanKuan(jie[1], jie[0])

    elif 'open' in self.side:

        """ 推动上涨"""

        if not self.usdt:
            return tlog('没U', [self.bnb.GetYuer(p=0)], 3)

        if self.stopOpen:
            DeleteOrder(self, symbol)
            return tlog('任务已完成 等待指令', '', 30)

        taker = min(self.usdt * 0.9, self.pos_limit)
        limit = (self.usdt - taker) * 0.1

        if jiazhi >= taker-11:
            DeleteOrder(self, symbol)
            tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(self.usdt), '目标', U(taker)], 3)
            self.stopOpen = 1
            return

        # """ 挂买1"""
        # if 'Buy' in self.side and bid != self.preJia:

        #     liang = N(limit / bid, L)
        #     if liang * bid > 100:
        #         if liang > Max:
        #             liang = N(Max, L)
        #             log('超过最大下单量', Max, U(Max*bid))

        #         DeleteOrder(self, symbol)
        #         order_id, msg = self.bnb.PostOrder(symbol, 'BUY', bid, liang, 'post_only')
        #         if order_id:
        #             self.preJia = bid


        duishou = askQty * ask if 'Buy' in self.side else bidQty * bid
        """ TESTETST"""
        isFuck = duishou < 400 and NowTime_ms() - self.preTime > 50

        """ 不断下单推动"""
        if NowTime_ms() >= self.SujiTime or isFuck:


            side = 'BUY' if 'Buy' in self.side else 'SELL'
            waitTime = 0
            if bid > self.preTakerJia:
                self.preTakerJia = bid
                self.takerCount = 0

            if isFuck:
                liang = duishou*1.05

            else:
                #等待 1-4s
                waitTime = random.randint(800, 4800)

                """ 连续五次下单未上涨，大额吃单"""
                if self.takerCount > 6:
                    liang = min(duishou*1.5, random.randint(3000, 11000))
                    self.takerCount = 0
                else:
                    liang = random.randint(400, 800)


            shengyu = round(taker - jiazhi, 1)
            liang = min(liang, shengyu)

            print(f'\n--------第 {self.takerCount} 次--------')
            print('对手', U(duishou),
                  ' ',
                  '目标', U(taker), '持仓', U(jiazhi), '剩余', U(shengyu),
                    '-------> 本次下单', U(liang),
                    '\n等待', round(waitTime / 1000, 1), 's\n')

            DeleteOrder(self, symbol)
            order_id, msg = self.bnb.PostOrder(symbol, side, bid, N(liang / bid, L), 'normal')
            if order_id:
                self.takerCount += 1
                self.SujiTime = NowTime_ms() + waitTime
                self.preTime = NowTime_ms()
                print()

            # Sleep(100)
            # upPos(self, set=1)

            if order_id:
                for i in range(1, 200): #2s
                    if order_id in self.ws.orders:
                        order = self.ws.orders[order_id]
                        if order['status'] in ['FILLED', 'EXPIRED']:
                            addPos(self, symbol, order['jiage'], order['liang'], J, side)
                            break
                    time.sleep(0.01)

    else:

        """ 清理所有仓位"""
        if pos and pos['liang'] * bid > 11:

            if 'closeAll' == self.side:
                """ 一次性"""
                # 撤销上次挂单
                DeleteOrder(self, symbol)

                x = 0.0015
                jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                jiage = N(jiage, J)
                jiage2 = bid  if pos['side'] == 'BUY' else ask

                log(pos['side'], jiazhi, '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'limit', 1)
                self.preJia = bid

                Sleep(5000)
                upPos(self, set=1)

            else:
                #最大砸盘金额
                maxTaker = 10000

                def limitSell():

                    """ 挂卖一"""
                    if jiazhi > maxTaker+100:
                        limitJiazhi = min(jiazhi-maxTaker, random.randint(10000, 20000)) #每次挂单不超过1wu
                        liang = N(limitJiazhi / bid * 0.99, L)
                        if liang * bid > 100:
                            if liang > Max:
                                liang = N(Max, L)
                                log('超过最大下单量', Max, U(Max*bid))

                            DeleteOrder(self, symbol)
                            order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], ask, liang, 'post_only')
                            if order_id:
                                self.preJia = bid

                                Sleep(random.randint(800, 2200))
                                DeleteOrder(self, symbol)

                """ 每砸盘3次，触发一次挂单"""
                if self.takerCount % 10 == 0 and tlog('挂卖一', '', 30) :
                    t = threading.Thread(target=limitSell)
                    t.setDaemon(True)
                    t.start()


                duishou = bidQty * bid
                """ TESTETST"""
                isFuck = duishou < 300 and NowTime_ms() - self.preTime > 100
                """ 缓慢砸盘"""
                if NowTime_ms() >= self.SujiTime or isFuck:
                    liang = random.randint(600, 1500)
                    duishou = bidQty * bid  if pos['side'] == 'BUY' else askQty * ask

                    """ 每6次吃一次对手盘"""
                    if self.takerCount and self.takerCount % 6 == 0:
                        liang = max(duishou*1.3, random.randint(1900, 3000))
                        liang = min(liang, maxTaker)

                    print(f'--------第 {self.takerCount} 次--------')
                    print('对手', U(duishou),
                        ' ',
                        '持仓', U(jiazhi),
                            '-------> 本次砸盘', U(min(jiazhi, liang)),'\n')

                    liang = N(liang / bid, L)
                    liang = min(liang, pos['liang'])

                    DeleteOrder(self, symbol)
                    order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], bid, liang, 'normal')
                    if order_id:
                        self.takerCount += 1

                    waitTime = random.randint(1500, 4800)
                    self.SujiTime = NowTime_ms() + waitTime
                    self.preTime = NowTime_ms()
                    print('等待', round(waitTime / 1000, 1), 's\n')

                    # Sleep(500)
                    # upPos(self, set=1)


        else:
            if tlog('平仓完毕', pos, 60):
                delPos(self, symbol, 'BUY')
                delPos(self, symbol, 'SELL')
                time.sleep(5)
                upPos(self, set=1)


def Spot(self, symbol, data, isbnb=0):

    Spot_Maker(self, symbol, data, isbnb)
#
    # Spot_Taker(self, symbol, data, isbnb)
