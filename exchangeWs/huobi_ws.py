from os import times
import aiohttp
import time
import asyncio
import zlib
import json, ujson
import zlib
import hmac
import traceback
import random
import gzip, sys
from main.config import *
from main.hanshu import *

from datetime import datetime
from urllib import parse
import hmac
import base64
from hashlib import sha256


from moneyV2.money import changePos

class huobi_ws:

    def __init__(self, coinex, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
    
        self.coinex = coinex
        self.access_id = coinex.access_id
        self.secret_key = coinex.secret_key

        all = self.coinex.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新余额"""
    def _update_account(self, msg):
        for i in msg['data']:
            if i['margin_asset'] == "USDT":
                
                self.usdt = N(float(i['margin_balance']), 4)
                self.keyong = N(float(i['withdraw_available']), 4)
                self.yingkui = N(float(i['profit_unreal']), 4)

                self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.usdt != self.preUsdt:
                    print('[火币余额更新]', self.usdt, '可用', self.keyong, '盈亏', self.yingkui)

                self.preUsdt = self.usdt

                
    
    """ 更新订单"""
    def _update_order(self, data2):
        try:
            side = data2['direction'].upper()
            status = {'1': '准备提交', '2': '准备提交', '3': '已提交', '4': '部分成交', '5': '部分成交已撤', '6': '全部成交', '7': '已撤单'}
            self.orders[data2['order_id']] = {
                'symbol': data2['contract_code'],              #交易对
                'status': data2['status'],               #状态
                'side': side,          #方向
                'type': data2['order_price_type'],               #订单类型
                'jiage': float(data2['trade_avg_price']),              #累积成交均价
                'liang': float(data2['trade_volume']),   #累积成交量
                'yingkui': float(data2['profit']),         #盈亏
                'time': NowTime(),                 #推送时间
            }
            #is_reduce_only

            # pprint(self.orders[data2['id']])
            close = '减仓' if data2['reduce_only'] else '开仓'
            bi = StrBi(data2['fee']/data2['trade_turnover']*-1) if data2['trade_turnover'] else '0%'
            msg = ['[火币监听订单]', GetTime(echoMs=1), '', close, side, data2['contract_code'], data2['order_price_type'],
            "　ID", data2['order_id'], status[str(data2['status'])],
            '　价格', data2['price'], '数量', data2['volume'],
            '　成交价', data2['trade_avg_price'], '成交量', str(float(data2['trade_volume'])) + ' ('+str(data2['trade_turnover'])+'$)'
            '　盈亏', data2['profit'],
            '　手续', STR_N(data2['fee'])+' ('+bi+')']

            if float(data2['trade_volume']):
                log(*msg)

                # 不准
                # if data2['status'] in self.order_status:
                #     changePos(self, data2['contract_code'], side, data2['trade_avg_price'], data2['trade_volume'], data2['reduce_only'])

            else:
                print(*msg)


            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])


        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新盘口和费率信息"""
    def _update_depth(self, v):
        s = v['ch'].replace('market.', '').replace('.bbo', '')
        
        if 'bid' in v['tick'] and v['tick']['bid'] and 'ask' in v['tick'] and v['tick']['ask']:
            if s not in self.data:
                self.data[s] = {'feilv': self.coinex.GetSymbolFeilv(s)}
                
            self.data[s]['bidPx']  = v['tick']['bid'][0]
            self.data[s]['bidQty'] = v['tick']['bid'][1]
            self.data[s]['askPx']  = v['tick']['ask'][0]
            self.data[s]['askQty'] = v['tick']['ask'][1]
            self.data[s]['t']      = NowTime_ms()

            if self.depathCallback:
                self.depathCallback(s, self.data[s])


    """ 更新费率"""
    def _update_feilv(self, datas):
        for data in datas:
            s = data['contract_code']
            if s not in self.data:
                self.data[s] = {}

            self.data[s]['feilv'] = float(data['funding_rate']) * 100
            
    """ 更新持仓"""
    def _update_pos(self, data):

        for v in data['data']:
            symbol = v['contract_code']
            side =  v['direction'].upper()

            ok = {}
            ok['symbol'] = symbol
            ok['liang'] = float(v['volume'])
            ok['side'] = side #持仓方向
            ok['side2'] = fanSide(ok['side'])
            ok['jiage'] = float(v['cost_open'])
            ok['nowJiage'] = float(v['last_price'])    #最新成交价格
            ok['yingkui'] = N(float(v['profit_unreal']), 4)
            ok['bzj'] = N(float(v['position_margin']), 4)
            ok['roe'] = float(v['profit_rate'])

            for i in range(0, len(self.pos)):
                if self.pos[i]['symbol'] == symbol and self.pos[i]['side'] == side:
                    if ok['liang']:
                        self.pos[i] = ok
                    else:
                        del self.pos[i]
                    # print(symbol, '更新仓位', ok)
                    break
            else:
                if ok['liang']:
                    # print(symbol, '新仓位', ok)
                    self.pos.append(ok)


    async def ping(self, conn):
        while True:
            await conn.send_str('{"ponmg": '+str(NowTime())+'}')
            #返回 {"op":"error","ts":1682821260671}
            await asyncio.sleep(5)

    async def main(self, symbols=[]):
        while True:
            try:
                # 尝试连接
                # log(f'火币 尝试连接ws', symbols)

                if symbols:
                    url = 'wss://api.hbdm.com/linear-swap-ws'
                else:

                    host = "api.hbdm.com"   #这个最快 2ms
                    method = 'get'
                    path = "/linear-swap-notification"
                    url = 'wss://{}{}'.format(host, path)

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            # proxy=self.proxy,
                            timeout=30,
                            receive_timeout=30,
                            ) as _ws:
                    # print(f'火币 ws 连接成功', symbols)


                    if symbols:
                        for symbol in symbols:
                            await _ws.send_str(ujson.dumps({"id":"id8","sub": f"market.{symbol}.bbo"}))
                            await _ws.send_str(ujson.dumps({"id":"id8","sub": f"market.{symbol}.bbo"}))

                    else:
                        # 订阅
                        # from datetime import timezone   .replace(tzinfo=timezone.utc)
                        timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S")

                        # get Signature
                        suffix = 'AccessKeyId={}&SignatureMethod=HmacSHA256&SignatureVersion=2&Timestamp={}'.format(
                            self.access_id, parse.quote(timestamp))
                        payload = '{}\n{}\n{}\n{}'.format(method.upper(), host, path, suffix)

                        digest = hmac.new(self.secret_key.encode('utf8'), payload.encode(
                            'utf8'), digestmod=sha256).digest()
                        signature = base64.b64encode(digest).decode()

                        # data
                        data = {
                            "op": "auth",
                            "type": "api",
                            "AccessKeyId": self.access_id,
                            "SignatureMethod": "HmacSHA256",
                            "SignatureVersion": "2",
                            "Timestamp": timestamp,
                            "Signature": signature
                        }
                        data = ujson.dumps(data)

                        await _ws.send_str(data)

                        msg = await _ws.receive(timeout=30)
                        msg = ujson.loads(gzip.decompress(msg.data).decode())
                        log('火币WS 登录', msg)

                        await _ws.send_str(ujson.dumps({"op":"sub","topic": f"accounts_cross.USDT"}))
                        await _ws.send_str(ujson.dumps({"op":"sub","topic": "orders_cross.*"}))
                        await _ws.send_str(ujson.dumps({"op":"sub","topic": "positions_cross.*"}))
                        await _ws.send_str(ujson.dumps({"op":"sub","topic": "public.*.funding_rate"}))

                        asyncio.create_task(self.ping(_ws))

                    while True:
                        try:
                            msg = await _ws.receive(timeout=30)
                        except:
                            print(f'火币 ws 长时间没有收到消息 准备重连...', symbols)
                            break
                        
                        try:
                            msg = gzip.decompress(msg.data).decode()
                        except:
                            log('Huobi Ws', symbols, 'Error', Color('', -1))
                            break
                        
                        if 'ping' in msg:
                            await _ws.send_str(msg.replace('ping', 'pong'))
                        
                        else:
                            try:
                                msg = ujson.loads(msg)
                            except:
                                break

                            # 处理消息
                            if 'topic' in msg:
                                if 'funding_rate' in msg['topic'] and 'data' in msg: self._update_feilv(msg['data'])
                                elif 'uid' in msg:
                                    if 'accounts_cross' in msg['topic']: self._update_account(msg)
                                    elif 'orders_cross' in msg['topic']: self._update_order(msg)
                                    elif 'positions_cross' in msg['topic']: self._update_pos(msg)

                            elif 'ch' in msg:
                                self._update_depth(msg)



            except:
                print(traceback.format_exc())
                log(f'火币 ws连接失败 开始重连...', symbols)

