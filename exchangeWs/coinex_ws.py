#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import ssl
import certifi
import json
import ujson
import logging
import time
import hashlib
import traceback
from main.config import *
from main.hanshu import *

from moneyV2.money import changePos

WS_URL = "wss://perpetual.coinex.com/"



class coinex_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.load_verify_locations(certifi.where())

        all = self.gate.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0

        self.pos = []
        self.orders = {}
        self.data = {}


    """ 更新订单"""
    def _update_order(self, data):
        try:
            data = ujson.loads(data)


            status = data['params'][0]
            if status == 1:
                status = 'Put'
            elif status == 2:
                status = 'Update'
            elif status == 3:
                status = 'Finish'
            

            data = data['params'][1]

            symbol = data['market']
            side = 'SELL' if data['side'] == 1 else 'BUY'
            orderType = data['type']
            if orderType == 1:
                orderType = 'Limit'
            if orderType == 2:
                orderType = 'Marker'

            liang = float(D(data["amount"]) - D(data["left"]))
            self.orders[data['order_id']] = {
                'symbol': symbol,                #交易对
                'status': 'ok',            #状态
                'jiage': float(data['last_deal_price']),      #价格
                'liang': liang,      #数量
                'side': side,              #方向
                'yingkui': float(data['deal_profit']),          #盈亏
                'time': NowTime(),              #推送时间
            }

            msg = ['[Coinex监听订单]', GetTime(echoMs=1), '', side, symbol, status, "　ID", data['order_id'], orderType,
            '　价格', data['price'],'数量', data['amount'],
            '　成交价', float(data['last_deal_price']), '成交量', liang,
            '　盈亏', float(data['deal_profit']),'　手续费', float(data['deal_fee']),
            '　费率',  str(data['maker_fee'])+'/'+str(data['taker_fee']),
            ]

            if float(data['last_deal_price']):
                log(*msg)
                #1: 开仓 2: 加仓 3: 减仓 4: 平仓 5: 减仓 8: 减仓(系统强平) 9: 减仓(自动减仓) 10: 减仓(止盈) 11: 减仓(止损) 12: 平仓(系统强平) 13: 平仓(自动减仓) 14: 平仓(止盈) 15: 平仓(止损)
                # changePos(self, symbol, side, data['last_deal_price'], liang, data['last_deal_type'] in [3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15])
            else:
                print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新余额"""
    def _update_account(self, msg):
        msg = ujson.loads(msg)
        for i in msg['params'][0]:
            if CcyBi == i:
                self.usdt = float(msg['params'][0][CcyBi]['available'])+float(msg['params'][0][CcyBi]['frozen'])+\
                    float(msg['params'][0][CcyBi]['margin'])+float(msg['params'][0][CcyBi]['profit_unreal'])
                self.usdt = round(self.usdt, 4)
                self.keyong = round(float(msg['params'][0][CcyBi]['available']), 4)
                self.yingkui = round(float(msg['params'][0][CcyBi]['profit_unreal']), 4)

                self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.usdt != self.preUsdt:
                    print('[Coinex余额更新]', self.usdt, '可用', self.keyong, '盈亏', self.yingkui)

                self.preUsdt = self.usdt




    async def auth(self, conn):
        current_time = int(time.time()*1000)
        sign_str = f"access_id={self.access_id}&timestamp={current_time}&secret_key={self.secret_key}"
        md5 = hashlib.sha256(sign_str.encode())
        param = {
            "id": 1,
            "method": "server.sign",
            "params": [self.access_id, md5.hexdigest().lower(), current_time]
        }
        await conn.send_str(ujson.dumps(param))

        res = await conn.receive(timeout=30)
        log('coinex ws 连接', json.loads(res.data))


    """ 更新盘口"""
    def _update_depth(self, data):
        data = ujson.loads(data)
        v = data['params']
        s = v['market']

        if s not in self.data:
            self.data[s] = {'feilv': 0}
        self.data[s]['bidPx']  = float(v['bid_price'])
        self.data[s]['askPx']  = float(v['ask_price'])
        self.data[s]['bidQty'] = float(v['bid_amount'])
        self.data[s]['askQty'] = float(v['ask_amount'])
        self.data[s]['t']      = NowTime_ms()
        if self.depathCallback:
            self.depathCallback(s, self.data[s])


    """ 更新费率"""
    def _update_feilv(self, data):
        data = ujson.loads(data)['params'][0]
        for s in data:
            if s not in self.data:
                self.data[s] = {}
            self.data[s]['feilv'] = float(data[s]['funding_rate_next']) * 100

    """ 更新仓位，order没有仓位id"""
    def _update_pos(self, data):
        data = ujson.loads(data)
        v = data['params'][1]
        
        symbol = v['market']
        side = 'BUY' if v['side'] == 2 else 'SELL'
        
        ok = {}
        ok['symbol'] = symbol
        ok['side'] = side #持仓方向
        ok['side2'] = fanSide(ok['side'])
        ok['jiage'] = float(v['open_price'])
        ok['liang'] = float(v['amount'])
        ok['yingkui'] = float(v['profit_unreal'])
        ok['position_id'] = v['position_id']

        for i in range(0, len(self.pos)):
            if self.pos[i]['symbol'] == symbol and self.pos[i]['side'] == side:
                if ok['liang']:
                    self.pos[i] = ok
                else:
                    del self.pos[i]
                break
        else:
            if ok['liang']:
                self.pos.append(ok)

        if not float(v['amount']):
            return


    async def ping(self, conn):
        sub_str = ujson.dumps({"id": 1, "method": "server.ping","params": []})
        while True:
            await conn.send_str(sub_str)
            await asyncio.sleep(15)

    async def main(self, symbols=[]):
        while True:
            # log("Coinex 准备连接ws", symbols)
            try:

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            WS_URL,
                            proxy=None,
                            timeout=30,
                            # receive_timeout=30,
                ) as conn:

                    if symbols:
                        for symbol in symbols:
                            await conn.send_str(json.dumps({"id": 1, "method": "bbo.subscribe","params": [symbol]}))
                            await conn.send_str(json.dumps({"id": 1, "method": "state.subscribe","params": [symbol]}))
                            # res = await conn.receive(timeout=30)
                            # log(symbol,' 订阅', json.loads(res.data))


                    else:
                        # auth
                        await self.auth(conn)

                        # 订阅资产
                        await conn.send_str(json.dumps({"id": 1, "method": "asset.subscribe","params": [CcyBi]}))

                        # 订阅订单
                        await conn.send_str(json.dumps({"id": 1, "method": "order.subscribe","params":  []}))

                        # 订阅仓位
                        await conn.send_str(json.dumps({"id": 1, "method": "position.subscribe","params": []}))

                    # note: important to keepalive 
                    asyncio.create_task(self.ping(conn))

                    # loop to process update data
                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive() #timeout=30
                        except asyncio.CancelledError:
                            log('ws取消')
                            return
                        except asyncio.TimeoutError:
                            log(f'coinex ws长时间没有收到消息 准备重连...')
                            break
                        except:
                            traceback.format_exc()
                            log(f'coinex ws出现错误 准备重连...')
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        if not msg or type(msg) == int:
                            print(f'coinex ws收到空消息 准备重连...', symbols, msg)
                            break

                        # elif 'deals.update' in msg:self._update_trade(msg)
                        if 'bbo.update' in msg:self._update_depth(msg)
                        elif 'state.update' in msg:self._update_feilv(msg)
                        elif 'order.update' in msg:self._update_order(msg)
                        elif 'asset.update' in msg:self._update_account(msg)
                        elif 'position.update' in msg:self._update_pos(msg)
                        elif 'pong' not in msg: 
                            # print(msg)
                            pass


            except Exception as e:
                # uploadError(traceback.format_exc())

                log(f'coinex ws连接失败 开始重连...', symbols, traceback.format_exc())
                time.sleep(1)
                # os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")