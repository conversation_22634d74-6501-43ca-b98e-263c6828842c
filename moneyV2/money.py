
from main.hanshu import *
from main.config import *

import random
import uvloop
import asyncio

def fuckBg(self):
    if self.fuckbg:
        tmpPos = []
        for pos2 in self.ws2.pos:
            if pos2['symbol'] in self.fuckGgSymbol:
                pos = pos2.copy()
                pos['symbol'] = pos['symbol'].replace('_UMCBL', '')
                if pos['symbol'].replace('USDT', '') in ['XEC', 'LUNC', 'PEPE', 'SHIB', 'FLOKI']:
                    pos['symbol'] = '1000'+pos['symbol']

                pos['side'] = fanSide(pos['side'])
                pos = GetPos2(self, pos, pos['jiage'])
                tmpPos.append(pos)
        self.ws.pos = tmpPos

# 定义一个专门创建事件循环的loop函数，在另一个线程中启动它
def start_loop(loop):
    asyncio.set_event_loop(loop)
    loop.run_forever()


""" 根据持仓方向计算费率"""
def getFeilv(side, feilv):
    # 正 变成 负，负变成正
    return feilv * -1 if side == 'BUY' else feilv


""" 获取增加的开仓比例
价差越大，开仓越大，最大2倍，现货0.7倍
8-11日 增加开仓比例没考虑资金费率，亏损1wu
"""
def GetAddBili(yingli, buy2, sell2, allFeeRoe):
    #历史盈利亏损禁止增加比例
    if yingli < -50:
        return 0

    allFeeRoe = min(allFeeRoe, 0)   #不参与
    m = max(buy2, sell2) - allFeeRoe - (OpenRoe * 1.6)
    return max(m, 0)



""" 开仓量 套利专用"""
def GetKaiLiang2(yuer, jiage, ws):
    if not jiage:
        return 0

    return N(yuer / jiage, ws)


""" 设置持仓时间"""
def setPosTime(self):
    if 'pos_time' not in self.api.d:
        self.api.d['pos_time'] = {}

    if 'pos_time' in self.api.d:
        chuange = 0
        pos2 = []
        for v in self.ws2.pos:
            if v['symbol'] not in self.api.d['pos_time']:
                self.api.d['pos_time'][v['symbol']] = NowTime()
                chuange = 1
            v['create_time'] = self.api.d['pos_time'][v['symbol']]
            pos2.append(v)
        self.ws2.pos = pos2

        for symbol in list(self.api.d['pos_time'].keys()):
            if not GetPos(symbol, pos2):
                chuange = 1
                del self.api.d['pos_time'][symbol]

        if chuange:
            self.api.SaveJson()


""" 用rest更新余额数据"""
def upYuer(self, exchanges):

    if Exchange2 in exchanges or self.spot2:
        yuer = self.coinex.GetYuer(p=0)
        self.ws2.usdt = yuer['all']
        self.ws2.keyong = yuer['keyong']
        self.ws2.yingkui = yuer['yingkui']

    if Exchange in exchanges or self.spot:
        if self.fuckbg:
            self.ws.usdt = self.ws2.usdt
            self.ws.keyong = self.ws2.keyong
            self.ws.yingkui = self.ws2.yingkui
        else:
            yuer = self.bnb.GetYuer(p=0)
            self.ws.usdt = yuer['all']
            self.ws.keyong = yuer['keyong']
            self.ws.yingkui = yuer['yingkui']


""" 用rest更新持仓数据"""
def upPos(self, rest, ws, set=0):

    if NowTime_ms() - self.posTime < 100:
        return log('更新持仓过快，跳过', Color('', -1))

    self.posTime = NowTime_ms()


    restTj = 0
    posR = rest.GetPos()
    posArr = [ws.pos, posR]

    for pos in posArr[0]:
        if not pos['liang']:
            continue
        for pos2 in posArr[1]:
            if pos['symbol'] == pos2['symbol'] and pos['side'] == pos2['side']:
                if abs(1 - pos['liang'] / pos2['liang']) > 0.01:
                    restTj = [pos['symbol'], pos['side'], 'ws', pos, 'rest', pos2]
                break
        else:
            restTj = ['未找到', pos['symbol'], pos['side'], 'ws', pos, 'rest', '']

    for pos in posArr[1]:
        for pos2 in posArr[0]:
            if pos['symbol'] == pos2['symbol'] and pos['side'] == pos2['side']:
                break

        else:
            restTj = ['未找到', pos['symbol'], pos['side'], 'ws', '', 'rest', pos]


    if not restTj:
        self.changePosTj = 0

    else:
        if not self.changePosTj:
            self.changePosTj = NowTime()

        if NowTime() - self.changePosTj > 15:
            log(Color('本地仓位与Rest仓位不符合', -1), restTj)
            set = 1

    if set:
        ws.pos = posR

    # jiazhi = 0
    # if Exchange == 'BianSwap' and not self.fuckbg:
    #     keyong = self.ws.usdt
    #     for v in self.ws.pos:
    #         if CcyBi == self.toSymbolFunc(v['symbol'], noCcyBi=1)[-4:]:
    #             MZ = self.symbol[v['symbol']]['Z'] if v['symbol'] in self.symbol else 1
    #             jiazhi += round(v['jiage'] * MZ * v['liang'], 4)
    #             keyong -= v['bzj'] - v['yingkui']

    #     if keyong > 0 and keyong < self.ws.keyong:
    #         self.ws.keyong = keyong

    # self.api.d['pos_jiazhi'] = round(jiazhi/self.ws2.usdt, 1)

    # setPosTime(self)





""" 启动监听费率和盘口的WS"""
def runWs(self, symbols, symbols2):
    #修改系统线程的限制  python执行不管用
    # os.system('sudo ulimit -n 2048')

    if not symbols:
        log('交易对异常', symbols)
        uploadLog(isExit=1)

    #启动一个框架
    loop = uvloop.new_event_loop() #采用高速事件循环库
    #添加携程进去
    loop.create_task(self.ws.main())
    if Exchange2:
        loop.create_task(self.ws2.main())
    t = threading.Thread(target=start_loop,args=(loop, ))
    t.setDaemon(True)
    t.start()

    loop = uvloop.new_event_loop() #采用高速事件循环库
    symbolss = arr_size(symbols, 1)
    log(Exchange+' runWs交易对', len(symbols), '分割成', len(symbolss))
    for symbol in symbolss:
        if not symbol:
            continue
        loop.create_task(self.ws.main(symbol))

    t = threading.Thread(target=start_loop,args=(loop, ))
    t.setDaemon(True)
    t.start()

    if Exchange2 and symbols2:
        loop = uvloop.new_event_loop() #采用高速事件循环库
        symbols22 = arr_size(symbols2, 1)
        log(Exchange2+' runWs交易对', len(symbols2), '分割成', len(symbols22))
        for symbol in symbols22:
            if not symbol:
                continue
            loop.create_task(self.ws2.main(symbol))

        t = threading.Thread(target=start_loop,args=(loop, ))
        t.setDaemon(True)
        t.start()


""" 获取盘口信息"""
def getDepth(symbol, data):
    depth = 0
    if symbol in data:
        depth = data[symbol]
        if 't' not in depth:
            return 0

    return depth


""" 设置初始化的JSON"""
def SetStartJson(self, symbols, symbols2):
    #设置杠杆
    if 'gg_bian' not in self.api.d:
        self.api.d['gg_bian'] = {}
        self.api.d['gg_coinex'] = {}
        self.api.d['tongji'] = {}


    #设置杠杆
    if 'bian_feilv' not in self.api.d:
        self.api.d['bian_feilv'] = 0
        self.api.d['coinex_feilv'] = 0
        self.api.d['today']['bian_feilv'] = 0
        self.api.d['today']['coinex_feilv'] = 0

        for s in list(self.api.d['tongji'].keys()):
            self.api.d['tongji'][s]['bian_feilv'] = 0
            self.api.d['tongji'][s]['coinex_feilv'] = 0
            self.api.d['tongji'][s]['today']['bian_feilv'] = 0
            self.api.d['tongji'][s]['today']['coinex_feilv'] = 0


    if Exchange != 'Huobi':
        for s in symbols:
            gg = self.symbol[s]['GangGan']

            if s not in self.api.d['gg_bian'] or self.api.d['gg_bian'][s] != gg:
                if hasattr(self.bnb, "SetGangGan"):
                    self.bnb.SetGangGan(s, gg)

                self.api.d['gg_bian'][s] = gg

            if Exchange == 'Ku':
                self.bnb.SetBzj(s)

    if Exchange2 != 'Huobi':
        for s in symbols2:
            gg = self.symbol2[s]['GangGan']
            if s not in self.api.d['gg_coinex'] or self.api.d['gg_coinex'][s] != gg or Exchange2 == 'Ku':       #傻逼KU老调整我杠杆
                if hasattr(self.coinex, "SetGangGan"):
                    self.coinex.SetGangGan(s, gg)

                self.api.d['gg_coinex'][s] = gg

            if Exchange2 == 'Ku':   #傻逼老关闭，老爆仓
                self.coinex.SetBzj(s)

    if 'coinex' not in self.api.d.keys():
        self.api.d['coinex'] = self.coinex.GetYuer()['all']
        self.api.d['volume'] = 0
        self.api.d['pos_jiazhi'] = 0
        self.api.d['today']['volume'] = 0
        self.api.d['dantui'] = 0
        self.api.d['today']['dantui'] = 0
        self.isOneRun = 1

    """ 老版本兼容 4-14日"""
    for s in list(self.api.d['pos'].keys()):
        if 'BUY' not in s and "SELL" not in s:
            self.api.d['pos']["BUY"+s] = self.api.d['pos'][s].copy()
            del self.api.d['pos'][s]
            log('兼容', s)

    self.api.SaveJson()






""" 找一下开仓价格"""
def GetPos2(self, pos, jiage, side=0):
    side = side if side else 'BUY'

    symbol = pos['symbol']
    symbol2 = side+symbol
    if symbol2 not in self.api.d['pos']:
        log(symbol, '异常开仓', pos, '当前价格', jiage, Color('', -1))
        addPos(self, symbol, jiage, pos['liang'], 10, side)
    pos2 = self.api.d['pos'][symbol2].copy()
    if 'liang' in pos2:
        del pos2['liang']
    pos = dict(pos, **pos2)
    return pos



""" 设置开仓"""
def addPos(self, symbol, jiage, liang, J2, side=0):
    if liang <= 0:
        return

    side = side if side else 'BUY'

    symbol2 = side+symbol
    if symbol2 in self.api.d['pos'].keys():
        prePos = self.api.d['pos'][symbol2]
        if 'liang' not in prePos:
            prePos['liang'] = 0

        preJiazhi = prePos['jiage'] * prePos['liang']
        nowJiazhi = jiage * liang
        nowJiage = R((preJiazhi + nowJiazhi) / (prePos['liang'] + liang), J2)
        log(symbol,
            '加仓', '价格', N(jiage, J2), '量', liang, U(nowJiazhi),
             '---> 上次持仓价格', N(prePos['jiage'], J2), '量', prePos['liang'], U(preJiazhi), '-->', '持仓均价', nowJiage, Color('', 1))

        jiage = nowJiage
        liang += prePos['liang']

    else:
        log(symbol, '当前价格', jiage, '量', liang, '价值', U(jiage*liang), Color('成功开仓', 1))


    # pos = GetPos(symbol, self.ws2.pos, side)
    # if pos:
    #     liang = max(pos['liang'], liang)

    posSave = {
        'symbol': symbol,
        'jiage': jiage,
        'liang': liang,
        'create_time': NowTime(),
    }

    self.api.d['pos'][symbol2] = posSave
    self.api.SaveJson()



""" 删除仓位"""
def delPos(self, symbol, side=0):
    side = side if side else 'BUY'

    symbol2 = side+symbol
    if symbol2 in self.api.d['pos']:
        del self.api.d['pos'][symbol2]
        log(symbol, Color('成功平仓', 1))
    else:
        log(symbol, Color('仓位不存在', -1))

    self.api.SaveJson()

""" 获取最近的结算时间离当前时间有多少秒"""
def getMinFeilvTime(feilvTime):
    nowDay = GetTime(0, "%Y-%m-%d")
    now = NowTime()
    all = []
    for v in feilvTime:
        t = time.mktime(time.strptime(f"{nowDay} {v}", '%Y-%m-%d %H'))
        if not v:
            t += 86400

        ok = t - now
        if ok>0:
            all.append(ok)
    """ 明天结算费率的时间"""
    if not all:
        for v in feilvTime:
            t = time.mktime(time.strptime(f"{nowDay} {v}", '%Y-%m-%d %H')) + 86400
            ok = t - now
            if ok>0:
                all.append(ok)

    if not all:
        uploadError(f'计算异常 nowDay:{nowDay} now:{now} feilvTime:{feilvTime}')

    return min(all) if all else 0


""" 资金费率风控"""
def dangerFeilv(self, openRoe, symbol, buy, buy2, feilv, feilv2, toFeilv):

    """ 套资金费率开仓"""
    # if buy < openRoe:
    #     if feilv > feilv2:
    #         t = getMinFeilvTime(self.feilv_time)
    #     else:
    #         t = getMinFeilvTime(self.feilv_time2)

        # """ 套资金费率距离结算必须在40分钟内"""
        # if t / 60 > 40:
        #     tlog(
        #         f'{symbol} 套资金费率',
        #         [Exchange, feilv, Exchange2, feilv2, '总和', round(feilv+feilv2, 4), '距离结算', round(t / 60, 2), '分', Color('禁止套利...', 0)],
        #          5*60)
        #     return 0

    if Exchange2 == 'Ku':
        feilv_ku = min(feilv2, feilv)
        if feilv_ku < -0.1:
            tlog(f'{symbol} Ku资金费率风控', ['allFei', StrBi(feilv_ku/100), 'buy2', StrBi(buy2/100), '|', 'buy', StrBi(buy/100), Color('', -1)], 3*60)
            return 0

    """ 价差很大，但是资金费率很坏，价差必须是资金费率的2倍"""
    """ 10-17日  如果是每4小时结算一次的话，资金费率太贵就放弃，回撤3w5u"""

    """ 这里只对币安判断，平仓是两者相加"""
    if feilv < -0.4 and toFeilv:
        tlog(f'{symbol} 资金费率风控2',
         ['币安', StrBi(feilv/100), Exchange2, StrBi(feilv2/100), '',
          'buy2', StrBi(buy2/100), '|', 'buy', StrBi(buy/100), '币安费率太高，每4小时结算一次，放弃', Color('', -1)], 3*60)
        return 0

    feilv = buy2 - buy
    mult  = 8 if toFeilv else 2.2
    feilv = min(feilv2, feilv)
    yuzhi = -0.08 if Exchange2 == 'Huobi' else -0.2

    """
    2023-12-3 Huobi费率抽风了
    """
    if Exchange2 == 'Huobi':
        mult *= 1.5

    if feilv < yuzhi and buy < abs(feilv * mult):
        tlog(f'{symbol} 资金费率风控',
         ['allFei', StrBi(feilv/100), 'buy2', StrBi(buy2/100), '|', 'buy', StrBi(buy/100), '<', 'allFei*mult', StrBi(abs(feilv*mult/100)),
          'mult', mult, Color('', -1)], 3*60)
        return 0

    return 1


""" 根据历史盈利来计算最大仓位"""
def getSymbolBili(self, s):

    bili = Kai_Bili
    if s in self.api.d['tongji']:
        yingli = self.api.d['tongji'][s]['m'] + self.api.d['tongji'][s]['bian_feilv'] + self.api.d['tongji'][s]['coinex_feilv']
        if yingli > 0:
            if yingli > 10:
                bili = Kai_Bili * 1.2
            if yingli > 30:
                bili = Kai_Bili * 1.3
            if yingli > 100:
                bili = Kai_Bili * 1.5

        else:
            if yingli < 5:
                bili = Kai_Bili * 0.7
            if yingli < 0:
                bili = Kai_Bili * 0.6
            if yingli < -15:
                bili = Kai_Bili * 0.5
            if yingli < -30:
                bili = Kai_Bili * 0.4
            if yingli < -50:
                bili = Kai_Bili * 0.3
            if yingli < -70:
                bili = Kai_Bili * 0.2
            if yingli < -90:
                bili = Kai_Bili * 0.1
            if yingli < -100:
                bili = Kai_Bili * 0.05

    if self.spot2:
        bili = min(98, bili)

    return bili


""" 订单更新，计算本地仓位"""
def changePos(self, symbol, side, jiage, liang, jiancang='-', ret=0):
    liang = str(liang).lstrip('-')
    jiage = D(jiage)
    liang = D(liang)

    tmpPos = self.pos.copy()

    for i in range(0, len(tmpPos)):
        """ 存在仓位"""
        if tmpPos[i]['symbol'] == symbol:
            pos = tmpPos[i].copy()
            pos['jiage'] = D(pos['jiage'])
            pos['liang'] = D(pos['liang'])

            """ 判断修改的是不是这个仓位"""
            if jiancang != '-':
                if jiancang and side != pos['side2']:   #如果是减仓，订单方向应该等于平仓方向
                    continue

                if not jiancang and side != pos['side']: #如果是加仓，订单方向应该等于持仓方向
                    continue

            """ 减仓"""
            if side != pos['side']:
                # print(symbol, side, jiage, liang, "--------")
                if liang > pos['liang']:
                    pos['side'] = fanSide(pos['side'])
                    pos['side2'] = fanSide(pos['side2'])
                    pos['jiage'] = jiage
                    pos['liang'] = liang - pos['liang']
                else:
                    pos['liang'] -= liang

                """ 仓位全平"""
                if pos['liang'] <= 0.0000001:
                    # print(symbol, "仓位全平")
                    del tmpPos[i]
                    break

            else:
                """ 加仓"""
                # print(symbol, side, jiage, liang, "++++++++")
                pos['jiage'] = ((pos['jiage'] * pos['liang']) + (jiage * liang)) / (pos['liang'] + liang)    # 价格 = 总价值 / 总量
                pos['liang'] += liang

            pos['jiage'] = float(pos['jiage'])
            pos['liang'] = float(pos['liang'])
            tmpPos[i] = pos
            break

    else:
        # print(symbol, side, jiage, liang, "新仓位")
        """ 新仓位"""
        tmpPos.append(
            {
                'symbol': symbol,
                'liang': float(liang),
                'jiage': float(jiage),
                'side': side,
                'side2': fanSide(side),
                'nowJiage': 0,
                'yingkui': 0,
                'bzj': 0,
                'time': NowTime_ms(),
            }
        )

    if ret:
        return tmpPos
    else:
        self.pos = tmpPos





""" 获取仓位数据，兼容币安，兼容现货"""
def GetNewPos(posAll, name, symbol, side, jiage, MZ, L):

    pos = GetPos(symbol, posAll, side)

    if pos:
        if not pos['liang']:
            return []

        # if 'Spot' in name:
        #     pos = GetPos2(self, pos, jiage, side)
        #     pos['liang'] = Si(pos['liang'], L)
        # else:
        pos['liang'] = N(pos['liang'], L)
        pos['jiazhi'] = round(jiage * pos['liang'] * MZ, 4)

        if not pos['jiazhi']:
            return []

    return pos

def getCaokongPos(posAll, name, symbol, bid, MZ, L):
    buyPos = GetNewPos(posAll, name, symbol, 'BUY', bid, MZ, L)
    sellPos = GetNewPos(posAll, name, symbol, 'SELL', bid, MZ, L)

    if buyPos and sellPos:
        if buyPos['liang'] * bid * MZ > 15:
            return buyPos
        return sellPos
    elif buyPos:
        return buyPos
    elif sellPos:
        return sellPos

#每次下单间隔时间
def randTime():
    return random.randint(0.1 * 100, 7 * 100)*10

#每次下单的价格
def randPrice(side, bid, ask, J, check=0):
    start = 0.0002
    end = 0.0014
    if side == 'BUY':
        mid = bid
        startPrice = mid - mid*end
        endPrice = mid - mid*start
    else:
        mid = ask
        startPrice = mid + mid*start
        endPrice = mid + mid*end

    price = round(random.uniform(startPrice, endPrice), J)
    # if check:
    #     print(side, 'mid', mid, startPrice, endPrice, "--> check", check,  check > startPrice and check < endPrice)

    if check and check > startPrice and check < endPrice:
        return False

    #防止生成随机价格和原来的价格一样
    if check and check == price:
        return randPrice(side, bid, ask, J, check)

    return price

#每次下单的金额
def randUsdt(max_value):
    ranges = [
        (20, min(max_value, 999)),
        (1000, min(max_value, 9999)),
        (10000, min(max_value, 99999)),
        # (100000, min(max_value, 2000000)),
    ]

    valid_ranges = [r for r in ranges if r[0] < max_value]

    if not valid_ranges:
        return 0
    chosen_range = random.choice(valid_ranges)
    random_number = random.uniform(chosen_range[0], min(chosen_range[1], max_value))
    return random_number





""" 操控市场传给Web的内容"""
def getCaokongData(self, tmp_symbol, data, pos, maxPos):

    J, L, Max, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['M']*0.99, self.symbol['Min'], self.symbol['Z']
    bid, ask = float(data['bidPx']), float(data['askPx'])
    posGg = self.symbol['GangGan']

    usdt = self.ws.usdt + self.bnb_usdt
    if 'spot' in Name and pos and pos['side'] == 'SELL':
        usdt = self.ws.keyong - pos['liang']*ask*MZ

    data = {
        'name': Name,
        'yuer': round(usdt, 1),
        'keyong': round(self.ws.keyong, 1),
        'symbol': tmp_symbol+f'[{posGg}x]'+f'[{self.max_ganggan}x]',
        'side': '-',
        'liang': '-',
        'liang_u': 0,
        'maxliang': round(maxPos*ask),
        'Z': MZ,
        'jiage': 0,
        'ping_jiage': ask,
        'yingkui': 0,
        'yingkui2': 0,
        'create_time': '-',
        'time': NowTime(),
        'okOpen': 1 if self.error >= 2 or self.stopOpen else 0,
    }

    if pos and pos['liang']:
        ping_jiage = ask if 'SELL' == pos['side'] else bid
        yingkui, _ = GetRoe(ping_jiage, pos, MZ)
        jiazhi = pos['liang']*ask*MZ

        data['side'] = Color('▲多', 1, html=1) if 'BUY' == pos['side'] else Color('▼空', -1, html=1)
        data['liang'] = STR_N(jiazhi, 0)+'$'+ ' (' + StrBi(jiazhi/self.ws.usdt, 0)+')'
        data['liang_u'] = jiazhi
        data['jiage'] = N(pos['jiage'], J)
        data['ping_jiage'] = ping_jiage
        data['yingkui'] = Color(round(yingkui, 1), html=1)+' ('+StrBi(yingkui/jiazhi)+')'
        data['yingkui2'] = yingkui

    self.data = data
