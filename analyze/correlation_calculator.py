#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币种相关性计算器
输入两个币种，计算过去24小时1分钟K线的相关性
"""

import requests
import pandas as pd
import numpy as np
import sys
import time
import warnings
warnings.filterwarnings('ignore')


class CorrelationCalculator:
    def __init__(self):
        self.futures_base_url = "https://fapi.binance.com"
        self.session = requests.Session()

    def get_kline_data(self, symbol, interval='1m', limit=1440):
        """获取K线数据 (1440分钟 = 24小时)"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/klines"
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': limit
                }
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} K线数据失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def process_kline_data(self, kline_data):
        """处理K线数据，提取收盘价"""
        if not kline_data:
            return None

        prices = []
        timestamps = []

        for kline in kline_data:
            timestamp = kline[0]  # 开盘时间
            close_price = float(kline[4])  # 收盘价

            prices.append(close_price)
            timestamps.append(timestamp)

        return pd.Series(prices, index=timestamps)

    def calculate_correlation(self, price_series1, price_series2):
        """
        计算两个价格序列的相关性

        使用收益率相关性（标准金融学方法）：
        1. 价格变化率已经是标准化的：(price_t - price_t-1) / price_t-1
        2. 相关性本身就是标准化的度量，不受价格绝对值影响
        3. 这是金融学中分析资产相关性的标准做法
        """
        if price_series1 is None or price_series2 is None:
            return None, "数据获取失败"

        if len(price_series1) == 0 or len(price_series2) == 0:
            return None, "数据为空"

        # 确保两个序列长度相同
        min_length = min(len(price_series1), len(price_series2))
        if min_length < 100:  # 至少需要100个数据点
            return None, f"数据点不足，需要至少100个，实际{min_length}个"

        series1 = price_series1.iloc[-min_length:]
        series2 = price_series2.iloc[-min_length:]

        # 计算价格变化率（收益率）
        # 这里自动进行了标准化：无论价格是110,000还是0.1
        # 变化率都是无量纲的比率
        returns1 = series1.pct_change().dropna()
        returns2 = series2.pct_change().dropna()

        if len(returns1) < 50 or len(returns2) < 50:
            return None, f"有效收益率数据不足，需要至少50个"

        # 过滤异常值（变化率超过50%的数据点）
        returns1_filtered = returns1[(returns1.abs() <= 0.5)]
        returns2_filtered = returns2[(returns2.abs() <= 0.5)]

        if len(returns1_filtered) < 50 or len(returns2_filtered) < 50:
            return None, "过滤异常值后数据不足"

        # 确保两个序列对应相同的时间点
        common_index = returns1_filtered.index.intersection(returns2_filtered.index)
        if len(common_index) < 50:
            return None, "时间对齐后数据不足"

        returns1_aligned = returns1_filtered.loc[common_index]
        returns2_aligned = returns2_filtered.loc[common_index]

        # 计算皮尔逊相关系数
        correlation = returns1_aligned.corr(returns2_aligned)

        if pd.isna(correlation):
            return None, "相关性计算失败"

        return correlation, "计算成功"

    def get_correlation_description(self, correlation):
        """获取相关性描述"""
        if correlation is None:
            return "无数据"

        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "强相关"
        elif abs_corr >= 0.6:
            return "中等相关"
        elif abs_corr >= 0.3:
            return "弱相关"
        else:
            return "不相关"

    def format_symbol(self, symbol):
        """格式化交易对名称"""
        symbol = symbol.upper().strip()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
        return symbol

    def calculate_pair_correlation(self, symbol1, symbol2):
        """计算两个币种的相关性"""
        # 格式化交易对名称
        symbol1 = self.format_symbol(symbol1)
        symbol2 = self.format_symbol(symbol2)

        print(f"正在计算 {symbol1} 和 {symbol2} 的相关性...")
        print(f"数据来源：Binance期货，过去24小时1分钟K线")
        print("-" * 60)

        # 获取K线数据
        print(f"获取 {symbol1} 数据...")
        kline1 = self.get_kline_data(symbol1)
        prices1 = self.process_kline_data(kline1)

        print(f"获取 {symbol2} 数据...")
        kline2 = self.get_kline_data(symbol2)
        prices2 = self.process_kline_data(kline2)

        if prices1 is None:
            print(f"❌ {symbol1} 数据获取失败，请检查币种名称是否正确")
            return

        if prices2 is None:
            print(f"❌ {symbol2} 数据获取失败，请检查币种名称是否正确")
            return

        print(f"✅ {symbol1} 数据：{len(prices1)} 个数据点")
        print(f"✅ {symbol2} 数据：{len(prices2)} 个数据点")

        # 计算相关性
        correlation, status = self.calculate_correlation(prices1, prices2)

        print("\n" + "="*60)
        print("📊 相关性分析结果")
        print("="*60)

        if correlation is not None:
            description = self.get_correlation_description(correlation)

            print(f"交易对1：{symbol1}")
            print(f"交易对2：{symbol2}")
            print(f"相关系数：{correlation:.6f}")
            print(f"相关性强度：{description}")
            print(f"计算状态：{status}")

            # 解释相关性
            print("\n📝 结果解释：")
            if correlation > 0:
                print(f"• 正相关：当{symbol1}价格上涨时，{symbol2}价格倾向于上涨")
            elif correlation < 0:
                print(f"• 负相关：当{symbol1}价格上涨时，{symbol2}价格倾向于下跌")
            else:
                print(f"• 无相关：{symbol1}和{symbol2}价格变动相互独立")

            abs_corr = abs(correlation)
            if abs_corr >= 0.8:
                print("• 强相关：两个币种价格走势高度一致")
            elif abs_corr >= 0.6:
                print("• 中等相关：两个币种价格走势有一定关联")
            elif abs_corr >= 0.3:
                print("• 弱相关：两个币种价格走势关联较弱")
            else:
                print("• 不相关：两个币种价格走势基本独立")

        else:
            print(f"❌ 相关性计算失败：{status}")

        print("="*60)


def main():
    calculator = CorrelationCalculator()

    print("🔗 币种相关性计算器")
    print("基于过去24小时1分钟K线数据计算两个币种的价格相关性")
    print("="*60)

    # 检查命令行参数
    if len(sys.argv) == 3:
        symbol1 = sys.argv[1]
        symbol2 = sys.argv[2]
    else:
        # 交互式输入
        print("请输入两个币种名称（例如：BTC, ETH）")
        symbol1 = input("币种1: ").strip()
        symbol2 = input("币种2: ").strip()

        if not symbol1 or not symbol2:
            print("❌ 币种名称不能为空")
            return

    try:
        calculator.calculate_pair_correlation(symbol1, symbol2)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
