{"cells": [{"cell_type": "code", "execution_count": 5, "id": "9eb46556", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>base_asset</th>\n", "      <th>spot_tick_size</th>\n", "      <th>spot_last_price</th>\n", "      <th>spot_tick_ratio</th>\n", "      <th>spot_volume_24h</th>\n", "      <th>futures_tick_size</th>\n", "      <th>futures_last_price</th>\n", "      <th>futures_tick_ratio</th>\n", "      <th>futures_volume_24h</th>\n", "      <th>volume_ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MANTAUSDT</td>\n", "      <td>MANTA</td>\n", "      <td>0.0010</td>\n", "      <td>0.2390</td>\n", "      <td>0.004184</td>\n", "      <td>5.894100e+06</td>\n", "      <td>0.00010</td>\n", "      <td>0.23830</td>\n", "      <td>0.000420</td>\n", "      <td>1.817906e+07</td>\n", "      <td>0.324225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>OMNIUSDT</td>\n", "      <td>OMNI</td>\n", "      <td>0.0100</td>\n", "      <td>2.6600</td>\n", "      <td>0.003759</td>\n", "      <td>7.313980e+06</td>\n", "      <td>0.00100</td>\n", "      <td>2.66200</td>\n", "      <td>0.000376</td>\n", "      <td>4.250305e+07</td>\n", "      <td>0.172081</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DYMUSDT</td>\n", "      <td>DYM</td>\n", "      <td>0.0010</td>\n", "      <td>0.2830</td>\n", "      <td>0.003534</td>\n", "      <td>2.081838e+06</td>\n", "      <td>0.00010</td>\n", "      <td>0.28340</td>\n", "      <td>0.000353</td>\n", "      <td>6.881994e+06</td>\n", "      <td>0.302505</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NKNUSDT</td>\n", "      <td>NKN</td>\n", "      <td>0.0001</td>\n", "      <td>0.0291</td>\n", "      <td>0.003436</td>\n", "      <td>7.035039e+05</td>\n", "      <td>0.00001</td>\n", "      <td>0.02913</td>\n", "      <td>0.000343</td>\n", "      <td>3.085510e+06</td>\n", "      <td>0.228002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GTCUSDT</td>\n", "      <td>GTC</td>\n", "      <td>0.0010</td>\n", "      <td>0.2980</td>\n", "      <td>0.003356</td>\n", "      <td>4.646929e+05</td>\n", "      <td>0.00100</td>\n", "      <td>0.29700</td>\n", "      <td>0.003367</td>\n", "      <td>3.283415e+06</td>\n", "      <td>0.141527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FLMUSDT</td>\n", "      <td>FLM</td>\n", "      <td>0.0001</td>\n", "      <td>0.0313</td>\n", "      <td>0.003195</td>\n", "      <td>8.614341e+05</td>\n", "      <td>0.00010</td>\n", "      <td>0.03120</td>\n", "      <td>0.003205</td>\n", "      <td>9.141645e+06</td>\n", "      <td>0.094232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SCRUSDT</td>\n", "      <td>SCR</td>\n", "      <td>0.0010</td>\n", "      <td>0.3180</td>\n", "      <td>0.003145</td>\n", "      <td>1.229028e+06</td>\n", "      <td>0.00010</td>\n", "      <td>0.31790</td>\n", "      <td>0.000315</td>\n", "      <td>3.410531e+06</td>\n", "      <td>0.360363</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>TRUUSDT</td>\n", "      <td>TRU</td>\n", "      <td>0.0001</td>\n", "      <td>0.0354</td>\n", "      <td>0.002825</td>\n", "      <td>1.837601e+06</td>\n", "      <td>0.00001</td>\n", "      <td>0.03541</td>\n", "      <td>0.000282</td>\n", "      <td>9.964720e+06</td>\n", "      <td>0.184411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>VANRYUSDT</td>\n", "      <td>VANRY</td>\n", "      <td>0.0001</td>\n", "      <td>0.0356</td>\n", "      <td>0.002809</td>\n", "      <td>2.295894e+06</td>\n", "      <td>0.00001</td>\n", "      <td>0.03564</td>\n", "      <td>0.000281</td>\n", "      <td>5.902371e+06</td>\n", "      <td>0.388978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>LUMIAUSDT</td>\n", "      <td>LUMIA</td>\n", "      <td>0.0010</td>\n", "      <td>0.3560</td>\n", "      <td>0.002809</td>\n", "      <td>2.334039e+06</td>\n", "      <td>0.00010</td>\n", "      <td>0.35460</td>\n", "      <td>0.000282</td>\n", "      <td>7.043526e+06</td>\n", "      <td>0.331374</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      symbol base_asset  spot_tick_size  spot_last_price  spot_tick_ratio  \\\n", "0  MANTAUSDT      MANTA          0.0010           0.2390         0.004184   \n", "1   OMNIUSDT       OMNI          0.0100           2.6600         0.003759   \n", "2    DYMUSDT        DYM          0.0010           0.2830         0.003534   \n", "3    NKNUSDT        NKN          0.0001           0.0291         0.003436   \n", "4    GTCUSDT        GTC          0.0010           0.2980         0.003356   \n", "5    FLMUSDT        FLM          0.0001           0.0313         0.003195   \n", "6    SCRUSDT        SCR          0.0010           0.3180         0.003145   \n", "7    TRUUSDT        TRU          0.0001           0.0354         0.002825   \n", "8  VANRYUSDT      VANRY          0.0001           0.0356         0.002809   \n", "9  LUMIAUSDT      LUMIA          0.0010           0.3560         0.002809   \n", "\n", "   spot_volume_24h  futures_tick_size  futures_last_price  futures_tick_ratio  \\\n", "0     5.894100e+06            0.00010             0.23830            0.000420   \n", "1     7.313980e+06            0.00100             2.66200            0.000376   \n", "2     2.081838e+06            0.00010             0.28340            0.000353   \n", "3     7.035039e+05            0.00001             0.02913            0.000343   \n", "4     4.646929e+05            0.00100             0.29700            0.003367   \n", "5     8.614341e+05            0.00010             0.03120            0.003205   \n", "6     1.229028e+06            0.00010             0.31790            0.000315   \n", "7     1.837601e+06            0.00001             0.03541            0.000282   \n", "8     2.295894e+06            0.00001             0.03564            0.000281   \n", "9     2.334039e+06            0.00010             0.35460            0.000282   \n", "\n", "   futures_volume_24h  volume_ratio  \n", "0        1.817906e+07      0.324225  \n", "1        4.250305e+07      0.172081  \n", "2        6.881994e+06      0.302505  \n", "3        3.085510e+06      0.228002  \n", "4        3.283415e+06      0.141527  \n", "5        9.141645e+06      0.094232  \n", "6        3.410531e+06      0.360363  \n", "7        9.964720e+06      0.184411  \n", "8        5.902371e+06      0.388978  \n", "9        7.043526e+06      0.331374  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "data = pd.read_csv(\"binance_analysis.csv\")\n", "data.head(10)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}