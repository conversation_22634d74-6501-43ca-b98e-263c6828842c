#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
import traceback  # 输出报错
from main.hanshu import *
from main.config import *
from hashlib import sha512
import base64
import hmac
from datetime import datetime
import json
from urllib import parse
import requests
from pprint import pprint
import ujson
import time
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning


urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))


def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]


class gate_spot():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        keys = {
            'gate下级': ['cd9ee26f26eb5a5dc25a501d217b2990', '359bc57fa51c5acbcded92a58da4df05c651f3d7bd844b81507a54e90688c0b5'],
            'gate8': ['4d9a6bdbe91e1a916da43e8080bb8c35', '0855abfbff35775c2dd8fe6e5d787c6f6cecaa2c0427e6e2b24e11d85a3d087c'],
                'v16': ['2aadeca376ced95ab8ba05f94274b07f', '6b35d68de611ee0553a43a11706aef3fc0e523a0e37aae4252cefae706df86b3'],


        }

        self.debug = 0
        self.debugs = []
        self.ccyBi = ccyBi if ccyBi else CcyBi2
        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]
        #self.passwd = keys[apiKey][2]
        self.tickerData = []

        try:
            if GateColo:
                log('Gate Rest 使用colo高速线路')
                self.HOST = 'https://apiv4-private.gateapi.io'
            else:
                self.HOST = 'https://api.gateio.ws'
        except:
            self.HOST = 'https://api.gateio.ws'

        self.session = requests.Session()

        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log('gate 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  # 指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')

    def get_sign(self, method, uri, params, body=None):
        t = time.time()
        m = sha512()
        m.update((body or "").encode('utf-8'))
        hashed_payload = m.hexdigest()
        s = '%s\n%s\n%s\n%s\n%s' % (method, uri, params or "", hashed_payload, t)
        sign = hmac.new(self.secret_key.encode('utf-8'), s.encode('utf-8'), sha512).hexdigest()
        return {'KEY': self.access_id, 'Timestamp': str(t), 'SIGN': sign}

    # 请求API
    def go(self, method, path, payload=None, needKey=1):
        url = self.HOST + path
        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        params = ''

        headers = {
            'Accept': 'application/json',
            'Content-type': 'application/json'
        }

        
        if needKey:
            if method in ["POST", 'PUT']:
                body = json.dumps(payload)
                query_param = None
                sign_headers = self.get_sign(method, path, query_param, body)
                headers.update(sign_headers)
            if method == "GET" or method == "DELETE":
                query_param = ''
                if payload:
                    for i in payload:
                        query_param += f'{i}={payload[i]}&'
                    query_param = query_param[:-1]
                sign_headers = self.get_sign(method, path, query_param)
                headers.update(sign_headers)

        if method in ['POST', 'PUT']:
            params = {'url': url, 'timeout': 5,
                      'data': json.dumps(payload), 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5,
                      'params': payload, 'headers': headers}
        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:' +
                  str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs), 2)))

        return response2


    def GetTickerAll(self):
        data = self.go("GET", "/api/v4/spot/tickers", {}, needKey=0)
        # print(data)

        if not data:
            print('Gate 获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        return data

    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/api/v4/spot/currency_pairs', {}, needKey=0)


    
    """ 获取现货币种持仓"""
    def GetPos(self, symbol=0, all=0):
        data = self.go('GET', "/api/v4/spot/accounts", "")
        try:
            okdata = []
            for v in data:
                if 'currency' not in v:
                    log(Color("获取现货持仓失败", -1), data)
                    time.sleep(1)
                    return self.GetPos(symbol, all)

                symbol = v['currency']
                if symbol == CcyBi2 and not all:
                    pprint(v)
                    continue

                ok = {}
                ok['symbol'] = symbol + '_' + self.ccyBi if symbol != self.ccyBi else symbol
                ok['liang'] = float(v['available'])
                if not ok['liang']:
                    continue

                ok['side'] = 'BUY'
                ok['side2'] = 'SELL'
                ok['jiage'] = 0
                ok['nowJiage'] = 0
                ok['yingkui'] = 0
                ok['bzj'] = 0
                ok['roe'] = 0
                ok['time'] = NowTime_ms()

                okdata.append(ok)
            return okdata
        
        except Exception as e:
            uploadError(traceback.format_exc())

        log(Color("获取现货持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)

    """获取可用余额"""
    def GetYuer(self,p=1, ref=1, ccyBi=0, symbols={}):
        ccyBi = ccyBi if ccyBi else CcyBi2

        all = self.GetPos(all=1)
        if not self.tickerData or tlog('GateSpotTickers', '', 20, xs=0):
            self.tickerData = self.go('GET', "/api/v4/spot/tickers")

        keyong = 0
        jiazhi = 0
        for pos in all:
            if pos['symbol'] == ccyBi:
                keyong = pos['liang']

            else:
                for item in self.tickerData:
                    if pos['symbol'] == item['currency_pair']:
                        jiazhi += pos['liang'] * float(item['last'])
                        break

                else:
                    if pos['symbol'] not in ['USDT_USDT', 'USDC_USDT'] and '均衡' not in ServerName:
                        log(pos, 'Gate Spot未找到价格', Color('', -1))

        ok = {}
        ok['all'] = Si(jiazhi + keyong, 4)
        ok['keyong'] = Si(keyong, 4)
        ok['yingkui'] = 0

        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(p + '  GateSpot USDT总余额' if type(p) == str else 'Gate USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                '持仓盈亏', N(ok['yingkui'], 4))

        return ok

    """下单"""

    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        if float(liang)*float(jiage) < 5:
            return 0, '价值小于5u'

        jiage = str(jiage)
        liang = str(liang)
        liang2 = liang+' ('+STR_N(float(liang)*float(jiage))+'$)'

        msg = 'Gate现货　'+symbol+'　'

        if side == 'BUY':
            msg += '买入'
            side = 'buy'
        else:
            msg += '卖出'
            side = 'sell'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　Type:"+type

        post = {
            'text': 't-123456',
            'currency_pair': symbol,
            'account': 'spot',
            'side': side,
            'iceberg': 0,
            'amount': liang,
            'price': jiage,
            'auto_borrow': False
        }
        
        """ IOC订单，否则市价单"""
        if type == 'ioc':
            post['type'] = 'limit'
            post['time_in_force'] = 'ioc'

        else:
            post['type'] = 'market'
            post['time_in_force'] = 'ioc'
            
            # if side == 'BUY':
            #     post['amount'] = N(liang*jiage, 4)
            """
            type为limit时，指交易货币，即需要交易的货币，如BTC_USDT中指BTC。
            type为market时，根据买卖不同指代不同
            side : buy 指代计价货币，BTC_USDT中指USDT
            side : sell 指代交易货币，BTC_USDT中指BTC
            """

        orderId = 0

        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST', "/api/v4/spot/orders", post)

            # log("下单返回", order)
            if not order or 'id' not in order:
                log(symbol+" [!!!] 套利下单失败！！", post, order)

            else:
                orderId = order['id']
                break

        msg = [msg + '　' + Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]

        return orderId, msg

    """ 获取Userid"""

    def GetUserId(self):
        data = self.go("GET", "/api/v4/account/detail")
        return data['user_id']
    
    def GetWsUrl(self):
        if GateColo:
            return 'wss://spotws-private.gateapi.io/ws/v4/'
        
        return 'wss://api.gateio.ws/ws/v4/'



if __name__ == "__main__":
    k = gate_spot()
    balance = k.GetYuer()
    print("当前获取现货余额：%s"%balance)
    symbols = k.GetSymbols()
    print("市场现货列表：%s"%symbols[:5])
    hold = k.GetPos('doge')
    print("现货持仓：%s"%hold)
    order = k.PostOrder('DOGE_USDT', 'BUY', 0.05, 100, "limit")
    print("下单：%s"%str(order))
