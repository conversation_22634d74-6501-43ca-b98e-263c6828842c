from main.hanshu import *
from main.config import *
from moneyV2.money import *
import math

# 交易对转成币安交易对格式
def toSymbol(symbol, noCcyBi=0):
    symbol2 = list(symbol)
    if '_UMCBL' == symbol[-6:]:
        symbol2[-6:] = ''
        return "".join(symbol2)

    if '_SPBL' == symbol[-5:]:
        symbol2[-5:] = ''
        return "".join(symbol2)

    if '1000' == symbol[:4]:
        symbol2[:4] = ''
        return "".join(symbol2)

    if 'USDTM' == symbol[-5:]:
        symbol2[-5:] = 'USDT'
        return "".join(symbol2)

    if '-USD' == symbol[-4:]:
        symbol2[-4:] = 'USDT'
        return "".join(symbol2)

    if '-SWAP' in symbol:       #OKX需要二次替换掉-
        symbol = symbol.replace('-SWAP', '')

    if '_' in symbol:
        return symbol.replace('_', '')

    if '-' in symbol:
        return symbol.replace('-', '')

    if not noCcyBi:
        if 'USDC' == symbol[-4:]:
            symbol2[-4:] = 'USDT'
            return "".join(symbol2)

        if 'BUSD' == symbol[-4:]:
            symbol2[-4:] = 'USDT'
            return "".join(symbol2)

    return symbol



""" 分布式切割交易对"""
def FengeSymbols(symbols, symbols2, print=1):

    fenge = ServerName.split('_')
    if len(fenge) > 4:
        f = 4 if Exchange2 == 'Bitget' and 'V9' in ServerName else 3
        f = math.ceil(len(symbols)/f)

        symbolsF = arr_size(symbols, f)
        symbols2F = arr_size(symbols2, f)
        if print:
            log('当前编号', ServerNameId, '原交易对数量', len(symbols), '分割后', len(symbolsF[0]) , '共', len(symbolsF), '组')

        if ServerNameId in ['1', '2', '3', '4', '5', '6']:
            symbols = symbolsF[int(ServerNameId)-1]
            symbols2 = symbols2F[int(ServerNameId)-1]
            if print:
                log('使用集群分割 --> ', ServerNameId, Color('', -1))

    if print:
        log(symbols)
    return symbols, symbols2

# 获取币安交易规则
def GetBnbSymbol(rest):

    SetGangGan2 = rest.GangGan if hasattr(rest, 'GangGan') else SetGangGan

    symbolData = rest.GetSymbols()
    symbolGG = rest.GetGanggan()
    symbolFeilv = rest.Get4HoursSymbol()

    symbols = {}

    for s2 in symbolData:
        symbol = s2['symbol']

        if s2['status'] != 'TRADING':
            # print(symbol, s2['status'], '已下架，跳过')
            continue

        gg = 0
        maxgg = 0
        is4hours = 0
        for v in symbolGG:
            try:
                if v['symbol'] == symbol:
                    for thisgg in v['brackets']:
                        maxgg = max(maxgg,  thisgg['initialLeverage'])

            except:
                pprint(symbolGG)

        for v in symbolGG:
            try:
                if v['symbol'] == symbol:
                    for thisgg in v['brackets']:
                        if SetGangGan2 <= thisgg['initialLeverage']:
                            gg = thisgg  # 5-12 杠杆太高开不了仓位

                        if not gg and SetGangGan2 > thisgg['initialLeverage']:
                            gg = thisgg
                            break
            except:
                pprint(symbolGG)

        for v in symbolFeilv:
            try:
                if v['symbol'] == symbol:
                    is4hours = 8 / v['fundingIntervalHours']
                    break
            except:
                print('error symbolFeilv')
                pprint(v)

        if not gg:
            log(symbol, '未找到此交易对的杠杆规则')

        try:
            # 先把末尾的0删掉，再取消科学计数法，最大6位，不可以用flaot
            J = WeiShu(s2['filters'][0]['tickSize'])
            L = WeiShu(s2['filters'][1]['stepSize'])  # 数量单位
            if "1000" in symbol:
                J += 3

            symbols[symbol] = {
                'J': J,
                'J2': float(s2['filters'][0]['tickSize']),
                'L': L,
                # 市价最大下单数量
                'M': N(float(s2['filters'][2]['maxQty']) * X(symbol), L),
                'GangGan': min(gg['initialLeverage'], SetGangGan2),
                'MaxGangGan': maxgg,
                'maxJiazhi': gg['notionalCap'],  # 最大持仓价值
                'Min': 0,
                'Z': 1,
                'is4hours': is4hours,
            }

        except Exception as e:
            log(symbol, s2, '数据异常', Color('', -1))
            uploadError(traceback.format_exc())

    return symbols




# 获取币安现货交易规则
def GetBnbSpotSymbol(rest):

    symbolData = rest.GetSymbols()
    symbols = {}

    for s2 in symbolData:
        symbol = s2['symbol']

        if s2['status'] != 'TRADING':
            # print(symbol, s2['status'], '已下架，跳过')
            continue

        try:
            # if symbol == 'ENJUSDT':
            #     pprint(s2)
            J = WeiShu(s2['filters'][0]['tickSize'])  #先把末尾的0删掉，再取消科学计数法，最大6位，不可以用flaot
            L = WeiShu(s2['filters'][1]['stepSize'])  #数量单位
            M = float(s2['filters'][1]['maxQty'])  #最大下单量
            M_Taker = float(s2['filters'][1]['maxQty'])  #最大市价下单量

            symbols[symbol] = {
                'J': J,
                'J2': float(s2['filters'][0]['tickSize']),
                'L': L,
                'M': M, #市价最大下单数量
                'M_Taker': M_Taker, #市价最大下单数量
                'GangGan': SetGangGan,
                'maxJiazhi': 0,     #最大持仓价值
                'Min': 0,
                'Z': 1,
            }

        except Exception as e:
            log(symbol, s2, '数据异常', Color('', -1))
            uploadError(traceback.format_exc())


    return symbols




""" 获取Coinex交易规则"""
def GetCoinexSymbol(rest):
    symbolData = rest.GetSymbols()
    symbolData2 = rest.GetLimit()
    sets = {}

    for symbol in symbolData2:
        data = symbolData2[symbol]
        gg = 0
        for v in data:
            if SetGangGan <= int(v[1]):
                gg = v

            if not gg and SetGangGan >= int(v[1]):
                gg = v

        if not gg:
            log('Coinex 出bug了， 找不到杠杆', symbol)
            input("")

        data = 0
        for v in symbolData:
            if v['name'] == symbol:
                data = v
                break
        else:
            log(symbol, '未找到此交易对的规则')
            uploadLog(isExit=1)

        if not data['available']:
            log('Coinex', symbol, '市场禁止交易')

        sets[symbol] = {
            'J': WeiShu(data['tick_size']),
            'L': int(data['amount_prec']),
            'Z': float(data['multiplier']),
            'M': float(gg[0]),   #最大下单量
            'Min': float(data['amount_min']),
            'GangGan': min(int(gg[1]), SetGangGan),
            'maxLiang': float(gg[0]),   #最大持仓量
        }

    return sets


""" 获取Coinex现货交易规则"""
def GetCoinexSpotSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息
    ammSymbol = rest.GetAmmSymbols()

    sets = {}
    for symbol in symbolData2:

        swap = symbolData2[symbol]
        sets[swap['name']] = {
            'symbol': swap['name'],                        #合约交易对
            'J': int(swap['pricing_decimal']),        #用正则的方式提取价格位数
            'L': int(swap['trading_decimal']),                                        #数量小数位
            'C': 0,         #价格之间的差距
            'Z': 1,         #合约面值
            'Min': float(swap['min_amount']),          #最小下单量
            'GangGan': 1,          #最大杠杆
            'M': MAX,
            'maxJiazhi': 0,
            'amm': swap['name'] in ammSymbol,
        }

    return sets



""" 获取Gate交易规则"""
def GetGateSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息
    sets = {}
    for swap in symbolData2:
        if swap['in_delisting']:
            # print(swap['name'], '跳过...')
            continue

        sets[swap['name']] = {
            'symbol': swap['name'],                        #合约交易对
            'J': WeiShu(swap['order_price_round']),        #用正则的方式提取价格位数
            'L': 0,                                        #数量小数位
            'C': float(swap['order_price_round']),         #价格之间的差距
            'Z': float(swap['quanto_multiplier']),         #合约面值
            'M': float(swap['order_size_max']),            #最大下单量
            'Min': float(swap['order_size_min']),          #最小下单量
            'GangGan': min(SetGangGan, int(swap['leverage_max'])),          #最大杠杆
        }

    return sets


""" 获取GateSpot交易规则"""
def GetGateSpotSymbol(rest):
    symbolData = rest.GetSymbols()

    sets = {}
    for data in symbolData:
        sets[data['id']] = {
            'symbol': data['id'],                        #合约交易对
            'J': int(data['precision']),        #用正则的方式提取价格位数
            'L': int(data['amount_precision']),                                        #数量小数位
            'C': 0,         #价格之间的差距
            'Z': 1,         #合约面值
            'Min': float(data.get('min_base_amount', 0)),          #最小下单量
            'GangGan': 1,          #最大杠杆
            'M': MAX,
            'maxJiazhi': 0,
        }

    return sets


""" 获取Huobi交易规则"""
def GetHuobiSymbol(rest):

    symbolGg = rest.GetSymbolsGg()
    symbolPosLimit = rest.GetMaxPos()    #最大持仓量
    symbollimit = rest.GetSymbolsLimit('ioc')    #获取ioc最大下单量
    # symbollimit2 = rest.GetSymbolsLimit('lightning')    #获取闪电平仓最大下单量
    symbolSwapData = rest.GetSymbols()['data']      #合约信息

    sets = {}
    for swap in symbolSwapData:
        symbol = swap['contract_code'].replace('-', '')
        if swap['contract_status'] != 1:
            continue

        for v in symbolGg:
            if v['contract_code'] == swap['contract_code']:
                gg = min(int(v['available_level_rate'].split(",")[-1]), SetGangGan)
                if gg != SetGangGan:
                    print(symbol , gg)
                break
        else:
            print(symbol, "未找到杠杆")
            continue

        for v in symbollimit:
            if v['contract_code'] == swap['contract_code']:
                order = min(v['open_limit'], v['close_limit'])
                break
        else:
            print(symbol, "未找到ICO最大下单量！！！！！！！！！！！！！！！")
            continue

        for v in symbolPosLimit:
            if v['contract_code'] == swap['contract_code']:
                maxLiang = min(v['buy_limit'], v['sell_limit'])
                # log('Huobi', v['contract_code'], '最大持仓量', maxLiang, U(v['buy_limit_value'], kuo=1))
                break
        else:
            print(symbol, "未找到最大持仓量！！！！！！！！！！！！！！！！")
            continue

        # for v in symbollimit2:
        #     if v['contract_code'] == swap['contract_code']:
        #         order2 = min(v['open_limit'], v['close_limit'])
        #         break
        # else:
        #     print(symbol, "未找到闪电平仓最大下单量")
        #     continue


        sets[swap['contract_code']] = {
            'symbol': swap['contract_code'],             #合约交易对
            'J': WeiShu(swap['price_tick']),          #用正则的方式提取价格位数
            'C': 0, #价格之间的差距
            'L': 0,           #数量小数位
            'Z': float(swap['contract_size']),            #合约面值
            'Min': 1,                                #最小下单量
            'GangGan': min(SetGangGan, int(gg)),          #最大杠杆
            'M': order,#min(order, order2),                          #做大下单量
            'maxLiang': maxLiang,#min(order, order2),                          #做大下单量
        }

    return sets


""" 获取Bitget交易规则"""
def GetBitgetSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:
        if swap['symbolStatus'] != 'normal':
            # print(swap['symbol'], swap['symbolStatus'], '已下架，跳过')
            continue

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': int(swap['pricePlace']),        #用正则的方式提取价格位数
            'L': int(swap['volumePlace']),                                        #数量小数位
            'C': float(swap['priceEndStep']),         #价格之间的差距
            'Z': 1,         #合约面值
            'Min': float(swap['minTradeNum']),          #最小下单量
            'maxJiazhi': 0,
            'GangGan': SetGangGan,          #最大杠杆
            'is4hours': 8 / int(rest.GetRatePeriod(swap['symbol'])['ratePeriod'])
        }
        # time.sleep(0.1) #GetRatePeriod限平路额

    return sets




""" 获取Bitget现货交易规则"""
def GetBitgetSpotSymbol(rest):

    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:
        if swap['status'] != 'online':
            # print(swap['symbol'], swap['status'], '已下架，跳过')
            continue

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': int(swap['priceScale']),        #用正则的方式提取价格位数
            'L': int(swap['quantityScale']),                                        #数量小数位
            'C': 0,         #价格之间的差距
            'Z': 1,         #合约面值
            'Min': float(swap['minTradeAmount']),          #最小下单量
            'GangGan': 1,          #最大杠杆
            'M': MAX,
            'maxJiazhi': 0,
        }

    return sets



"""获取小数位数"""
def BybitWeiShu(val):
    val_str = str(val)
    if "." not in val_str:
        return 0

    #删除尾部0，float不行，会科学计数
    # rgx = re.compile(r'(?:(\.)|(\.\d*?[1-9]\d*?))0+(?=\b|[^0-9])')
    # val_str = rgx.sub(r'\2', val_str)

    digits_location = val_str.find('.')
    if digits_location:
        return len(val_str[digits_location + 1:])

    return 0


""" 获取Bybit交易规则"""
def GetBybitSymbol(rest):
    SetGangGan2 = rest.GangGan if hasattr(rest, 'GangGan') else SetGangGan

    symbolData2 = rest.GetSymbols()      #合约信息
    symbolPos = rest.GetSymbolsPosMax()      #合约信息

    sets = {}
    for swap in symbolData2:
        if swap['status'] != 'Trading':
            print(swap['symbol'], swap['status'], '已下架，跳过')
            continue

        if 'qtyStep' not in swap['lotSizeFilter']:
            print(swap['symbol'], swap, '数据异常')
            continue

        # if 'OCEAN' in swap['symbol']:
        #     pprint(swap)

        gg = min(int(float(swap['leverageFilter']['maxLeverage'])), SetGangGan2)
        maxJiazhi = 0
        maxId = 0
        tmpGg = 0
        for v in symbolPos:
            if v['symbol'] == swap['symbol']:
                if float(v['maxLeverage']) <= gg  and float(v['maxLeverage']) > tmpGg:
                    maxJiazhi = float(v['limit'])
                    tmpGg = float(v['maxLeverage'])
                    gg = min(gg, int(tmpGg))
                    maxId = int(v['id'])

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': BybitWeiShu(swap['priceFilter']['tickSize']),        #用正则的方式提取价格位数
            'L': WeiShu(swap['lotSizeFilter']['qtyStep']),                                        #数量小数位
            'C': 0,         #价格之间的差距
            'Z': 1,         #合约面值
            'Min': float(swap['lotSizeFilter']['minTradingQty']),          #最小下单量
            'GangGan': gg,          #最大杠杆
            'maxJiazhi': maxJiazhi,
            'riskId': maxId,
            # 'M': float(swap['lotSizeFilter']['maxTradingQty']),
            'M': float(swap['lotSizeFilter']['postOnlyMaxOrderQty']),
        }

    return sets



""" 获取Kucoin交易规则"""
def GetKuSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:
        if swap['status'] != 'Open':
            print(swap['symbol'], swap['status'], 'Ku已下架，跳过')
            continue

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': WeiShu(swap['tickSize']),        #用正则的方式提取价格位数
            'L': 0,                                        #数量小数位
            'Z': float(swap['multiplier']),         #合约面值
            'M': float(swap['maxOrderQty']),         #最大下单量
            'Min': float(swap['lotSize']),          #最小下单量
            'GangGan': min(int(float(swap['maxLeverage'])), SetGangGan),          #最大杠杆
            'maxJiazhi': 0,
        }

    return sets



""" 获取Okx交易规则"""
def GetOkxSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:
        if 'linear' != swap['ctType']:
            continue

        if 'live' != swap['state']:
            print(swap['instId'], swap['state'], 'Okx已下架跳过')
            continue

        sets[swap['instId']] = {
            'symbol': swap['instId'],                        #合约交易对
            'J': WeiShu(swap['tickSz']),        #用正则的方式提取价格位数
            'L': WeiShu(swap['lotSz']),                                        #数量小数位
            'Z': float(swap['ctVal']),         #合约面值
            'M': MAX,         #最大下单量
            'Min': float(swap['minSz']),          #最小下单量
            'GangGan': SetGangGan,          #最大杠杆
            'maxJiazhi': 0,
        }
        # pprint(sets[swap['instId']])

    return sets



""" 获取Mexc现货交易规则"""
def GetMexcSpotSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:

        if swap['status'] != 'ENABLED':
            print(swap['symbol'], swap['status'], 'MexcSpot已下架，跳过')
            continue

        if not swap['isSpotTradingAllowed']:
            # if "3LUSDT" not in swap['symbol']:
                # print(swap['symbol'], swap['isSpotTradingAllowed'], 'MexcSpot不允许Api现货，跳过')
            continue

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': swap['quotePrecision'],        #用正则的方式提取价格位数
            'L': swap['baseAssetPrecision'],                                        #数量小数位
            'Z': 1,         #合约面值
            'M': float(swap['maxQuoteAmount']),         #最大下单量
            'Min': float(swap['baseSizePrecision']),          #最小下单量
            'GangGan': 1,          #最大杠杆
            'maxJiazhi': 0,
        }

    return sets

""" 获取Dydx交易规则"""
def GetDydxSymbol(rest):
    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for s in symbolData2:
        swap = symbolData2[s]
        if swap['status'] != 'ONLINE':
            print(swap['market'], swap['status'], 'MexcSpot已下架，跳过')
            continue

        sets[swap['market']] = {
            'symbol': swap['market'],                        #合约交易对
            'J': WeiShu(swap['tickSize']),        #用正则的方式提取价格位数
            'L': WeiShu(swap['stepSize']),                                        #数量小数位
            'Z': 1,         #合约面值
            'M': float(swap['maxPositionSize']),         #最大下单量
            'Min': float(swap['minOrderSize']),          #最小下单量
            'GangGan': SetGangGan,          #最大杠杆
            'maxJiazhi': 0,
        }

    return sets



""" 获取Ku现货交易规则"""
def GetKuSpotSymbol(rest):

    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2:
        if not swap['enableTrading']:
            # print(swap['symbol'], swap['enableTrading'], '已下架，跳过')
            continue

        sets[swap['symbol']] = {
            'symbol': swap['symbol'],                        #合约交易对
            'J': WeiShu(swap['priceIncrement']),        #用正则的方式提取价格位数
            'L': WeiShu(swap['baseIncrement']),                                        #数量小数位
            'Z': 1,         #合约面值
            'Min': float(swap['baseMinSize']),          #最小下单量
            'GangGan': 1,          #最大杠杆
            'M': MAX,
            'maxJiazhi': 0,
        }

    return sets

def GetZoomexSymbol(rest):

    symbolData2 = rest.GetSymbols()      #合约信息

    sets = {}
    for swap in symbolData2["result"]["LinearPerpetual"]:
        symbol = swap['symbolName']
        if symbol == 'CHRUSDT':
            pprint(swap)
        sets[symbol] = {
            'symbol': symbol,                        #合约交易对
            'J': WeiShu(swap['minPrice']),        #用正则的方式提取价格位数
            'L': swap['tickSizeFraction'],                                        #数量小数位
            'Z': 1,         #合约面值
            'M': 0,         #最大下单量
            'Min': float(swap['minQty']),          #最小下单量
            'GangGan': min(SetGangGan, int(swap['fgridbotConfig']['maxLeverageE2']['1'] / 10)),          #最大杠杆
            'maxJiazhi': 0,
        }

    return sets


def GetDeepcoinSymbol(rest):
    # symbolData = rest.GetSymbols()
    symbolData2 = rest.GetSymbolsList()

    # symbolDataSwap = rest.GetSymbols('SWAP')
    sets = {}

    for data in symbolData2:
        symbol = data['InstrumentID']

        sets[symbol] = {
            'symbol': symbol,           #合约交易对
            'J': WeiShu(data['PriceTick']),    #用正则的方式提取价格位数
            'J2': float(data['PriceTick']),
            'L': 0, #WeiShu(data['lotSz']), #数量小数位
            'Z': float(data['VolumeMultiple']),         #合约面值
            'Min': float(data['MinOrderVolume']), #最小下单量
            'GangGan': float(data['DefaultLeverage']),#int(min(float(data['DefaultLeverage']), SetGangGan)),  #最大杠杆
            'M': 0, #float(data['MaxOrderVolume'])
            'maxJiazhi': 0,
        }

    return sets
