
""" 利用Websocket下单，非异步"""
from ws4py.client.threadedclient import WebSocketClient

import json
import ujson
import hmac
import time
import traceback
from main.hanshu import *
from main.config import *

class _okx_ws_trade(WebSocketClient):

    def opened(self):
        self.heartBeatHandler = Timer(10,self.xingtiao)
        
        timestamp = str(int(time.time()))
        message = timestamp + 'GET' + '/users/self/verify'
        mac = hmac.new(bytes(self.secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
        d = mac.digest()
        sign = base64.b64encode(d)
        login_param = {"op": "login", "args": [{"apiKey": self.access_id,
                                                "passphrase": self.passwd,
                                                "timestamp": timestamp,
                                                "sign": sign.decode("utf-8")}]}
        login_str = ujson.dumps(login_param)
        self.send(login_str)

        
    
    def xingtiao(self):
        self.send("ping")

    def closed(self, code, reason=None):
        if hasattr(self,"heartBeatHandler"): self.heartBeatHandler.cancel()

    """ 处理下单信息"""
    def _PostOrder(self, data):
        order = data['data'][0]
        cha = NowTime_ps() - int(order['clOrdId'])
        self.order[order['clOrdId']] = order

    def received_message(self, data):
        data = str(data)
        if 'pong' in data:
            return
    
        try:
            data = ujson.loads(data)
        except:
            return log('okx ws2', 'jspn解析失败', data, Color('', -1))


        tlog("[okx ws2] 心跳", data, 120 * 60) #2

        if 'op' in data and 'order' == data['op']:
            self._PostOrder(data)

        elif 'event' in data and data['event'] == 'error':
            log('okx ws2', 'login失败', data, Color('', -1))
            time.sleep(5)
            self.close(1001)


        """ 防止数据堆的太多影响查找速度"""
        if len(self.order) > 300:
            for i in list(self.order.keys()):
                del self.order[i]
                # print('del', i, GetTime(i, echoMs=1))
                if len(self.order) <= 100:
                    break

            
        

class okx_ws_trade():
    def __init__(self, rest) -> None:
        
        self.coinex = rest
        self.symbol = self.coinex.symbol
        t = threading.Thread(target=self.link, args=(rest, ))
        t.setDaemon(True)
        t.start()


    def link(self, rest):
        while True:
            try:
                tlog('[okx ws2] 连接...', '', 120)
                try:
                    url = 'ws://10.2.0.200/login'
                    print('Okx ws2 加速线路', url, OkxCelo)
                except:
                    url = 'wss://ws.okex.com:8443/ws/v5/private'

                self.ws = _okx_ws_trade(url)
                self.ws.order = {}
                self.ws.secret_key = rest.secret_key
                self.ws.access_id =  rest.access_id
                self.ws.passwd = rest.passwd
                self.ws.connect()
                self.ws.run_forever()

            except Exception as e:
                uploadError(traceback.format_exc())

            time.sleep(1)


    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):

        MZ = self.symbol[symbol]['Z'] if symbol in self.symbol else 1
        liang2 = str(liang)+' ('+STR_N(float(liang)*float(jiage)*MZ)+'$)'

        jiage = str(jiage)
        liang = str(int(liang))

        if type == 'normal':
            type = 'market'

        msg = 'OkxWS　'+symbol+' 　'

        if jiancang:
            msg += '平空' if side == 'BUY' else '平多'
        else:
            msg += '开多' if side == 'BUY' else '开空'


        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　面值:"+str(MZ)+"　减仓:"+str(jiancang)+"　Type:"+type


        iid = str(NowTime_ps())
        post = {
            'instId': symbol,  #交易对
            'tdMode': 'cross',      #保证金模式：全仓
            'ccy': self.coinex.ccyBi,
            'clOrdId': iid,
            'side': side.lower(),           #方向
            'ordType': type,        #模式
            'sz': liang,         #数量
        }
        
        if jiancang:
            post['posSide'] = 'short' if side == 'BUY' else 'long'
        else:
            post['posSide'] = 'long' if side == 'BUY' else 'short'

        msg += '　持仓:'+post['posSide']

        if type != 'market' and type != 'optimal_limit_ioc':
            post['px'] = jiage
            
        if jiancang:
            post['reduceOnly'] = True

        orderId = 0
        
        t = NowTime_ms()


        data = {
            "id": "1512",
            "op": "order",
            "args": [post]
        }
        data = ujson.dumps(data)
        self.ws.send(data)

        # Sleep(20)
        
        orderId = 0
        for i in range(1 * 10000 * 20):
            if iid in self.ws.order:
                order = self.ws.order[iid]
                if not order or 'ordId' not in order or not order['ordId']:
                    log(msg, "OKX下单失败："+str(order)+str(data), str(round((NowTime_ps()-int(iid)) / 1000, 1))+'ms', Color('', -1))

                else:
                    orderId = order['ordId']

                break

            Sleep(0.02)

        else:
            uploadError("OKXws订单未找到："+str(data))
            time.sleep(5)
            os._exit(0)

        msg = [msg+ '　'+Color(msg2, 1), str(round((NowTime_ps()-int(iid)) / 1000, 1))+'ms', orderId]
        
        # print(msg)
        return orderId, msg
    
