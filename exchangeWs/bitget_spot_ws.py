#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *

from moneyV2.money import changePos

def sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return str(base64.b64encode(d), 'utf8')

def pre_hash(timestamp, method, request_path):
    return str(timestamp) + str.upper(method) + str(request_path)


class bitget_spot_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        self.passwd = gate.passwd
        
        self.URL = 'wss://ws.bitget.com/spot/v1/stream'

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新订单"""
    def _update_order(self, data):
        # pprint(data)
        try:
            for data2 in data:
                id = data2['ordId']
                side = data2['side'].upper()
                close = '减仓' if side == 'SELL' else '开仓'
                status = data2['status']
                if 'avgPx' not in data2:
                    data2['avgPx'] = 0
                    data2['accFillSz'] = 0
                
                if 'px' not in data2:
                    data2['px'] = 0

                if 'sz' not in data2:
                    data2['sz'] = 0

                # 如果是部分成交，会推送cancelled，但是成交量是0，手动补充
                if status== 'cancelled' and id in self.orders:
                    data2['avgPx'] = self.orders[id]['jiage']
                    data2['accFillSz'] = self.orders[id]['liang']

                if status == 'partial-fill' and float(data2['sz']) == float(data2['accFillSz']):
                    status = 'full-fill'

                jiazhi = max(round(float(data2['avgPx']) * float(data2['accFillSz']), 2), 0.0001)
                self.orders[id] = {
                    'id': id,                  #交易对
                    'symbol': data2['instId'],                  #交易对
                    'status': status,                  #状态
                    'side': side,                               #方向
                    'type': data2['force'],                     #订单类型
                    'jiage': float(data2['avgPx']),             #累积成交均价
                    'liang': float(data2['accFillSz']),         #累积成交量
                    'yingkui': 0,             #盈亏
                    'jiazhi': jiazhi,  #价值
                    'sxf': 0,  #手续费
                    'time': int(data2['cTime']),                #推送时间
                }

                # pprint(data)
                # pprint(self.orders[id])
                # print("------------------")

                msg = ['[Bitget现货监听订单]', GetTime(echoMs=1), '', close, side, data2['instId'], data2['ordType'], data2['force'],
                "　ID", id, data2['status'],
                '　价格', data2['px'], '数量', str(data2['sz']) + ' ('+N_STR(float(data2['px'])*float(data2['sz']), 2)+'$)',
                '　成交价', data2['avgPx'], '成交量', str(data2['accFillSz']) + ' ('+N_STR(jiazhi, 2)+'$)'
                '　手续费', 0]

                if float(data2['accFillSz']):
                    log(*msg)

                    """ 这里不能用status变量 否则会重复两次
                    bitget现货会收手续费，推算的不准，用ws的pos更新算了~
                    """
                    # if data2['status'] in self.order_status:
                    #     changePos(self, data2['instId'], side, data2['avgPx'], data2['accFillSz'])

                else:
                    print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())



    """ 更新余额"""
    def _update_account(self, data):
        
        for v in data:
            if v['coinName'].upper() in [CcyBi, CcyBi2]:
                self.keyong = N(float(v['available']), 4)
                self.yingkui = 0
                self.keyong = self.keyong if self.keyong else 0.01

                if self.keyong != self.preUsdt:
                    print('[Bitget现货余额更新]', self.keyong)

                self.preUsdt = self.keyong

            # 不兼容            
            else:
                symbol = v['coinName'].upper()+'USDT_SPBL'
                for i in range(0, len(self.pos)):
                    if self.pos[i]['symbol'] == symbol:
                        self.pos[i]['liang'] = float(v['available'])
                        self.pos[i]['time'] = NowTime_ms()
                        break
                else:

                    ok = {}
                    ok['symbol'] = symbol
                    ok['liang'] = float(v['available'])
                    if not ok['liang']:
                        continue
                    ok['side'] = 'BUY'
                    ok['side2'] = 'SELL'
                    ok['jiage'] = 0
                    ok['nowJiage'] = 0
                    ok['yingkui'] = 0
                    ok['bzj'] = 0
                    ok['roe'] = 0
                    ok['time'] = NowTime_ms()

                    self.pos.append(ok)
        


    """ 更新盘口和费率信息"""
    def _update_data(self, data):
        for v in data:
            s = v['instId']+'_SPBL'
            data= {
                'bidPx': float(v['bestBid']),
                'askPx': float(v['bestAsk']),
                'bidQty': float(v['bidSz']),
                'askQty': float(v['askSz']),
                'maxLiang': MAX,
                'feilv': 0,
                't': NowTime_ms()
            }
            if self.depathCallback:
                self.depathCallback(s, data)

            self.data[s] = data


    async def ping(self, conn):
        while True:
            await conn.send_str('ping')
            await asyncio.sleep(5)


    async def main(self, symbols=[]):
        while True:
            # log("Bitget现货 准备连接ws", symbols)
            try:

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            self.URL,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:

                    channels = []

                    if symbols:
                        for symbol in symbols:
                            channels += [
                                {
                                    "instType": "SP",
                                    "channel": "ticker",
                                    "instId": symbol.replace('_SPBL', '')
                                }
                            ]

                    else:
                        # 先登录
                        timestamp = str(int(time.time()))
                        sign_str = sign(pre_hash(timestamp, "GET", '/user/verify'), self.secret_key)
                        data = {
                            "op":"login",
                                "args":[
                                    {
                                        "apiKey":self.access_id,
                                        "passphrase":self.passwd,
                                        "timestamp":timestamp,
                                        "sign":sign_str
                                    }
                                ]
                        }
                        await conn.send_str(ujson.dumps(data))
                        res = await conn.receive(timeout=30)
                        log('Bitget现货 登录ws', res.data)

                        
                        # 先登录
                        channels += [
                            {
                                "instType": "spbl",
                                "channel": "account",
                                "instId": "default"
                            },
                            {
                                "channel": "orders",
                                "instType": "spbl",
                                "instId": "default"
                            }
                        ]


                    for i in channels:
                        sub_str = ujson.dumps({"args": [i], "op":"subscribe"})
                        await conn.send_str(sub_str)

                    asyncio.create_task(self.ping(conn))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('Bitget ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Bitget ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        if msg == 'pong':
                            continue

                        try:
                            msg = ujson.loads(msg)
                            # pprint(msg)
                        except:
                            print('解析失败: '+str(msg))
                            break

                        if 'data' in msg:
                            
                            if 'ticker' in msg['arg']['channel']:self._update_data(msg['data'])
                            elif 'account' in msg['arg']['channel']:self._update_account(msg['data'])
                            elif 'orders' in msg['arg']['channel']:self._update_order(msg['data'])
                            else: pprint(msg)
    

            except Exception as e:
                log(f'Gate ws连接失败 开始重连...', traceback.format_exc())

                #强制重启
                os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")