from gc import set_debug
import os
import re
import time
import json
import requests
import base64
import threading        #多线程
import traceback        #输出报错
from urllib.parse import urlencode
from pprint import pprint
from decimal import Decimal

from main.config import *

RunTime = int(time.time())
q = []
qTable = []
MAX = 9999999999999999999

# 必须用str，否则会变成很多小数位
def D(v):
    return Decimal(str(v))

"""ws中的定时执行"""
class Timer():
    def __init__(self,delay,fun):
        self.delay,self.f=delay,fun
        self.t=threading.Timer(self.delay,self.fun)
        self.t.start()

    def fun(self):
        if self.f: self.f()
        self.t=threading.Timer(self.delay,self.fun)
        self.t.start()
    def cancel(self):
        self.t.cancel()

"""文件追加内容"""
def zhuijiaFile(filename,data):
    f = open(filename, 'a+',encoding='utf-8')
    f.write(data+"\n")
    f.close()

'''获取本地ip'''
DuoIp = []
# import netifaces as ni
# print('检测服务器网络配置')
# for dev in ni.interfaces():
#     print('dev:',dev)
#     if 'ens' in dev or 'eth' in dev or 'enp' in dev:
#         if len(ni.ifaddresses(dev)) > 2:
#             # print(ni.ifaddresses(dev))
#             for i in ni.ifaddresses(dev)[2]:
#                 ip=i['addr']
#                 print(f"检测到私有ip:{ip}")
#                 if ip not in DuoIp:
#                     DuoIp.append(ip)
# print(f"当前服务器私有ip为{DuoIp}")


class glo():
    ServerCount = False
    Stop = 0
    StopTrade = 0
    StopKai = 0
    Yuer = 0
    nowYuer = 0.1
    preYuer = 0.1   #开仓前余额
    Pingcang = 0
    Huazhuan = 0
    preHuazhuan = 0
    Huazhuan2 = 0
    preHuazhuan2 = 0
    ganggan = GangGan
    SideTime = 0
    SideTime2 = 0
    SideData = {}

    trades = 0
    preTrades = 0

    useDomain2 = '-'
    domain = 'https://rich.wusisanba.com'



""" 文件写入内容"""
def write(filePath,data):
    dirPath = os.path.dirname(filePath)

    if not os.path.exists(dirPath):
        os.makedirs(dirPath)

    f = open(filePath, 'w+',encoding='utf-8')
    f.write(data)
    f.close()


"""文件追加内容"""
def zhuijia(filename, data):
    f = open(filename, 'a+',encoding='utf-8')
    f.write(data+"\n")
    f.close()


""" 上传错误日志"""
def uploadError(error):
    try:
        if NoUpload:
            return print(error)
    except:
        pass

    log(error)
    print("----------")

    if not tlog("上传错误日志", '', 10, xs=0):
        return False

    filePath = '/root/error/'+time.strftime("%Y-%m-%d_%H_%M_%S", time.localtime())+'.txt'
    write(filePath, error)

    url = f'{glo.domain}/api/log/addError?key=asdos1123399xxx'
    data = {'serverName': ServerName, 'data': base64.b64encode(error.encode())}

    #重复尝试上传十次，每次间隔1秒
    for x in range(10):
        fanhui = False
        try:
            fanhui = requests.post(url,data=data, timeout=30)
            fJson = fanhui.json()
            if fJson['status']:
                print(fJson)
                return True
            else:
                print(fJson)

        except Exception as e:
            try:
                print(fanhui.text)
            except:
                pass
            print(url+'&'+urlencode(data))
            traceback.print_exc()

        time.sleep(1)

    print("----------")
    time.sleep(3)
    return True



def uploadLog(isExit=0):
    if isExit:
        log(Color('重启策略...', -1))
        os._exit(0)

    return
    try:
        if NoUpload:
            return
    except:
        pass
    global q, qTable, RunTime

    while 1:
        if q == [] and qTable == []:
            time.sleep(0.1)
            if isExit:
                os._exit(0)
            else:
                continue

        else:
            qTmp = q
            qTmp.reverse()  #倒序
            qq = q

            if qTable != []:
                qq.append(qTable)

            q = []
            qTable = []

            data = {'serverName': ServerName, 'runTime': RunTime, 'sideTime': glo.SideTime, 'data': base64.b64encode(json.dumps(qq).encode())}
            for x in range(2):
                url = f'{glo.domain}/api/log?key=asdos1123399xxx' if not x else f'https://rich.sumubai.cc/api/log?key=asdos1123399xxx'


                fanhui = False
                try:
                    fanhui = requests.post(url, data=data, timeout=10)
                    fJson = fanhui.json()

                    if glo.useDomain2 == '-':
                        glo.useDomain2 = 0

                    if fJson['status']:

                        try:
                            try:
                                glo.Yuer = float(fJson['data']['editYuer'])
                            except:
                                glo.Yuer = fJson['data']['editYuer']

                            glo.Stop = int(fJson['data']['stop'])
                            glo.StopTrade = int(fJson['data']['stopTrade'])
                            glo.StopKai = int(fJson['data']['stopKai'])
                            glo.Pingcang = fJson['data']['pingcang']
                            glo.Huazhuan = float(fJson['data']['huazhuan'])


                        except:
                            pass

                        #print(glo.Huazhuan2)
                        Reboot = int(fJson['data']['reboot'])
                        if Reboot:
                            log('云端重启', Color('', -1))
                            os._exit(0)

                    else:
                        print(fJson)

                    break

                except Exception as e:
                    if glo.useDomain2 == '-':
                        glo.useDomain2 = 1
                        glo.domain = 'https://rich.sumubai.cc'

                    try:
                        print(fanhui.text)
                        for v in qTmp:  #异常还原
                            if len(q) < 200 or (v['name'] != 'log' and v['name'] != 'table'):
                                q.append(v)
                    except:
                        pass
                    print(url+'&'+urlencode(data))
                    traceback.print_exc()

                    time.sleep(3)

            if isExit:
                os._exit(0)
            time.sleep(5)


# t = threading.Thread(target=uploadLog)
# t.setDaemon(True)
# t.start()

def logTable(data, d={}):
    global q, qTable

    l = list(d.keys())
    if 'limit' in l:
        del d['limit']
    if 'pos' in l:
        del d['pos']
    if 'gg_bian' in l:
        del d['gg_bian']
    if 'gg_coinex' in l:
        del d['gg_coinex']
    if 'tongji' in l:
        del d['tongji']
    if 'pos_time' in l:
        del d['pos_time']
    if 'usdt' in l:
        del d['usdt']
    if 'usdc' in l:
        del d['usdc']

    qTable = {'time': NowTime_ps(), 'name': 'table', 'data': [data, d]}

def logProfit(data):
    global q
    q.append({'time': NowTime_ps(), 'name': 'profit', 'data':data})

def logPos(data):
    global q
    q.append({'time': NowTime_ps(), 'name': 'pos', 'data':data})

def logTrades(data):
    global q
    q.append({'time': NowTime_ps(), 'name': 'trades', 'data':data})

def logBaocang(data):
    global q
    q.append({'time': NowTime_ps(), 'name': 'baocang', 'data':data})

def log(*args):
    global q
    q.append({'time': NowTime_ps(), 'name': 'log', 'data': toMsg([*args])})
    try:
        print('[-]',GetTime(echoMs=1),'',*args)
    except:
        print('[-] Error',*args)

def logT(t, args):
    global q
    q.append({'time': t, 'name': 'log', 'data': toMsg([*args])})
    try:
        print('[-]',GetTime(echoMs=1),'',*args)
    except:
        print('[-] Error',*args)

def logWait(*args):
    log('暂停策略：',*args)
    time.sleep(60 * 60 * 24 * 10)

Log_time = {}
""" 每N秒打印一次日志"""
def tlog(data, msg, xsTime=0, xs=1, look=0):

    if isinstance (data,list):
        data = toMsg(data)

    if isinstance (msg,list):
        msg = toMsg(msg)

    if data not in Log_time.keys():
        Log_time[data] = 0

    nowTime = NowTime()
    if nowTime - Log_time[data] >= xsTime:
        if data == '盈利画图':
            pass#printProfit(msg)
        elif xs:
            log(data,msg)

        if not look:    #只查询
            Log_time[data] = nowTime

        return 1

    return 0

""" 延迟 毫秒单位"""
def Sleep(t):
    if not t:
        tlog('跳过等待...', '', 5)
    else:
        time.sleep(t/1000)

""" 秒级时间戳"""
def NowTime():
    return int(time.time())

""" 毫秒级时间戳"""
def NowTime_ms():
    return int(round(time.time() * 1000))

""" 微秒级时间戳"""
def NowTime_ps():
    return int(round(time.time() * 1000000))

""" 分割数组"""
def list_split(items, n):
    return [items[i:i+n] for i in range(0, len(items), n)]

"""获取当前分钟时间戳"""
def GetMin():
    time_stamp = time.strftime('%Y-%m-%d-%H-%M',time.localtime(time.time()))
    time2 = time.strptime(time_stamp, "%Y-%m-%d-%H-%M")
    time2 = int(time.mktime(time2))
    return time2

""" 时间戳转换"""
def GetTime(x=0,style="%H:%M:%S", echoMs=0, echoDay=0):
    if not x:
        x = NowTime_ms()
    if echoDay:
        style = "%m-%d %H:%M:%S"

    str_x = str(x)
    s = int(str_x[:10])
    time_local = time.localtime(s)
    dt = time.strftime(style,time_local)
    if echoMs:
        dt += '.'+str_x[-3:]
    return dt

""" 相差时间"""
def GetChaTime(kTime, nowTime=0):
    if not nowTime:
        nowTime = NowTime()
    kTime = int(str(kTime)[:10])
    nowTime = int(str(nowTime)[:10])
    cha_day = round((nowTime - kTime) / 86400) if nowTime - kTime > 86400 else 1
    str_d = int((nowTime - kTime) / 86400)
    str_h = round((nowTime - kTime - str_d*86400) / 3600)
    return [cha_day, str(str_d)+'天 '+str(str_h)+'小时']

"""
python实现 数值 转换为 万，亿单位，保留3位小数
"""
def str_of_num(num):
    '''
    递归实现，精确为最大单位值 + 小数点后三位
    '''
    def strofsize(num, level):
        if level >= 2:
            return num, level
        elif num >= 10000:
            num /= 10000
            level += 1
            return strofsize(num, level)
        else:
            return num, level
    units = ['', '万', '亿']
    num, level = strofsize(num, 0)
    if level > len(units):
        level -= 1
    return '{}{}'.format(round(num, 1), units[level])


""" 数据自动换颜色"""
def Color(msg, data=None, msg2='', html=0):
    if data == None:
        data = msg

    msg = str(msg) + msg2
    if not html:
        msg += '#FF4500' if data < 0 else '#32CD32'
    else:
        msg = f'<span style="color: #FF4500">{msg}</span>' if data < 0 else f'<span style="color:#32CD32">{msg}</span>'

    return msg

"""获取开仓比例"""
def GetBili(data):
    bili = data['kai'] if data and 'kai' in data.keys() else Kai_Bili
    try:
        if JiQun and len(JiQun):
            bili *= len(JiQun)
    except:
        pass
    return bili


""" 开仓量"""
def GetKaiLiang(yuer, jiage, ws, data=0):
    if not jiage:
        return 0

    ok = yuer * GetBili(data) / jiage / 100
    return N(ok, ws)



""" Okx开仓量"""
def GetKaiOKX(yuer, jiage, mz):
    if not jiage:
        return 0

    ok = yuer * Kai_Bili / jiage / 100 / mz
    return int(round(ok))


""" 数组转换msg"""
def toMsg(list1):
    return " ".join('%s' %id for id in list1)


"""获取小数位数"""
def WeiShu(val):

    if float(val) >= 1:
        return 0

    if val == 1e-01:
        return 1
    elif val == 1e-02:
        return 2
    elif val == 1e-03:
        return 3
    elif val == 1e-04:
        return 4
    elif val == 1e-05:
        return 5
    elif val == 1e-06:
        return 6
    elif val == 1e-07:
        return 7
    elif val == 1e-08:
        return 8
    elif val == 1e-09:
        return 9
    elif val == 1e-10:
        return 10

    val_str = str(val)
    if "." not in val_str:
        return 0

    #删除尾部0，float不行，会科学计数
    rgx = re.compile(r'(?:(\.)|(\.\d*?[1-9]\d*?))0+(?=\b|[^0-9])')
    val_str = rgx.sub(r'\2', val_str)

    digits_location = val_str.find('.')
    if digits_location:
        return len(val_str[digits_location + 1:])

    return 0


"""四舍五入缩写"""
def R(x, t=2):
    x = float(x)
    if not x:
        return x
    if not t:
        return int(round(x, 0))

    return round(x, t)

""" 不四舍五入"""
def N(x,t=3):
    if not t:
        return int(round(x))

    x = float(x)
    c = '{:.'+str(t)+'f}'
    return float(c.format(x))

""" 刀了"""
def U(x, t=1, kuo=0):
    try:
        x = float(x)
    except:
        return x

    data = str(round(x, t))+'$'
    if kuo:
        data = ' ('+data+')'
    return data

""" 小数转字符串，防止科学计数"""
def N_STR(x, t=3):

    x = float(x)

    if not t:
        return "%d"%x

    return f"%.{t}f"%x

""" 不四舍五入 返回字符串"""
def STR_N(x,t=2):
    x = float(x)
    c = '{:.'+str(t)+'f}'
    return c.format(x)

"""通过截取字符串的方式不四舍五入
4-16 可能会截取失败，不应该用来处理交易量
"""
def Si(x, y=1):
    x = float(x)
    if not y:
        x2 = int(x)
        return x2

    strx = str(x)
    if '.' in strx and 'e' not in strx:
        x2 = strx.split('.')[0] + '.' + strx.split('.')[1][:y]
        x2 = float(x2)

    else:
        x2 = round(float(x), y)

    return x2 #防止为0


""" 字符串百分比"""
def StrBi(data, v=2, kuo=0):
    if kuo:
        return ' ('+STR_N(data * 100, v)+'%)'
    return STR_N(data * 100, v)+'%'


""" 根据最新价格重新计算回报率"""
def GetRoe(ping_jiage, pos, mz=1):
    if not pos:
        return [0, 0]

    cc = abs(pos['liang']) * mz

    yingkui = (cc * ping_jiage) - (cc * pos['jiage']) if pos['side'] == "BUY" else (cc * pos['jiage']) - (cc * ping_jiage)
    yingkui = round(yingkui,4)

    if 'bzj' in pos.keys() and pos['bzj'] > 0:
        #这里不能用字符串，因为要用到这个值
        roe = N(yingkui / pos['bzj'] * 100,2) #回报率计算：盈亏 / 保证金 * 100
    else:
        roe = 0
    return [yingkui, roe]



""" 根据回报率计算价格"""
def GetRoeJia(roe, pos, ws=''):
    if roe == 0:
        return 0

    roe = roe / 100
    jia1 = pos['jiage'] * (1 + abs(roe))
    jia2 = pos['jiage'] * (1 - abs(roe))
    if pos['side'] == "BUY":
        jiage = jia2 if roe < 0 else jia1

    else:
        jiage = jia1 if roe < 0 else jia2

    return round(jiage, ws if ws != '' else WeiShu(pos['jiage']))


""" 获取强停余额"""
def GetStop(yuer, bili=0):
    if QiangPing:
        stop = round(yuer*QiangPing if QiangPing < 1 else QiangPing, 2)
        if not bili:
            return stop
        elif QiangPing < 1: #显示止损比例
            return str(stop)+' ('+str(int(QiangPing*100))+'%)'
    else:
        return 0

""" 是否强停"""
def IsStop(nowYuer, yuer):
    if nowYuer and QiangPing and nowYuer < GetStop(yuer):
        return 1
    else:
        return 0

""" 策略ID= 名称+交易对+K线周期"""
def GetSideId(seting):
    return seting['name'] + '_' + seting['symbol'] + '_' + seting['kTime'] + '_' + str(seting['buy']) + str(seting['sell'])

""" 根据策略ID提取Symbol"""
def toSideId(sideId):
    return sideId.split("_")[1] if '_' in sideId else ''


""" 从所有持仓里快速找到指定交易对"""
def GetPos(symbol, posAll, side=0):
    pos = []
    try:
        for tmp in posAll:
            if tmp:
                if type(tmp) != dict:
                    tmp = posAll[tmp]

                if tmp['symbol'] == symbol:
                    if not side  or side == tmp['side']:
                        pos = tmp.copy()
                        break

    except Exception as e:
        traceback.print_exc()

    return pos


""" 从所有持仓里快速删除指定交易对"""
def DelPos(symbol, posAll):
    if type(posAll) == dict:
        if symbol in posAll.keys():
            del(posAll[symbol])
        return posAll

    else:
        pos = []
        for tmp in posAll:
            if tmp['symbol'] != symbol:
                pos.append(tmp)
        return pos

""" 浮亏状态的持仓数量"""
def GetPosCount(posAll):
    if not posAll:
        return 0

    i = 0
    for pos in posAll:
        if pos and pos['roe'] / GangGan < -0.01: #跌了0.01才算
            i += 1
    return i


""" 持仓信息省略输出"""
def PosInfo(posAll):
    ok = []
    names = ['symbol', 'side', 'jiage', 'nowJiage', 'yingkui', 'roe', 'liang']
    for pos in posAll:
        if pos:
            tmp = {}
            for name in names:
                if name in pos.keys():
                    tmp[name] = pos[name]

            if tmp:
                ok.append(tmp)

    return ok


"""挂单偏移"""
def PianYi(Jia, side, ws):
    minws = 1
    for i in range(ws):
        minws /= 10

    if side == 'BUY':
        return N(Jia + minws, ws)

    if side == 'SELL':
        return N(Jia - minws, ws)

"""对内容进行截取并*比例"""
def JieQu(data, bili):
    data2 = data.split(" (")
    if len(data2) > 1:
        ok = STR_N(float(data2[0]) * bili, 2)
        data = data.replace(data2[0], ok)
    return data


"""保存data到本地"""
def SaveJson(json_path, data):

    # print("保存Json", json_path)
    if not json_path:
        return False

    with open(json_path, 'w') as file_obj:
        json.dump(data, file_obj)

    return data


""" 设置开仓"""


def addPos(self, symbol, jiage, liang, J2, side=0):
    if liang <= 0:
        return

    side = side if side else 'BUY'

    symbol2 = side+symbol
    if symbol2 in self.api.d['pos'].keys():
        prePos = self.api.d['pos'][symbol2]
        if 'liang' not in prePos:
            prePos['liang'] = 0

        preJiazhi = prePos['jiage'] * prePos['liang']
        nowJiazhi = jiage * liang
        nowJiage = R((preJiazhi + nowJiazhi) / (prePos['liang'] + liang), J2)
        log(symbol,
            '加仓', '价格', N(jiage, J2), '量', liang, U(nowJiazhi),
            '---> 上次持仓价格', N(prePos['jiage'], J2), '量', prePos['liang'], U(preJiazhi), '-->', '持仓均价', nowJiage, Color('', 1))

        jiage = nowJiage
        liang += prePos['liang']

    else:
        log(symbol, '当前价格', jiage, '量', liang,
            '价值', U(jiage*liang), Color('成功开仓', 1))

    # pos = GetPos(symbol, self.ws2.pos, side)
    # if pos:
    #     liang = max(pos['liang'], liang)

    posSave = {
        'symbol': symbol,
        'jiage': jiage,
        'liang': liang,
        'create_time': NowTime(),
    }

    self.api.d['pos'][symbol2] = posSave
    self.api.SaveJson()


""" 删除仓位"""


def delPos(self, symbol, side=0):
    side = side if side else 'BUY'

    symbol2 = side+symbol
    if symbol2 in self.api.d['pos']:
        del self.api.d['pos'][symbol2]
        log(symbol, Color('成功平仓', 1))
    else:
        log(symbol, Color('仓位不存在', -1))

    self.api.SaveJson()

"""读取本地文件数据"""
def SetJson(json_path, data):
    if not json_path:
        return False

    if not os.path.exists(json_path):
        return SaveJson(json_path, data)

    with open(json_path) as file_obj:
        datas = json.load(file_obj)
        # log("读取Json", json_path, datas)
        for k in datas.keys():
            data[k] = datas[k]

    return data

"""按指定数量分割数组"""
def arr_size(arr, size):
    try:
        s=[]
        for i in range(0,int(len(arr))+1,size):
            c=arr[i:i+size]
            if c:
                s.append(c)
    except:
        log('分割出错', arr, size)
        uploadLog(isExit=1)

    return s


def SideMsg(side):
    return '▲多#32CD32' if side == 'BUY' else '▼空#FF4500'    #方向

def X(s):
    try:
        if Exchange == 'Bybit' or Exchange2 == 'Bybit':
            return 1
    except:
        pass

    return 1000 if '1000' == s[:4] else 1



class MyThread(threading.Thread):
    def __init__(self, func, args=()):
        super(MyThread, self).__init__()
        self.func = func
        self.args = args

    def run(self):
        self.result = self.func(*self.args)

    def get_result(self):
        try:
            return self.result
        except:
            return []
### 反向SIDE
def fanSide(side):
    if side != 'BUY' and side != 'SELL':
        return side

    return 'SELL' if side=='BUY' else 'BUY'




""" 获取ApiKey"""
TMPAPIKEY = {}
TMPAPIKEY_File = SetJson('/root/TMPAPIKEY.json', {})
def GetApiKey(name, exchange):
    global TMPAPIKEY, TMPAPIKEY_File

    if name+exchange in TMPAPIKEY:
        # log('ApiKey使用缓存', name, exchange)
        return TMPAPIKEY[name+exchange]

    data = {'name': name, 'exchange': exchange}

    #重复尝试上传十次，每次间隔1秒
    for x in range(10):
        fanhui = False
        try:
            url = 'https://rich.sumubai.cc/api/Qian/getApiKey?key=asdos1123399xxx'
            fanhui = requests.post(url, data=data, timeout=30)
            fJson = fanhui.json()
            if fJson['status']:
                fJson = fJson['data']
                okdata = [fJson['api1'], fJson['api2'], fJson['pass'], fJson['cookie'], fJson['ua']]
                TMPAPIKEY[name+exchange] = okdata
                print('ApiKey获取成功', name, exchange, fJson['api1'][:10] if fJson['api1'] else '')
                TMPAPIKEY_File[name+exchange] = okdata
                SaveJson('/root/TMPAPIKEY.json', TMPAPIKEY_File)
                return okdata
            else:
                log('未配置ApiKey', name, exchange)
                time.sleep(3)
                uploadLog(isExit=1)

        except Exception as e:
            try:
                print(fanhui.text)
            except:
                pass
            print(url+'&'+urlencode(data))
            traceback.print_exc()

            if name+exchange in TMPAPIKEY_File:
                log('服务器挂壁了 ApiKey使用文件缓存', name, exchange)
                return TMPAPIKEY_File[name+exchange]

        time.sleep(1)

    return False
