#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import ujson
import hmac
import time
import hashlib
import traceback
from main.config import *
from main.hanshu import *
from moneyV2.money import changePos


class bian_spot_swap_ws():
    def __init__(self, bnb, depathCallback=0, symbol=0, ccyBi=0, orderCallback=0):
        self.data = {}
        self.orders = {}
        self.bnb = bnb
        self.symbol = symbol      #逐仓
        self.expired_time = 300
        self.depathCallback = depathCallback
        self.orderCallback = orderCallback
        self.ccyBi = ccyBi if ccyBi else CcyBi

        all = self.bnb.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0
        self.pos = {}
    

    """ 更新费率"""
    def _update_feilv(self, msg):
        msg = ujson.loads(msg)['data']
        s = msg['s']
        if s not in self.data:
            self.data[s] = {}
        self.data[s]['feilv'] = float(msg['r']) * 100


    """ 更新盘口信息"""
    def _update_depth(self, msg):
        msg = msg['data']
        s = msg['s']
        if s not in self.data:
            self.data[s] = {'feilv': 0}
        
        self.data[s]['bidPx'] = float(msg['b'])
        self.data[s]['bidQty'] = float(msg['B'])
        self.data[s]['askPx'] = float(msg['a'])
        self.data[s]['askQty'] = float(msg['A'])
        self.data[s]['t'] = NowTime_ms()

        if self.depathCallback:
            self.depathCallback(s, self.data[s], 'BianSpotSwap')


    """ 更新订单"""
    def _update_order(self, data2):
        try:
            if data2['X'] == 'EXPIRED' and data2['i'] in self.orders and self.orders[data2['i']]['jiage']:
                data2['L'] = self.orders[data2['i']]['jiage']

            symbol = data2['s']
            data2['n'] = float(data2['n']) if 'n' in data2 else 0
            jiage =  float(data2['L']) if float(data2['L']) else float(data2['p'])
            self.orders[data2['i']] = {
                'id': data2['i'],            #交易对
                'symbol': symbol,            #交易对
                'status': data2['X'],            #状态
                'jiage': jiage,      #价格
                'liang': float(data2['z']),      #数量
                'side': data2['S'],              #方向
                'yingkui': 0,   #盈亏
                'time': data2['E'],              #推送时间
            }

            name = '开空' if data2['S'] == 'SELL' else '开多'

            jiazhi = float(data2['p'])*float(data2['q'])
            jiazhi2 = float(data2['z'])*float(data2['L'])
            msg = [GetTime(echoMs=1), f'[币安现货]', symbol, name, data2['o'], data2['f'], "　ID", data2['i'], data2['X'], '　方向', data2['S'],
            '　价格', data2['p'],'　数量', data2['q']+U(jiazhi, kuo=1),
            '　成交价格', jiage,'　成交数量', str(data2['z'])+U(jiazhi2, kuo=1),'　手续费', data2['n'], data2['N']]

            if jiazhi2:
                del msg[0]
                log(*msg)

                if data2['X'] in ['FILLED', 'EXPIRED']:
                    changePos(self, symbol, data2['S'], data2['L'], data2['z'])

            else:
                print(*msg)

            if self.orderCallback:
                self.orderCallback(self.orders[data2['i']])
                
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新余额"""
    def _update_account(self, data):
        
        for v in data:
            if v['a'] == self.ccyBi:
                self.keyong = N(float(v['f']), 4)
                self.yingkui = 0
                self.keyong = self.keyong if self.keyong else 0.01

                # if self.keyong != self.preUsdt:
                #     print('[币安现货可用余额更新]', self.keyong)

                self.preUsdt = self.keyong

            # 不兼容            
            # else:
            #     symbol = v['a']+self.ccyBi
            #     liang = abs(float(v['f'])) + abs(float(v['l']))
            #     side = 'BUY' if float(v['f']) > 0 else 'SELL'

            #     for i in range(0, len(self.pos)):
            #         if self.pos[i]['symbol'] == symbol:
            #             self.pos[i]['liang'] = liang
            #             self.pos[i]['time'] = NowTime_ms()
            #             break
            #     else:

            #         ok = {}
            #         ok['symbol'] = symbol
            #         ok['liang'] = liang
            #         if not ok['liang']:
            #             continue
            #         ok['side'] = side
            #         ok['side2'] = fanSide(side)
            #         ok['jiage'] = 0
            #         ok['nowJiage'] = 0
            #         ok['yingkui'] = 0
            #         ok['bzj'] = 0
            #         ok['roe'] = 0
            #         ok['time'] = NowTime_ms()

            #         self.pos.append(ok)
        


    async def ping(self, conn):
        while 1:
            await conn.send_str('pong')
            await asyncio.sleep(10)


    async def main(self, symbols=[]):
        while True:
            # log("币安 准备连接ws", symbols)
            try:
                url = 'wss://stream.binance.com:9443'

                urls = ''
                if symbols:
                    for symbol in symbols:
                        urls += symbol.lower()+"@bookTicker/"
                    urls = urls.strip('/')

                    url += "/stream?streams="+urls

                else:
                    listen_key = self.bnb.GetlistenKey(self.symbol)
                    listenKeyTime = time.time()
                    url += f"/ws/{listen_key}"
                    log(f'[币安{self.symbol}Ws] 连接...', url)

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            # receive_timeout=30,
                ) as conn:
                    # 不能发错误信息 否则后面的消息全是none
                    # asyncio.create_task(self.ping(conn))

                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30 if symbols else 3 * 60) #
                        except asyncio.CancelledError:
                            log('ws取消')
                            return
                        except asyncio.TimeoutError:
                            log(f'币安 ws长时间没有收到消息 准备重连...')
                            break
                        except:
                            print(traceback.format_exc())
                            log(f'币安 ws出现错误 准备重连...')
                            time.sleep(3)
                            break
                        
                        msg = msg.data

                        if not msg:
                            print(f'币安spot ws收到空白消息...')
                            continue

                        if 'ping' in msg:
                            await conn.send_str('pong')
                            continue
                        
                        try:
                            msg = ujson.loads(msg)
                        except:
                            print(f'币安spot 消息解析失败', msg)
                            continue

                        if symbols:
                            if 'stream' not in msg:
                                continue

                            if 'bookTicker' in msg['stream']:
                                self._update_depth(msg)

                        else:

                            if msg['e'] == 'outboundAccountPosition':
                                self._update_account(msg['B'])

                            elif msg['e'] == 'executionReport':
                                self._update_order(msg)

                        if not symbols:
                            if time.time() - listenKeyTime > 60*15: # 每15分钟续一次
                                log('[币安Ws] 续期listenKey', self.bnb.PutlistenKey(listen_key, self.symbol))
                                listenKeyTime = time.time()


            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'币安 ws连接失败 开始重连...')


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")