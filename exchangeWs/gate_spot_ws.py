#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *

from moneyV2.money import changePos

class gate_spot_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.symbol = gate.symbol
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

    def gen_signed(self, channel, event, timestamp):
        # 为消息签名
        api_key = self.access_id
        api_secret = self.secret_key

        s = 'channel=%s&event=%s&time=%d' % (channel, event, timestamp)
        sign = hmac.new(api_secret.encode('utf-8'), s.encode('utf-8'), hashlib.sha512).hexdigest()
        return {'method': 'api_key', 'KEY': api_key, 'SIGN': sign}

    """ 更新订单"""
    def _update_order(self, data22):
        try:
            for data2 in data22:
                pprint(data2)
                id = data2['id']
                symbol = data2['currency_pair']
                side = data2['side'].upper()

                if 'price' not in data2 or not data2['price']:    #委托价格
                    if id in self.orders:
                        data2['price'] = self.orders[id]['price']
                        data2['size'] = self.orders[id]['size']
                    else:
                        data2['price'] = '0'
                        data2['size'] = '0'

                if 'avg_deal_price' not in data2 or not data2['avg_deal_price']:   #成交价格
                    if id in self.orders:
                        data2['avg_deal_price'] = self.orders[id]['jiage']
                    else:
                        data2['avg_deal_price'] = float(data2['price'])

                data2['type'] = data2['type'] if 'type' in data2 else '-'

                jiage = float(data2['avg_deal_price'])
                liang = float(data2['filled_total']) / jiage if jiage else 0

                self.orders[id] = {
                    'id': id,                  
                    'symbol': symbol,                  
                    'status': data2['event'],                  
                    'side': side,                               
                    'type': data2['type'],                     #
                    'jiage': jiage,             #累积成交均价
                    'liang': liang,         #累积成交量
                    'price': data2['price'],
                    'size': data2['amount'],
                    'yingkui': 0,             #盈亏
                    'jiazhi': 0,  #价值
                    'sxf': 0,  #手续费
                    'time': NowTime(),                #推送时间
                }

                # pprint(self.orders[id])
                # print("------------------")

                #价格小数位

                msg = ['[Gate监听订单]', GetTime(echoMs=1), '', side, symbol, data2['type'], data2['time_in_force'],
                "　ID", id, data2['event'],
                '　价格', data2['price'], '数量', str(data2['amount']) + ' ('+U(float(data2['price'])*float(data2['amount']))+')',
                '　成交价', jiage, '成交量', str(liang) + ' ('+U(jiage*liang)+')',
                '　GT抵扣', data2['gt_fee'],
                
                ]

                if liang:
                    log(*msg)

                    if data2['event'] in ['finish']:
                        changePos(self, symbol, side, jiage, liang)

                else:
                    print(*msg)


                
                if len(self.orders) > 500:
                    self.orders = dict(list(self.orders.items())[-100:])

                self.private_update_time = time.time()
            
        except Exception as e:
            uploadError(traceback.format_exc())



    """ 更新余额"""
    def _update_account(self, data):
        for v2 in data:
            if v2['currency'].upper() == CcyBi2:
                self.keyong = N(float(v2['available']), 4)
                self.yingkui = 0

                # self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.keyong != self.preUsdt:
                    print('[Gate现货余额更新]', self.keyong)

            self.preUsdt = self.keyong
        


    """ 更新盘口和费率信息"""
    def _update_depth(self, v):
        s = v['s']
        if not v['bids']:
            return

        data= {
            'bidPx': float(v['bids'][0][0]),
            'askPx': float(v['asks'][0][0]),
            'bidQty': float(v['bids'][0][1]),
            'askQty': float(v['asks'][0][1]),
            'maxLiang': ***********,
            'feilv': 0,
            't': v['t']
        }

        if self.depathCallback:
            self.depathCallback(s, data)
            
        self.data[s] = data
            

    async def ping(self, conn):
        while True:
            await conn.send_str('{"time": %d, "channel" : "spot.ping"}' % int(time.time()))
            await asyncio.sleep(10)


    async def main(self, symbols=[]):
        while True:
            try:
                url = self.gate.GetWsUrl()
                # print(url, symbols)

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                ssl=False,
                                #verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:
                    if symbols:
                        for symbol in symbols:
                            current_time = int(time.time())
                            channel = "spot.order_book"
                            sub_str = {
                                "time": current_time,
                                "channel": channel,
                                "event": "subscribe", 
                                "payload": [symbol, "5", "100ms"]
                            }
                            await conn.send_str(ujson.dumps(sub_str))

                    else:
                        current_time = int(time.time())
                        channel = "spot.orders"
                        sub_str = {
                            "time": current_time,
                            "channel": channel,
                            "event": "subscribe", 
                            "payload": ["!all"]
                            }
                        sub_str["auth"] = self.gen_signed(sub_str['channel'], sub_str['event'], sub_str['time'])
                        await conn.send_str(ujson.dumps(sub_str))

                        current_time = int(time.time())
                        channel = "spot.balances"
                        sub_str = {
                            "time": current_time,
                            "channel": channel,
                            "event": "subscribe", 
                        }
                        sub_str["auth"] = self.gen_signed(sub_str['channel'], sub_str['event'], sub_str['time'])
                        await conn.send_str(ujson.dumps(sub_str))

                    asyncio.create_task(self.ping(conn))

                    while True:
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('Gate ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Gate ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        try:
                            msg = ujson.loads(msg.data)
                        except:
                            log(f'Gate ws解码失败...')
                            time.sleep(5)
                            #强制重启
                            os._exit(0)

                        if msg['event'] in ['update', 'all']:
                            if msg['channel'] == 'spot.order_book':self._update_depth(msg['result'])
                            elif msg['channel'] == 'spot.orders':self._update_order(msg['result'])
                            elif msg['channel'] == 'spot.balances':self._update_account(msg['result'])

            except Exception as e:
                print(e)
                uploadError(traceback.format_exc())
                log(f'Gate ws连接失败 开始重连...')

                #强制重启
                os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")