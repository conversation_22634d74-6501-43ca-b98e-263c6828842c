from main.config import *
from main.hanshu import *
from os import times
import aiohttp
import asyncio
import ujson
import time
import hashlib
import traceback
from datetime import datetime
from urllib import parse
import hmac
import gzip
from moneyV2.money import changePos

class deepcoin_ws():
    def __init__(self, rest, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.rest = rest
        
        self.URL = 'wss://net-wss.deepcoin.com'

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.order_status = ['1']
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

        self.id = 1

        self.name = 'depth'

    def _update_order(self, data):
        """更新订单"""
        try:
            data = data['result'][0]['data']
            if data['InstrumentID']:
                symbol = data['InstrumentID']
                eventType = data['OrderStatus']
                side = "BUY" if data['Direction'] == '0' else "SELL"
                data['TradePrice']  = data['TradePrice'] if 'TradePrice' in data else 0
                data['Volume']  = data['Volume'] if 'Volume' in data else 0
                
                self.orders[data['OrderSysID']] = {
                    'symbol': symbol,                #交易对
                    'status': eventType,            #状态
                    'jiage': float(data['TradePrice']),      #价格
                    'liang': data['Volume'],      #数量
                    'side':side,              #方向
                    'jiazhi': 0,  #价值
                    'yingkui': 0,          #盈亏
                    'time': NowTime(),              #推送时间
                }

                msg = [
                    '[deepcoin合约监听订单]', GetTime(echoMs=1), '', side, symbol, eventType, "　ID", data['OrderSysID'],
                    '　价格', data['Price'], '数量', data['Volume'],
                    '　成交价', float(data['TradePrice']), '成交量', data['VolumeTraded'],
                    # '　费率',  str(data['maker_fee'])+'/'+str(data['taker_fee']),
                ]

                if eventType in self.order_status and data['VolumeTraded']:
                    changePos(self, symbol, side, float(data['TradePrice']), data['Volume'])

                if data['VolumeTraded']:
                    log(*msg)
                else:
                    print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())
    
    def _update_account(self, data):
        """更新余额"""

        # self.usdt = N(float(i['available']), 4)   #2-8，Rest来统计所有余额，ws更新可用
        self.keyong = N(float(data['walletBalance']) - float(data['openOrderMarginFrozen']), 4)
        self.yingkui = 0

        # self.usdt = self.usdt if self.usdt else 0.01
        self.keyong = self.keyong if self.keyong else 0.01

        if self.keyong != self.preUsdt:
            print('[deepcoin合约更新]', self.keyong)

        self.preUsdt = self.keyong
        
        
    """ 更新盘口信息"""
    def _update_depath(self, msg, name):

        if name == 'trade':
            pprint(msg)
            v = msg['r'][0]['d']

            s = v['I']
            if 'BP1' not in v or not v['BP1'] or not v['AP1']:
                return
            
            if s not in self.data:
                self.data[s] = {'bidQty': 0, 'askQty': 0, 'feilv': 0}
            
            self.data[s]['bidPx'] = float(v['BP1'])
            self.data[s]['askPx'] = float(v['AP1'])
            self.data[s]['t'] = int(v['U'])*1000

        else:
            try:
                s = msg['r'][0]['d']['I']
            except:
                return
                
            if s not in self.data:
                self.data[s] = {'bidPx': 0, 'askPx': 0, 'bidQty': 0, 'askQty': 0, 'feilv': 0}

            bid = 0
            ask = 0
            for v in msg['r']:
                v = v['d']
                if v['D'] == '0' and not bid:
                    bid = v 
                if v['D'] == '1' and not ask:
                    ask = v

            if bid:
                self.data[s]['bidPx'] = float(bid['P'])
                self.data[s]['bidQty'] = float(bid['V'])
            
            if ask:
                self.data[s]['askPx'] = float(ask['P'])
                self.data[s]['askQty'] = float(ask['V'])

        # pprint(self.data)
        # print(name)
        self.data[s]['t'] = NowTime_ms()

        if self.depathCallback:
            self.depathCallback(s, self.data[s])


    async def ping(self, conn):
        while True:
            await conn.send_str("ping")
            await asyncio.sleep(5)


    async def main(self, symbols=[]):

        trade_ms = NowTime_ms()
        depth_ms = NowTime_ms()
        kline_ms = NowTime_ms()

        trade = 0
        depth = 0
        kline = 0

        symbolData = self.rest.GetSymbolsList()

        while True:
            try:
                if symbols:
                    url = self.URL + '/public/ws'
                else:
                    url = self.URL + '/private/ws'

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:
                    if symbols:
                        await conn.send_str('zip=1')
                        # 订阅深度
                        for symbol in symbols:

                            """ 这个买一卖一更新频率太慢了 1s更新一次"""
                            if self.name in ['trade', 'all']:
                                sub_params = {
                                    "SendTopicAction":{
                                        "Action":"2",
                                        "FilterValue":"DeepCoin_"+ symbol,
                                        "LocalNo":9,
                                        "ResumeNo":-2,
                                        "TopicID":"7"
                                    }
                                }
                                sub_str_depth = ujson.dumps(sub_params)
                                await conn.send_str(sub_str_depth)
                                sub_params = {
                                    "SendTopicAction":{
                                        "Action":"1",
                                        "FilterValue":"DeepCoin_"+ symbol,
                                        "LocalNo":9,
                                        "ResumeNo":-2,
                                        "TopicID":"7"
                                    }
                                }
                                sub_str_depth = ujson.dumps(sub_params)
                                await conn.send_str(sub_str_depth)

                                log('订阅成交记录...')
                                log('订阅成交记录...')
                                log('订阅成交记录...')
                            
                            if self.name in ['depth', 'all']:
                                for data in symbolData:
                                    symbol2 = data['InstrumentID']
                                    if symbol == symbol2:
                                        sz = float(data['PriceTick'])
                                        print(symbol, sz)
                                        break
                                else:
                                    uploadError('未找到小数位:'+symbol)
                                    time.sleep(5)
                                    break

                                sub_str_depth = ujson.dumps({"SendTopicAction":{"Action":"2","FilterValue": f"DeepCoin_{symbol}_{sz}","LocalNo":4,"ResumeNo":-2,"TopicID":"25"}})
                                await conn.send_str(sub_str_depth)

                                sub_str_depth = ujson.dumps({"SendTopicAction":{"Action":"1","FilterValue": f"DeepCoin_{symbol}_{sz}","LocalNo":4,"ResumeNo":-2,"TopicID":"25"}})
                                await conn.send_str(sub_str_depth)
                            
                                log('订阅深度信息...')
                                log('订阅深度信息...')
                                log('订阅深度信息...')
                            # sub_str_depth = ujson.dumps({"SendTopicAction":{"Action":"1","FilterValue":"DeepCoin_BTCUSDT_1m","LocalNo":6,"ResumeNo":-1,"TopicID":"11"}})
                            # await conn.send_str(sub_str_depth)

                            # sub_str_depth = ujson.dumps({"SendTopicAction":{"Action":"2","FilterValue":"DeepCoin_BTCUSDT_1m","LocalNo":6,"ResumeNo":-1,"TopicID":"11"}})
                            # await conn.send_str(sub_str_depth)

                    else:
                        sub_params = {
                            "SendUserLogin":{
                                "Token": self.rest.token
                            }
                        }

                        sub_str_depth = ujson.dumps(sub_params)
                        await conn.send_str(sub_str_depth)    

                        sub_params = {
                            "SendTopicAction":{
                                "Action":"1",
                                "FilterValue":str(self.rest.account_id),
                                "LocalNo":12,
                                "ResumeNo":-1,
                                "TopicID":"3"
                            }
                        }
                        sub_str_depth = ujson.dumps(sub_params)
                        await conn.send_str(sub_str_depth)  

                        sub_params = {
                            "SendTopicAction":{
                                "Action":"1",
                                "FilterValue":"9154578",
                                "LocalNo":13,
                                "ResumeNo":-2,
                                "TopicID":"14"
                            }
                        }
                        sub_str_depth = ujson.dumps(sub_params)
                        await conn.send_str(sub_str_depth)  
                                   
                    asyncio.create_task(self.ping(conn))

                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('deepcoin ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('deepcoin ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        try:
                            msg = msg.data
                        
                        except:
                            print('解析失败: '+str(msg))
                            break
                        
                        # print('depth', NowTime_ms() - depth_ms, 'ms', depth, '  | ', 'trade', NowTime_ms() - trade_ms, 'ms', depth)
                        # if 'PMO' in msg: 
                        #     msg = ujson.loads(msg)
                        #     try:
                        #         depth = [msg['r'][0]['d']['P'], msg['r'][12]['d']['P']]
                        #     except:
                        #         pprint(msg)
                        #     depth_ms = NowTime_ms()
                        
                        # if 'PO' in msg: 
                        #     msg = ujson.loads(msg)
                        #     v = msg['r'][0]['d']
                        #     if 'BP1' not in v or not v['BP1'] or not v['AP1']:
                        #         continue
                        #     depth = [v['BP1'], v['AP1']]
                        #     trade_ms = NowTime_ms() 


                        if '"PO"' in msg: self._update_depath(ujson.loads(msg), 'trade')
                        elif '"PMO"' in msg: self._update_depath(ujson.loads(msg), 'depth')
                        elif 'PushOrder' in msg: self._update_order(ujson.loads(msg))
                        elif msg == 'pong': pass
                        else: pprint(msg)
    

            except Exception as e:
                # uploadError(traceback.format_exc())
                log(f'deepcoin ws连接失败 开始重连...', symbols, traceback.format_exc())

                time.sleep(1)
                #强制重启
                # os._exit(0)


if __name__ == "__main__":
    import deepcoin
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    ks = deepcoin.deepcoin()
    k = deepcoin_ws(ks)
    loop = asyncio.get_event_loop()
    asyncio.run(k.main())
