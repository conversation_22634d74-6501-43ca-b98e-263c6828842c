#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *


from moneyV2.money import changePos

class gate_ws():
    def __init__(self, gate, depathCallback=0, orderCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.orderCallback = orderCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        
        try:
            if GateColo:
                log('Gate Ws使用colo高速线路')
                self.URL = 'wss://fxws-private.gateapi.io/v4/ws/usdt'
                # self.URL = 'wss://fx-ws.gateio.ws/v4/ws/usdt'
            else:
                self.URL = 'wss://fx-ws.gateio.ws/v4/ws/usdt'
        except:
            self.URL = 'wss://fx-ws.gateio.ws/v4/ws/usdt'

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01

        self.feilv = {}
        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

    def gen_signed(self, channel, event, timestamp):
        # 为消息签名
        api_key = self.access_id
        api_secret = self.secret_key

        s = 'channel=%s&event=%s&time=%d' % (channel, event, timestamp)
        sign = hmac.new(api_secret.encode('utf-8'), s.encode('utf-8'), hashlib.sha512).hexdigest()
        return {'method': 'api_key', 'KEY': api_key, 'SIGN': sign}


    """ 更新订单"""
    def _update_order(self, data):
        # pprint(data)
        try:
            for data2 in data:
                side = "BUY" if data2['size'] > 0 else "SELL"
                filled_paper = float(D(abs(data2["size"])) - D(abs(data2["left"])))
                self.orders[data2['id']] = {
                    'symbol': data2['contract'],              #交易对
                    'status': data2['status'],               #状态
                    'side': side,          #方向
                    'type': data2['tif'],               #订单类型
                    'jiage': data2['fill_price'],              #累积成交均价
                    'liang': filled_paper,   #累积成交量
                    'yingkui': 0,         #盈亏
                    'time': NowTime(),                 #推送时间
                }
                #is_reduce_only

                # pprint(self.orders[data2['id']])
                close = '减仓' if data2['is_reduce_only'] else '开仓'
                if data2['contract'] in self.data:
                    data = self.data[data2['contract']]
                    nowPrice = f"　bid {data['bidPx']} ask {data['askPx']}"
                    if side == 'BUY':
                        huadian = StrBi(1 - float(data2['price']) / data['bidPx'], 3, kuo=1)
                    else:
                        huadian = StrBi(1 - data['askPx'] / float(data2['price']), 3, kuo=1)
                else:
                    nowPrice =''
                    nowPrice =''

                msg = ['[Gate监听订单]', GetTime(echoMs=1), '', close, side, data2['contract'], data2['tif'],
                "　ID", data2['id'], data2['status'], data2['finish_as'],
                nowPrice,
                '　价格', str(data2['price'])+huadian, '数量', data2['size'],
                '　成交价', data2['fill_price'], '成交量', filled_paper,
                '　手续费', StrBi(data2['mkfr'], 3)+'/'+StrBi(data2['tkfr'], 3)]

                if filled_paper:
                    log(*msg)
                    if data2['status'] in ['finished']:
                        changePos(self, data2['contract'], side, data2['fill_price'], filled_paper, data2['is_reduce_only'])
                else:
                    print(*msg)
                
                if self.orderCallback:
                    self.orderCallback(msg)

            
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新余额"""
    def _update_account(self, data):
        for data2 in data:
            if data2['currency'].upper() == CcyBi:
                pprint(data2)
                self.usdt = N(float(data2['balance']), 4)
                self.usdt = self.usdt if self.usdt else 0.01
                print('[Gate余额更新]', self.usdt)
        


    """ 更新盘口信息"""
    def _update_depth(self, depth):
        if not depth['bids']:
            depth['bids'] = depth['asks']
        if not depth['asks']:
            depth['asks'] = depth['bids']
        
        s = depth['contract']
        if s not in self.data:
            self.data[s] = {'feilv': 0}
        self.data[s]['bidPx']  = float(depth['bids'][0]['p'])
        self.data[s]['askPx']  = float(depth['asks'][0]['p'])
        self.data[s]['bidQty'] = depth['bids'][0]['s']
        self.data[s]['askQty'] = depth['asks'][0]['s']
        self.data[s]['t']      = depth['t']

        if self.depathCallback:
            self.depathCallback(s, self.data[s])

        self.public_update_time = time.time()


    """ 更新费率"""
    def _update_feilv(self, datas):
        for data in datas:
            s = data['contract']
            if s not in self.data:
                self.data[s] = {}
            self.data[s]['feilv'] = float(data['funding_rate']) * 100
            # print(GetTime(0, echoMs=1), s, self.data[s]['feilv'])
                

    async def ping(self, conn):
        while True:
            await conn.send_str('{"time": %d, "channel" : "futures.ping"}' % int(time.time()))
            await asyncio.sleep(5)


    async def main(self, symbols=[]):
        while True:
            # log("Gate 准备连接ws", symbols)
            try:
                
                # print(self.URL)
                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            self.URL,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:


                    if symbols:
                        for symbol in symbols:
                            
                            current_time = int(time.time())
                            channel = "futures.order_book"
                            sub_str = {
                                "time": current_time,
                                "channel": channel,
                                "event": "subscribe", 
                                "payload": [symbol, "1", "0"]
                            }
                            await conn.send_str(ujson.dumps(sub_str))
                            
                            current_time = int(time.time())
                            channel = "futures.tickers"     #资金费率
                            sub_str = {
                                "time": current_time,
                                "channel": channel,
                                "event": "subscribe", 
                                "payload": [symbol]
                            }
                            await conn.send_str(ujson.dumps(sub_str))

                    else:
                        user_id = self.gate.GetUserId()

                        current_time = int(time.time())
                        channel = "futures.orders"
                        sub_str = {
                            "time": current_time,
                            "channel": channel,
                            "event": "subscribe", 
                            "payload": [str(user_id), "!all"]
                            }
                        sub_str["auth"] = self.gen_signed(sub_str['channel'], sub_str['event'], sub_str['time'])
                        await conn.send_str(ujson.dumps(sub_str))
                        
                        # current_time = int(time.time())
                        # channel = "futures.balances"
                        # sub_str = {
                        #     "time": current_time,
                        #     "channel": channel,
                        #     "event": "subscribe", 
                        #     "payload": [str(user_id)]
                        # }
                        # sub_str["auth"] = self.gen_signed(sub_str['channel'], sub_str['event'], sub_str['time'])
                        # await conn.send_str(ujson.dumps(sub_str))


                    # note: important to keepalive 
                    asyncio.create_task(self.ping(conn))

                    # loop to process update data
                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=10) #
                        except asyncio.TimeoutError:
                            log(Color('Gate ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Gate ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        # print(msg)
                        try:
                            msg = ujson.loads(msg.data)
                        except:
                            log(f'Gate ws解码失败...')
                            time.sleep(5)
                            #强制重启
                            os._exit(0)


                        # 处理消息
                        if msg['event'] in ['update', 'all']:
                            if msg['channel'] == 'futures.order_book':self._update_depth(msg['result'])
                            elif msg['channel'] == 'futures.balances':self._update_account(msg['result'])
                            elif msg['channel'] == 'futures.orders':self._update_order(msg['result'])
                            elif msg['channel'] == 'futures.tickers':self._update_feilv(msg['result'])
                            # else: print(msg)

                        # else:
                        #     print(msg)
                        
                        if not symbols:                            
                            if time.time() - self.private_update_time > 300*5:
                                log(Color('Gate Ws 长期未更新私有信息重连', -1))
                                os._exit(0)

                        elif time.time() - self.public_update_time > 10:
                            log(Color('Gate Ws 长期未更新公有信息重连', -1))
                            os._exit(0)

            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'Gate ws连接失败 开始重连...')

                #强制重启
                os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")