#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *


from moneyV2.money import changePos

class kucoin_spot_ws():
    def __init__(self, coinex, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.coinex = coinex
        self.access_id = coinex.access_id
        self.secret_key = coinex.secret_key
        self.passwd = coinex.passwd

        
        all = self.coinex.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0.01

        self.orders = {}
        self.symbol = coinex.symbol
        self.data = {}
        self.balance = {}
        self.order_status = ['done']
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

    """ 更新订单"""
    def _update_order(self, data2):
        try:
            id = data2['orderId']
            symbol = data2['symbol']
            side = data2['side'].upper()

            if 'price' not in data2 or not data2['price']:  # 委托价格
                if id in self.orders:
                    data2['price'] = self.orders[id]['price']
                    data2['size'] = self.orders[id]['size']
                else:
                    data2['price'] = '0'
                    data2['size'] = '0'

            if 'matchPrice' not in data2 or not data2['matchPrice']:  # 成交价格
                if id in self.orders:
                    data2['matchPrice'] = self.orders[id]['jiage']
                else:
                    data2['matchPrice'] = float(data2['price'])

            data2['orderType'] = data2['orderType'] if 'orderType' in data2 else '-'

            jiage = float(data2['matchPrice'])
            liang = float(data2['filledSize'])

            self.orders[id] = {
                'id': id,                  # 交易对
                'symbol': symbol,          # 交易对
                'status': data2['status'],  # 状态
                'side': side,              # 方向
                'type': data2['orderType'],  # 订单类型
                'jiage': jiage,            # 累积成交均价
                'liang': liang,            # 累积成交量
                'price': data2['price'],
                'size': data2['size'],
                'yingkui': 0,              # 盈亏
                'jiazhi': 0,               # 价值
                'sxf': 0,                  # 手续费
                'time': NowTime(),         # 推送时间
            }

            msg = ['[KuCoin监听订单]', GetTime(echoMs=1), '', side, symbol, data2['orderType'], data2['type'],
                   "　ID", id, data2['status'],
                   '　价格', data2['price'], '数量', str(data2['size']) + ' (' + U(float(data2['price']) * float(data2['size'])) + ')',
                   '　成交价', jiage, '成交量', str(liang) + ' (' + U(jiage * liang) + ')']

            if liang:
                log(*msg)
                if data2['status'] in self.order_status:
                    changePos(self, symbol, side, jiage, liang)

            else:
                print(*msg)

            
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()

        except Exception as e:
            uploadError(traceback.format_exc())

    """ 更新余额"""
    def _update_account(self, data):
        s = data['currency']
        if s == CcyBi2:
            self.keyong = N(float(data['available']), 4)
            self.yingkui = 0
            self.keyong = self.keyong if self.keyong else 0.01

            if self.keyong != self.preUsdt:
                print('[Ku现货余额更新]', self.keyong)

        self.preUsdt = self.keyong


    def _update_depth(self, v):
        topic = v['topic']
        symbol = topic.split(':')[1]
        data = {
            'bidPx': float(v['data']['bestBid']),
            'askPx': float(v['data']['bestAsk']),
            'bidQty': float(v['data']['bestBidSize']),
            'askQty': float(v['data']['bestAskSize']),
            'maxLiang': MAX,
            'feilv': 0,
            't': NowTime_ms()
        }
        if self.depathCallback:
            self.depathCallback(symbol, data)

        self.data[symbol] = data


    async def ping(self, conn):
        while True:
            msg = {
                'id': str(int(time.time() * 1000)),
                'type': 'ping'
            }
            await conn.send_str(ujson.dumps(msg))
            await asyncio.sleep(10)

    async def main(self, symbols=[]):
        while True:
            try:
                url = self.coinex.GetWsUrl(0 if symbols else 1)
                connector = aiohttp.TCPConnector(
                    limit=50,
                    keepalive_timeout=120,
                    ssl=False,
                )
                async with aiohttp.ClientSession(connector=connector) as session:
                    async with session.ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                    ) as conn:
                        if symbols:
                            channels = []
                            for symbol in symbols:
                                channels += [
                                    f"/market/ticker:{symbol}"
                                ]
                        else:
                            # 订阅
                            channels = [
                                f"/spotMarket/tradeOrders",
                                f"/account/balance",  # 添加订阅余额更新通道
                            ]

                        for i in channels:
                            sub_str = ujson.dumps({"topic": i, "type": "subscribe"})
                            await conn.send_str(sub_str)

                        asyncio.create_task(self.ping(conn))

                        while True:
                            # 接受消息
                            try:
                                msg = await conn.receive(timeout=30)
                            except asyncio.TimeoutError:
                                log(Color('Ku ws长时间没有收到消息 准备重连...', -1))
                                break
                            except:
                                print(traceback.format_exc())
                                log(Color('Ku ws出现错误 准备重连...', -1))
                                time.sleep(3)
                                break

                            msg = msg.data
                            try:
                                msg = ujson.loads(msg)

                            except:
                                print('Ku 解析失败: ' + str(msg))
                                break
                            
                            if 'data' in msg:
                                subject = msg.get('subject', '')
                                if 'orderChange' in subject:
                                    self._update_order(msg['data'])
                                elif 'ticker' in subject:
                                    self._update_depth(msg)
                                elif 'account.balance' in subject:  # 添加处理余额更新
                                    self._update_account(msg['data'])
                                elif 'tradeOrders' in subject:  # 添加处理订单状态更新
                                    self._update_order(msg['data'])

            except Exception as e:
                print(str(e))
                print(traceback.format_exc())
                uploadError(traceback.format_exc())
                log(f'Ku ws连接失败 开始重连...')

                # 强制重启
                os._exit(0)


# from kucoin_spot import kucoin_spot

# async def main():
#     coinex = kucoin_spot()
#     await kucoin_spot_ws(coinex).main(['DOGE-USDT'])
#     print("websocket demo")

# if __name__ == "__main__":
#     asyncio.run(main())

