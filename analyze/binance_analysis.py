#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance现货和期货交易对分析脚本
分析所有现货币种中有永续合约的品种，计算price tick/last price比例并排序
"""

import requests
import json
import time
from decimal import Decimal
import pandas as pd


class BinanceAnalyzer:
    def __init__(self):
        self.spot_base_url = "https://api.binance.com"
        self.futures_base_url = "https://fapi.binance.com"
        self.session = requests.Session()

    def get_spot_exchange_info(self):
        """获取现货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_exchange_info(self):
        """获取期货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_spot_24hr_ticker(self):
        """获取现货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_24hr_ticker(self):
        """获取期货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def parse_tick_size(self, filters):
        """解析价格精度(tick size)"""
        for filter_item in filters:
            if filter_item['filterType'] == 'PRICE_FILTER':
                return float(filter_item['tickSize'])
        return None

    def calculate_tick_ratio(self, tick_size, last_price):
        """计算tick size / last price比例"""
        if last_price == 0:
            return 0
        return tick_size / last_price

    def format_volume(self, volume):
        """格式化交易量为M/B单位"""
        if volume >= 1_000_000_000:
            return f"{volume / 1_000_000_000:.3f}B"
        elif volume >= 1_000_000:
            return f"{volume / 1_000_000:.3f}M"
        else:
            return f"{volume:.0f}"

    def analyze_trading_pairs(self):
        """分析交易对数据"""
        print("正在获取数据...")

        # 获取基础数据
        spot_info = self.get_spot_exchange_info()
        futures_info = self.get_futures_exchange_info()
        spot_24hr = self.get_spot_24hr_ticker()
        futures_24hr = self.get_futures_24hr_ticker()

        if not all([spot_info, futures_info, spot_24hr, futures_24hr]):
            print("获取数据失败，请检查网络连接")
            return

        print("数据获取完成，开始分析...")

        # 构建数据字典
        spot_symbols = {}
        futures_symbols = {}
        spot_24hr_data = {}
        futures_24hr_data = {}

        # 处理现货数据
        for symbol_info in spot_info['symbols']:
            if symbol_info['status'] == 'TRADING':
                symbol = symbol_info['symbol']
                tick_size = self.parse_tick_size(symbol_info['filters'])
                if tick_size:
                    spot_symbols[symbol] = {
                        'tick_size': tick_size,
                        'base_asset': symbol_info['baseAsset'],
                        'quote_asset': symbol_info['quoteAsset']
                    }

        # 处理期货数据
        for symbol_info in futures_info['symbols']:
            if symbol_info['status'] == 'TRADING' and symbol_info['contractType'] == 'PERPETUAL':
                symbol = symbol_info['symbol']
                tick_size = self.parse_tick_size(symbol_info['filters'])
                if tick_size:
                    futures_symbols[symbol] = {
                        'tick_size': tick_size,
                        'base_asset': symbol_info['baseAsset'],
                        'quote_asset': symbol_info['quoteAsset']
                    }

        # 处理24小时数据
        for ticker in spot_24hr:
            spot_24hr_data[ticker['symbol']] = {
                'last_price': float(ticker['lastPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume'])  # 现货已经是USDT单位
            }

        for ticker in futures_24hr:
            futures_24hr_data[ticker['symbol']] = {
                'last_price': float(ticker['lastPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume'])  # 期货已经是USDT单位
            }

        # 找出既有现货又有期货的交易对
        common_symbols = set(spot_symbols.keys()) & set(futures_symbols.keys())

        results = []

        for symbol in common_symbols:
            if symbol in spot_24hr_data and symbol in futures_24hr_data:
                spot_data = spot_symbols[symbol]
                futures_data = futures_symbols[symbol]
                spot_ticker = spot_24hr_data[symbol]
                futures_ticker = futures_24hr_data[symbol]

                # 计算现货tick ratio
                spot_tick_ratio = self.calculate_tick_ratio(
                    spot_data['tick_size'],
                    spot_ticker['last_price']
                )

                # 计算期货tick ratio
                futures_tick_ratio = self.calculate_tick_ratio(
                    futures_data['tick_size'],
                    futures_ticker['last_price']
                )

                # 计算交易量比值
                volume_ratio = 0
                if futures_ticker['quote_volume'] > 0:
                    volume_ratio = spot_ticker['quote_volume'] / futures_ticker['quote_volume']

                results.append({
                    'symbol': symbol,
                    'base_asset': spot_data['base_asset'],
                    'spot_tick_size': spot_data['tick_size'],
                    'spot_last_price': spot_ticker['last_price'],
                    'spot_tick_ratio': spot_tick_ratio,
                    'spot_volume_24h': spot_ticker['quote_volume'],
                    'futures_tick_size': futures_data['tick_size'],
                    'futures_last_price': futures_ticker['last_price'],
                    'futures_tick_ratio': futures_tick_ratio,
                    'futures_volume_24h': futures_ticker['quote_volume'],
                    'volume_ratio': volume_ratio
                })

        # 按现货tick ratio从高到低排序
        results.sort(key=lambda x: x['spot_tick_ratio'], reverse=True)

        return results

    def display_results(self, results):
        """显示分析结果"""
        if not results:
            print("没有找到符合条件的交易对")
            return

        print(f"\n找到 {len(results)} 个既有现货又有永续合约的交易对")
        print("=" * 150)
        print(f"{'交易对':<15} {'基础币种':<10} {'现货Tick':<12} {'现货价格':<15} {'现货Tick比例':<15} "
              f"{'现货24h交易量':<15} {'期货Tick':<12} {'期货价格':<15} {'期货Tick比例':<15} "
              f"{'期货24h交易量':<15} {'交易量比值':<12}")
        print("=" * 150)

        for item in results:
            spot_volume_str = self.format_volume(item['spot_volume_24h'])
            futures_volume_str = self.format_volume(item['futures_volume_24h'])

            print(f"{item['symbol']:<15} {item['base_asset']:<10} "
                  f"{item['spot_tick_size']:<12.8f} {item['spot_last_price']:<15.3f} "
                  f"{item['spot_tick_ratio']:<15.8f} {spot_volume_str:<15} "
                  f"{item['futures_tick_size']:<12.8f} {item['futures_last_price']:<15.3f} "
                  f"{item['futures_tick_ratio']:<15.8f} {futures_volume_str:<15} "
                  f"{item['volume_ratio']:<12.3f}")

    def save_to_csv(self, results, filename="binance_analysis.csv"):
        """保存结果到CSV文件"""
        if not results:
            return

        df = pd.DataFrame(results)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到 {filename}")


def main():
    analyzer = BinanceAnalyzer()

    try:
        results = analyzer.analyze_trading_pairs()
        if results:
            analyzer.display_results(results)
            analyzer.save_to_csv(results)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()
