#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint

import uuid
import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
import hashlib


from main.config import *
from main.hanshu import *

def parse_params_to_str(params):
    url = ''
    for key in sorted(params.keys()):
        value = params[key]
        if isinstance(value, bool):
            if params[key]:
                value = "true"
            else:
                value = "false"
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]

NAME = 'Bybit'
class bybit():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['9zMljHuV5Xfjkr0rWL', 'KayoPgCbdXzNrk4DGhxojbvcspb3yLrGAJK0'],
            'bybit1': ['YHUSGJDXNHVJMIROUV', 'SJUALMISHVTNCBEAUYZOEBOSOQVNOZUBVVRE'],
            'bybit2-球球': ['WMTCFHVBKEPUELSPMB', 'EXGMZNKVVIXAXKZIJBQXWHRQQVFNGQFLOZLV'],
            'bybit3-球球': ['RMWCGUBISCIYRWBJWF', 'CJEAMETWNIVRTPSJWBZLSXVKPFSEKDQGWHRX'],
            'bybit4-球球': ['SVMUOKLCXPBEYKOQIL', 'RTKWZWGFNDQNWLCPQFAKFBDWRJHLFOMGPJMP'],
            'bybit5': ['HUXRVONYWZMDKVJXJG', 'FWNCEVIGYNQDHYYTZAGPXJMTPHJASXOFAHYM'],
            'bybit6-球球': ['ZEESXWGIBQBCWYPITD', 'RKFLZQIGACGEDUIHCRIWKUOWSEFGIOYIJWXO'],
            'bybit7-球球': ['EILLGOCBAIMANNLYWI', 'PAQVAQLRFLXERBJDRFCIGNNTCKJYITPJKEMI'],
            'bybit8-球球': ['IJKZWATSTBZYGJNBZA', 'CPETBRCJXFPJRADNXEJEKHATMXFUNPVOLPHX'],
            '返佣下级': ['1adkUewXgT58U8doZv', '8u61lAH5n1nPm6YA0eXe4fR1l3v7mrWKiAMV'],
            'ltp': ['SIPAMCHJQHDSMFOEFE', 'NCXGVDCNHGXBEZSRWMZKBLZGUYXLJQIZHCYF'],
        }

        self.debug = 0
        self.debugs = []

        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]

        self.HOST = 'https://api.bybit.com'

        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log(f'{NAME} 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  #指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

        if '均衡' not in ServerName:
            self.SetChicang()

    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')



    def get_sign(self, params, secret_key):
        message = parse_params_to_str(params)
        hash = hmac.new(bytes(secret_key, encoding='utf-8'), bytes(message, encoding='utf-8'), hashlib.sha256)
        signature = hash.hexdigest()
        return signature

        
    # 请求API
    def go(self, method, path, payload=None, needKey=1):

        url = self.HOST + path

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        headers = {
            'Content-Type': 'application/json',
        }

        if needKey:
            payload['timestamp'] = str(int(time.time()*1000))
            payload['api_key'] = self.access_id
            payload['recv_window'] = "15000"
            payload['sign'] = self.get_sign(payload, self.secret_key)
            headers = {}


        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'data': ujson.dumps(payload), 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}


        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2



    """ 获取最佳挂单"""
    def GetTickerAll2(self):
        data = self.go("GET","/v5/market/tickers", {'category': 'spot'}, needKey=0)

        return data

    """ 获取所有交易对资金费率"""
    def GetSymbolFeilv(self, symbol=0):
        if not self.feilvData or not symbol:
            data = self.go("GET", '/derivatives/v3/public/tickers', {'category': 'linear'}, needKey=0)
        
            if not data or 'result' not in data or 'list' not in data['result'] or not data['result']['list']:
                print('Bybit获取资金费率失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            data = data['result']['list']
            if not symbol:
                return data

            for v in data:
                if v['nextFundingTime']:
                    v['next'] = int(v['nextFundingTime'])
                    v['time'] = NowTime()
                    self.feilvData[v['symbol']] = v

        try:
            symbolData = self.feilvData[symbol]
        except:
            symbolData = 0

        if not symbolData or NowTime() >= symbolData['next']+2:
            log(symbol, '更新资金费率',
                 '更新时间', GetTime(symbolData['next'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
                 )
            time.sleep(0.5)
            self.feilvData = {}
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['fundingRate'])*100, 4)


    """ 获取最佳挂单"""
    def GetTickerAll(self):
        data = self.go("GET", '/derivatives/v3/public/tickers', {'category': 'linear'}, needKey=0)
        # print(data)

        if not data or 'data' not in data:
            print('BitGet 获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        return data['data']
        

    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/derivatives/v3/public/instruments-info', {'category': 'linear'}, needKey=0)['result']['list']

    """ 获取交易对风险限额"""
    def GetSymbolsPosMax(self):
        return self.go("GET", '/derivatives/v3/public/risk-limit/list', {'category': 'linear'}, needKey=0)['result']['list']


    """获取可用余额"""
    def GetYuer(self, p=1):
        data = self.go("GET", "/contract/v3/private/account/wallet/balance", {'coin': CcyBi})
    
        no = 0
        ok = 0
        for v in data['result']['list']:
            if v['coin'] == CcyBi: #USDT
                ok = v
                break
        if not ok:
            no = 1

        if no:
            print(f'{NAME} 获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)

        ok['all'] = N(ok['equity'], 4)
        ok['keyong'] = N(ok['availableBalance'], 4)
        ok['yingkui'] = N(ok['unrealisedPnl'], 4)
        
        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(p+f' {NAME}  USDT总余额' if type(p) == str else f'{NAME} USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                 '持仓盈亏', N(ok['yingkui'], 4))

        return ok


    
    """ 获取所有持仓"""
    def GetPos(self):
        data = self.go('GET', "/contract/v3/private/position/list", {'settleCoin': CcyBi})
        try:
            if 'result' in data and 'retMsg' in data and data['retMsg'] == 'OK':
                okdata = data['result']['list']
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['symbol']
                    ok['liang'] = float(v['size'])
                    if not ok['liang']:
                        continue
                    
                    ok['side'] = v['side'].upper()
                    ok['side2'] = 'SELL' if ok['side'] == 'BUY' else 'BUY'

                    ok['jiage'] = float(v['entryPrice'])
                    ok['nowJiage'] = 0    #最新成交价格
                    ok['yingkui'] = N(float(v['unrealisedPnl']), 4)
                    ok['bzj'] = 0
                    ok['create_time'] = int(v['createdTime']) / 1000
                    ok['roe'] = 0
                    ok['time'] = NowTime_ms()

                    data.append(ok)

                return data

            else:
                log(Color(f"{NAME} 获取持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos()

        except Exception as e:
            uploadError(traceback.format_exc())
        
        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)

    
    """ 设置为双向持仓, 0为单项"""
    def SetChicang(self, mode=3):
        fh = self.go('POST', '/contract/v3/private/position/switch-mode', {'coin': CcyBi, 'mode': mode})
        log(f'Bybit 设置持仓模式', mode, fh['retMsg'])



    """设置杠杆倍数和持仓方向"""
    def SetGangGan(self, symbol, beishu, riskId=0):
        if not beishu:
            return

        if riskId:
            post = {
                "category": 'linear',
                "symbol": symbol,
                "riskId": riskId,
                "positionIdx": 1,
            }
            fh = self.go('POST', "/v5/position/set-risk-limit", post)
            log(f'{NAME} {symbol} 设置多仓风险限额', fh['retMsg'])
            post = {
                "category": 'linear',
                "symbol": symbol,
                "riskId": riskId,
                "positionIdx": 2,
            }
            fh = self.go('POST', "/v5/position/set-risk-limit", post)
            log(f'{NAME} {symbol} 设置空仓风险限额', fh['retMsg'])

        post = {
            "symbol": symbol,
            "buyLeverage": str(int(beishu)),
            "sellLeverage": str(int(beishu)),
        }
        fh = self.go('POST', "/contract/v3/private/position/set-leverage", post)
        log(f'{NAME} {symbol} 设置杠杠', beishu, fh['retMsg'])


        time.sleep(0.1)



    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ImmediateOrCancel', jiancang=0, msg2=''):
        
        jiage = str(jiage)
        liang = str(liang)
        liang2 = liang+' ('+STR_N(float(liang)*float(jiage))+'$)'
        

        msg = f'{NAME}　{symbol}　'

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'
            positionIdx = '2' if side == 'BUY' else '1'
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'
            positionIdx = '1' if side == 'BUY' else '2'

        side = 'Buy' if side == 'BUY' else 'Sell'
        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　减仓:"+str(jiancang)+"　Type:"+type+"　PosSide:"+positionIdx

        post = {
            'symbol': symbol,
            'positionIdx': positionIdx, #1多仓，2空仓
            'qty': liang,
            'price': jiage,
            'side': side,
            'orderType': 'Limit',
            'timeInForce': type,
        }

        if type == 'ioc':   #兼容Bitget
            post['timeInForce'] = 'ImmediateOrCancel'
            
        elif type == 'post_only':   #兼容Bitget
            post['timeInForce'] = 'PostOnly'

        elif type != 'ImmediateOrCancel':
            post['orderType'] = 'Market'
            post['timeInForce'] = 'GoodTillCancel'
            del post['price']

        if jiancang:
            post['reduceOnly'] = True

        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST', "/contract/v3/private/order/create", post)

            # log("下单返回", order)
            if not order or 'result' not in order or not order['result'] or 'orderId' not in order['result']:
                log(symbol+" [!!!] 套利下单失败！！重试", post, order)
                # if 'retCode' not in order or order['retCode'] != 140007:
                #     uploadError(symbol+' Bybit下单失败：'+str(post)+'  '+str(order))

                #     time.sleep(60)  #傻逼bybit老抽风
                #     os._exit(0)

            else:
                orderId = order['result']['orderId']
                break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg


    """提现"""
    def Tixian(self, dizhi, liang, bi='USDT', net='TRX'):
        p = {'coin':bi, 'address':dizhi, 'amount':str(liang), 'chain': net}
        data = self.go("POST", "/asset/v3/private/withdraw/create", p)
        log('Bybit 提现 地址', '链', net, dizhi, '数量', liang, '币种',bi, '结果', data)
        return data

    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi
        data = self.go("GET", "/spot/v3/private/account", {'coin':bi})
        for v in data['result']['balances']:
            if v['coin'] == bi:
                return N(v['free'], 4)
    

    """ 子母账户相互转账"""
    def ziHua(self, liang, uid, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'transferId': str(uuid.uuid4()),
            'coin': bi, 
            'amount': str(liang),
            'subMemberId': uid, 
            'type': side.upper(),
        }

        pprint(data)
        data = self.go("POST", "/asset/v3/private/transfer/sub-member-transfer", data)
        log('[Bybit子母]', '主账户转入' if side == 'in' else '子账户转出', '币种', bi, liang, '结果', data, Color('', 1))

        return 'retMsg' in data and data['retMsg'] == 'success'

    """ 现货和合约之间的划转"""
    def huazhuan(self, liang, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'transferId': str(uuid.uuid4()),
            'coin': bi,
            'amount': str(liang),
        }
        if side=='in':
            data['fromAccountType'] = 'SPOT'
            data['toAccountType'] = 'CONTRACT'
        else:
            data['fromAccountType'] = 'CONTRACT'
            data['toAccountType'] = 'SPOT'


        data = self.go("POST", "/asset/v3/private/transfer/inter-transfer", data)

        log('[Bybit划转]', '现货转入合约' if side == 'in' else '合约转入现货', '币种', bi, liang, '结果', data, Color('', 1))
        return 'retMsg' in data and data['retMsg'] == 'success'
 

    """创建子账户"""
    def CreateNumber(self, username, beizhu=''):
        data = self.go("POST","/user/v3/private/create-sub-member", {'username': username, 'memberType': '1', 'switch': '1', 'note': beizhu})
        log('Bybit创建子账户', username, '返回', data)
        
        return data


    """撤销所有挂单"""
    def DeleteAllOrder(self, symbol=''):
        t = NowTime_ms()
        data = self.go("POST", "/contract/v3/private/order/cancel-all", {'settleCoin': 'USDT'})

        log('Bybit撤单', data, str(NowTime_ms()-t)+'ms')
        
        return data


    """撤销所有挂单"""
    def DeleteSymbolOrder(self, symbol):
        t = NowTime_ms()
        data = self.go("POST", "/contract/v3/private/order/cancel-all", {'symbol': symbol})

        print('Bybit撤单', data, str(NowTime_ms()-t)+'ms')
        
        return data