from exchangeV2.bian import bian
from exchangeWs.bian_ws import bian_ws
from exchangeV2.bian_spot_swap import bian_spot_swap
from exchangeWs.bian_spot_swap_ws import bian_spot_swap_ws
from exchangeV2.bitget import bitget
from exchangeWs.bitget_ws import bitget_ws
from exchangeV2.bitget_spot import bitget_spot
from exchangeWs.bitget_spot_ws import bitget_spot_ws
from exchangeV2.bybit import bybit
from exchangeWs.bybit_ws import bybit_ws
from exchangeV2.coinex import coinex
from exchangeWs.coinex_ws import coinex_ws
from exchangeV2.coinex_spot import coinex_spot
from exchangeWs.coinex_spot_ws import coinex_spot_ws
from exchangeV2.gate import gate
from exchangeWs.gate_ws import gate_ws
from exchangeV2.gate_spot import gate_spot
from exchangeWs.gate_spot_ws import gate_spot_ws
from exchangeV2.huobi import huobi
from exchangeWs.huobi_ws import huobi_ws
from exchangeV2.kucoin import kucoin
from exchangeWs.kucoin_ws import kucoin_ws
from exchangeV2.mexc_spot import mexc_spot
from exchangeWs.mexc_spot_ws import mexc_spot_ws
from exchangeV2.kucoin_spot import kucoin_spot
from exchangeWs.kucoin_spot_ws import kucoin_spot_ws
from exchangeV2.okx import okx
from exchangeWs.okx_ws import okx_ws
# from exchangeV2.zoomex import zoomex
# from exchangeWs.zoomex_ws import zoomex_ws
from exchangeV2.deepcoin import deepcoin
from exchangeWs.deepcoin_ws import deepcoin_ws

from main.config import *
from moneyV2.symbol import *


""" 需要用RESt更新余额的交易所
现货默认都要
"""
REST_YUER = ['BianSwap', 'Gate', 'Ku', 'Bitget', 'Coinex', 'Dydx']



def getExchange(name):

    rest = bian
    ws = bian_ws
    symbol = GetBnbSymbol
    order = ['FILLED', 'EXPIRED']
    feilv = [0, 8, 16]

    if name == 'BianSpotSwap':
        rest = bian_spot_swap
        ws = bian_spot_swap_ws
        symbol = GetBnbSpotSymbol
        order = ['FILLED', 'EXPIRED']

    elif name == 'Bitget':
        rest = bitget
        ws = bitget_ws
        symbol = GetBitgetSymbol
        order = ['full-fill', 'cancelled']

    elif name == 'BitgetSpot':
        rest = bitget_spot
        ws = bitget_spot_ws
        symbol = GetBitgetSpotSymbol
        order = ['full-fill','cancelled']

    elif name == 'Bybit':
        rest = bybit
        ws = bybit_ws
        symbol = GetBybitSymbol
        order = ['Filled', 'PendingCancel', 'Cancelled', 'Rejected']

    elif name == 'Coinex':  #和币安一直
        rest = coinex
        ws = coinex_ws
        symbol = GetCoinexSymbol
        order = ['ok']

    elif name == 'CoinexSpot':
        rest = coinex_spot
        ws = coinex_spot_ws
        symbol = GetCoinexSpotSymbol
        order = ['ok']

    elif name == 'Gate':
        rest = gate
        ws = gate_ws
        symbol = GetGateSymbol
        order = ['finished']

    elif name == 'Huobi':
        rest = huobi
        ws = huobi_ws
        symbol = GetHuobiSymbol
        order = [5, 6, 7]

    elif name == 'Ku':
        rest = kucoin
        ws = kucoin_ws
        symbol = GetKuSymbol
        order = ['done']

    elif name == 'MexcSpot':
        rest = mexc_spot
        ws = mexc_spot_ws
        symbol = GetMexcSpotSymbol
        order = [2, 3, 4, 5]

    elif name == 'Okx':
        rest = okx
        ws = okx_ws
        symbol = GetOkxSymbol
        order = ['filled', 'canceled']

    elif name == 'Dydx':
        from exchangeV2.dydx import dydx
        from exchangeV2.dydx_ws import dydx_ws
        rest = dydx
        ws = dydx_ws
        symbol = GetDydxSymbol
        order = ['CANCELED', 'FILLED']
        feilv = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]

    elif name == 'KuSpot':
        rest = kucoin_spot
        ws = kucoin_spot_ws
        symbol = GetKuSpotSymbol

    elif name == 'GateSpot':
        rest = gate_spot
        ws = gate_spot_ws
        symbol = GetGateSpotSymbol
        order = ['finish']

    # elif name == 'Zoomex':
    #     rest = zoomex
    #     ws = zoomex_ws
    #     symbol = GetZoomexSymbol
    #     order = ['Cancelled', 'Filled']

    elif name == 'Deepcoin':
        rest = deepcoin
        ws = deepcoin_ws
        symbol = GetDeepcoinSymbol
        order = ['Cancelled', 'Filled']


    return rest, ws, symbol, order, feilv




""" 黑名单交易对"""
def getNoSymbol():
    
    if CcyBi == 'USDT':
        noSymbol = ['BTC', 'ETH', 'SXP', 'USDC', 'ENJ']
    else:
        noSymbol = []

    if Exchange2 == 'Coinex':
        noSymbol += ['IOTA', 'RUNE'] #RUNE，6-14，亏了很多资金费率

    elif Exchange2 == 'CoinexSpot':
        noSymbol += ['SXP']

    elif Exchange2 == 'Gate':
        noSymbol += []

    elif Exchange2 == 'Huobi':
        noSymbol += []

    elif Exchange2 == 'Ku':
        noSymbol += ['CELR', 'PERP', 'MASK']

    elif Exchange2 == 'Okx':
        noSymbol += []

    elif Exchange2 == 'Dydx':
        noSymbol = []

    elif Exchange2 == 'KuSpot':
        noSymbol += ['COTI']
        
    return noSymbol


""" 交易对开仓平仓阈值拉高"""
def getNoTradeSymbol():
    
    noTradeSymbol = ['ID', 'DOGE']

    if Exchange2 == 'Okx':
        noTradeSymbol += []#['OP', 'OMG', 'LDO', 'SOL', 'EOS']

    if Exchange2 == 'Huobi':
        """ 2023-11-10 Huobi ksm和axs开仓了三个月都没平仓"""
        noTradeSymbol += ['MATIC', 'XRP', 'AXS', 'BLUR', 'KSM', 'AXS']

    if Exchange2 == 'Gate':
        noTradeSymbol += []#['MASK', 'XRP', 'EOS', 'MTL']

    if Exchange2 == 'Bybit':
        noTradeSymbol += ['CTSI']

    if Exchange2 == 'Ku':
        noTradeSymbol += ['CFX', 'STORJ', 'ZIL', 'LRC', 'WLD']

    if Exchange2 == 'Bitget':
        noTradeSymbol += ['CHZ', 'USDC', 'KSM', 'AUDIO', 'BLZ']        #持仓一个月了还不平仓，audio太差了

    elif Exchange2 == 'Dydx':
        noTradeSymbol = []

    elif Exchange2 == 'Coinex':
        noTradeSymbol += ['CRV', 'LPT'] #8-9 LPT每个号亏了200u费率

    elif Exchange2 == 'BitgetSpot':
        noTradeSymbol += ['XRP']
    
    return noTradeSymbol


""" 要拆单交易的交易所"""
def getChaiDan():
    u = 0

    if Exchange2 == 'Okx':
        u = 5000

    return u


""" 偏离达到次数才下单的交易所"""
def getPianliLimit():
    limit = 0

    if 'Okx' in [Exchange, Exchange2]:
        limit = 2

    if 'Bybit' in [Exchange, Exchange2]:
        limit = 3

    # if 'Gate' in [Exchange, Exchange2]:
    #     limit = 2

    return limit


""" 币安盘口延迟的交易所"""
def getYcliLimit():
    limit = 150

    if 'Coinex' in [Exchange, Exchange2]:
        limit = 100

    if 'Bitget' in [Exchange, Exchange2]:
        limit = 100

    if 'Okx' in [Exchange, Exchange2]:
        limit = 50

    if 'Huobi' in [Exchange, Exchange2]:
        limit = 30

    if 'Gate' in [Exchange, Exchange2]:
        limit = 60

    if 'Ku' in [Exchange, Exchange2]:
        limit = 50


    return limit

