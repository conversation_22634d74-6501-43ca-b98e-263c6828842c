from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from moneyV2.symbol import *

import random


def DeleteOrder(self, symbol):
    if self.preJia:
        self.bnb.DeleteAllOrder(symbol)
        self.preJia = 0
    
    self.startJia = 0

""" Taker"""
def Taker(self, symbol, data, isbnb=0):
    
    J, L, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['Min'], self.symbol['Z']
    posGg = self.ganggan

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])

    pankou = [
        symbol, '延迟', NowTime_ms()-data['t'], '',
        'bid', str(bid)+' ('+str(bidQty)+')'+' ('+U(bidQty*bid*MZ)+')', 'ask', str(ask)+' ('+str(askQty)+')'+' ('+U(askQty*ask*MZ)+')'
    ]


    maxPos = MAX
    if self.symbol['M'] and self.symbol['M']:
        maxPos = self.symbol['M']*0.98

    if 'maxJiazhi' in self.symbol and self.symbol['maxJiazhi']:
        maxPos  = min(maxPos, N(self.symbol['maxJiazhi'] / ((bid+ask) * 0.5) * 0.98, L))       #最大持仓量
    
    if 'maxLiang' in data and data['maxLiang']:
        maxPos = min(maxPos, data['maxLiang']*0.98)


    """ 持仓价值"""
    jiazhi = 0
    pos  = getCaokongPos(self, Exchange, symbol, bid, MZ, L)
    pos_limit = self.pos_limit
    # if Exchange == 'Deepcoin':
    pos_limit = min(self.pos_limit, maxPos*bid)

    if pos:
        jiazhi = round(pos['liang']*MZ*ask, 2)
        pos_limit -= jiazhi
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1), '面值', MZ], 3)
            self.prePos = pos['liang']

    if pos_limit > maxPos*bid:
        tlog('最大持仓量限制', [maxPos, U(maxPos*bid, 1)], 2*60)
        pos_limit = maxPos*bid
    
    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, maxPos)

    def ClosePos():
        
        """ 清理所有仓位"""
        if pos and pos['liang'] * bid * MZ > 11:
            
            """ 一次性"""
            jiage   =  bid if pos['side'] == 'BUY' else ask
            duishou =  bidQty*bid*MZ if pos['side'] == 'BUY' else askQty*ask*MZ
            duishou = max(duishou, 5000)
            duishou = min(duishou, 30000)

            if self.startJia and NowTime_ms() - self.preTime < 4000 and pos:
                if pos['side'] == 'BUY' and jiage < self.startJia and abs(self.startJia - jiage) / jiage > 0.0003:
                    return log('平多', '滑点太大 等待中...', '初始', self.startJia, '当前', jiage)

                if pos['side'] == 'SELL' and jiage > self.startJia and abs(self.startJia - jiage) / jiage > 0.0003:
                    return log('平空', '滑点太大 等待中...', '初始', self.startJia, '当前', jiage)

            if self.startJia and NowTime_ms() - self.preTime < 500:
                return '频率过高'

            log(pos['side'], jiazhi, '平仓', '当前价', jiage, '对手', U(duishou))
            # print(self.startJia, self.preTime, abs(self.startJia - jiage) / jiage)
            
            if jiazhi > duishou:
                taker = N(duishou / jiage / MZ, L)
            else:
                taker = pos['liang']

            # taker = N(30 / jiage / MZ, L)

            order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], jiage, taker, 'normal', 1)
            self.preTime = NowTime_ms()
            log(*msg)
            if not order_id:
                self.error += 1
                if self.error > 2:
                    os._exit(0)
            else:
                self.error = 0
                if not self.startJia:
                    self.startJia = jiage

        else:
            tlog('平仓完毕', pos, 60)

    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 10)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        return tlog(f'{Name} 策略已暂停...', '', 60)

    elif 'open' in self.side:

        """ 快速建仓"""

        # if self.stopOpen:
        #     data = [pos['side'], U(jiazhi)] if pos else []
        #     return tlog(f'{symbol} 任务已完成 等待指令...', data, 30)
        
        side = 'BUY' if 'Buy' in self.side else 'SELL'
        jiage = bid if side == 'BUY' else ask 
        taker = min(self.ws.keyong * posGg * 0.99, pos_limit)
        duishou =  bidQty*bid*MZ if side == 'SELL' else askQty*ask*MZ
        duishou = max(duishou, 5000)
        duishou = min(duishou, 30000)

        """ 先平仓"""
        if pos and side != pos['side'] and pos['liang'] * bid * MZ > 15:
            return ClosePos()

        if jiazhi >= self.pos_limit or taker < 11:
            tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(self.usdt), '目标', U(taker)], 3)
            self.stopOpen = 1
            return

        if self.startJia and NowTime_ms() - self.preTime < 4000 and pos:
            if pos['side'] == 'BUY' and jiage < self.startJia and abs(self.startJia - jiage) / jiage > 0.0003:
                return log('开多', '滑点太大 等待中...', '初始', self.startJia, '当前', jiage)

            if pos['side'] == 'SELL' and jiage > self.startJia and abs(self.startJia - jiage) / jiage > 0.0003:
                return log('开空', '滑点太大 等待中...', '初始', self.startJia, '当前', jiage)

        if self.startJia and NowTime_ms() - self.preTime < 500:
            return '频率过高'

        log(side, '开仓', '当前价', jiage, '对手', U(duishou))
        # print(self.startJia, NowTime_ms(), self.preTime, NowTime_ms() - self.preTime, abs(self.startJia - jiage) / jiage)

        if taker > duishou:
            taker = duishou

        # taker = 30

        liang = N(taker / jiage / MZ, L)
        if liang < Min:
            tlog('小于最小下单量', ['量', liang, '最小下单', Min], 3)
            self.stopOpen = 1
            return

        order_id, msg = self.bnb.PostOrder(symbol, side, jiage, liang, 'normal')
        self.preTime = NowTime_ms()
        log(*msg)

        if not order_id:
            self.error += 1
            if self.error > 2:
                os._exit(0)
        else:
            self.error = 0
            if not self.startJia:
                self.startJia = jiage

    else:
        ClosePos()

"""
更新挂单条件
1. 没有挂过单
2. 开多，价格上涨了
3. 开空，价格下跌了
4. 超过10s
"""
def ifUpdate(side, preJia, bid, ask):
    if not preJia:
        return True
    if side == 'BUY' and preJia < bid:
        return True
    if side == 'SELL' and preJia > ask:
        return True
    if (side == 'BUY' and preJia != bid) or (side == 'SELL' and preJia != ask):
        if tlog('更新挂单', 'wait 10s', 10):
            return True
    return False


""" Maker"""
def Maker(self, symbol, data, isbnb):
    
    J, L, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['Min'], self.symbol['Z']
    bid, ask = float(data['bidPx']), float(data['askPx'])
    mid = (bid+ask) * 0.5
    posGg = self.ganggan

    maxPos = MAX
    if self.symbol['M'] and self.symbol['M']:
        maxPos = self.symbol['M']*0.99

    if 'maxJiazhi' in self.symbol and self.symbol['maxJiazhi']:
        maxPos  = min(maxPos, N(self.symbol['maxJiazhi'] / ((bid+ask) * 0.5) * 0.99, L))       #最大持仓量
    
    if 'maxLiang' in data and data['maxLiang']:
        maxPos = min(maxPos, data['maxLiang']*0.99)

    """ 持仓价值"""
    jiazhi = 0
    pos  = getCaokongPos(self, Exchange, symbol, bid, MZ, L)
    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, maxPos)

    pos_limit = min(self.pos_limit, maxPos*bid)
    buy_limit, sell_limit = pos_limit, pos_limit

    buy_pos = GetNewPos(self, Exchange, symbol, 'BUY', bid, MZ, L)
    sell_pos = GetNewPos(self, Exchange, symbol, 'SELL', bid, MZ, L)

    if buy_pos:
        buy_limit -= buy_pos['liang']*mid*MZ

    if sell_pos:
        sell_limit -= sell_pos['liang']*mid*MZ


    if pos:
        if self.prePos != pos['liang']:
            log('持仓价值', U(round(pos['liang']*ask*MZ, 2), 1))
            self.prePos = pos['liang']

    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        return tlog(f'{Name} 策略已暂停...', '', 60)

    if self.stopOpen:
        return tlog(f'{symbol} 挂单已完成 等待指令...', '', 30)
    

    """ 平仓单"""
    if 'open' not in self.side and not pos:
        DeleteOrder(self, symbol)
        return tlog('平仓完毕', pos, 60)
    
    
    if 'open' in self.side:
        
        side = 'BUY' if 'Buy' in self.side else 'SELL'
        if side == 'BUY':
            taker = min(self.ws.keyong * posGg * 0.99, buy_limit)
        else:
            taker = min(self.ws.keyong * posGg * 0.99, sell_limit)

        liang = N(taker / mid / MZ, L)

        """ 这里是反的 吃对手盘"""
        jiage = ask-self.limit_bili*ask/100 if 'Buy' in self.side else bid+self.limit_bili*bid/100
        jiage = N(jiage, J)
        fuck = 0

        if pos and side != pos['side']  and pos['liang'] * bid * MZ > 15:
            DeleteOrder(self, symbol)
            fuck = 1
            order, msg = self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'limit', jiancang=1)
            log(*msg)
            if not order:
                os._exit(0)
            else:
                self.preJia = jiage

        if liang * mid * MZ > 15:
            if not fuck:
                DeleteOrder(self, symbol)

            for i in range(3):
                order, msg = self.bnb.PostOrder(symbol, side, jiage, liang, 'limit')
                log(*msg)
                if order:
                    self.preJia = jiage
                    self.error = 0
                    self.stopOpen = 1
                    break
                else:
                    liang = N(liang * 0.5, L)

            else:
                os._exit(0)
    
    else:
        if pos and pos['liang'] * bid * MZ > 15:
            
            """ 这里是反的 吃对手盘"""
            jiage = ask-self.limit_bili*ask/100 if pos['side'] == 'SELL' else bid+self.limit_bili*bid/100
            jiage = N(jiage, J)

            DeleteOrder(self, symbol)
            order, msg = self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'limit', jiancang=1)
            log(*msg)
            if order:
                self.preJia = jiage
                self.error = 0
                self.stopOpen = 1
            else:
                os._exit(0)
    

""" Getswap"""
def Getswap(self, symbol, data, isbnb=0):

    if self.order == 'Maker':
        Maker(self, symbol, data, isbnb)

    else:
        Taker(self, symbol, data, isbnb)
