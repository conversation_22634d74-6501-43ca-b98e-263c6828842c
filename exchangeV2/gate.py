#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import hmac
import hashlib
import requests
import datetime
import time
import ujson
import traceback        #输出报错
from pprint import pprint
from urllib.parse import parse_qs, urlencode

from main.config import *
from main.hanshu import *


def get_sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return base64.b64encode(d)

def pre_hash(timestamp, method, request_path, body):
    return str(timestamp) + str.upper(method) + request_path + body

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'

    return url[0:-1]

def get_timestamp():
    now = datetime.datetime.utcnow()
    t = now.isoformat("T", "milliseconds")
    return t + "Z"


class gate():

    def __init__(self, apiKey=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['32fc6595425a42235cf930a04804ec65', 'd0a08bbc8288aba2201e0edc6822e5a7eacaa791bcbcd3696945b474a812c54d'],
            'partner001': ['b9fe778a48ef2e217e89b425296e5d26', 'b12c5e6a61ab394e31c1d9b63ebd5bb84b59cf8a446dd6280508c67048df8a7d'],
            'partner002': ['7e92df2c4f1f1e2a28e5e2b26f7c7eef', '7852ba9860b7cfd9f19ef20156f7f429faddae57a27f998d9ada75c80c94eeb2'],
            'partner003': ['578f7f8c6a2155e986d8feec2a1225fa', 'd627b65a46f244d5dc1ae573c4b3f1f7b9f39e801c097fa616a4f008664a0d46'],
            'partner004': ['2b666e37c5ec2862a0b7752e11d3cecb', 'ea866d4dc24fda8dca781ae2e32fddb874870dcc04ed75960d834b94676f2e79'],
            'partner005': ['9ca0bae5d1a10b514edd472b804616c8', 'beab4e35c1a86ee0bf903fb8a39cfb53585861e113afa1206b6e01e7d59bee65'],
            'partner006': ['6f291dc6c39bd515c5e9e9b0aa803295', '327464863e54b44b03ce42dfa8ed57c0c78e25598911d6f118bd8aca3163623f'],
            'partner007': ['c17edf305369d498ccb36101d44cecb2', '5622df1fbf384f87f590930d90debe2161de682b3632b837baf11c124308c002'],
            'partner008': ['39fe16842a44cfedabaf1586f027de3a', '9b127a7ee9475cd9a092b61457928e03df8e3883b44a98716e4ad84bb775a352'],
            'partner009': ['c92ca2b0906688f63e3a63e867e6b752', '3e09c0915926d1a63726725de7d32280989387bb042d455026f1d9402cbc96ee'],
            'partner010': ['01c0b293256931f00d868ab6efeb9504', '51537f808047cbc8fc6264fd6da852444be4944a38af22c43afa03bf7120593b'],
            'partner011': ['7266185ebd1a20ae1e67fb5562f7fc97', '21150101e012f04faa923b076b628cb7b65d5eb8c3f2c093f619ffbcc9ede28d'],
            'partner012': ['0fbe1ce489134eb651b2093bdf697292', 'eae7d66b9823301f83ad8aa78270830bc124b6a29500954112986b6d53c8a802'],

            'gate下级': ['cd9ee26f26eb5a5dc25a501d217b2990', '359bc57fa51c5acbcded92a58da4df05c651f3d7bd844b81507a54e90688c0b5'],
            'gate1': ['ff9723fb3260b6d33c39278723a9dc45', '26d40056aa4c3086691dc828657c1b210289599aa36c73b5153c43f819fa7d28', '', '', ''],
            'gate2': ['564d4256998a23c9052e38f911278bfd', '962616c38e56b53346c4c631142fc3ac71a7b5177d1d1d9102d24dd082129ef8', '', '', ''],
            'gate3': ['770f8f9864117d9ab22e24f1381a1a96', '096590170ba1bb14ee8786214f60ea2fbb3dc1ae820066a9fd4c1232335ba811', '', '', ''],
            'gate4': ['c499ffe17dcb01d4aa507d3e42a0ac88', '419b38f95e311cc68eb4fceade270fdd26e50e87b05639309c765e422f0c6c43', '', '', ''],
            'gate5': ['9dcea456e1b9a29897cf93a550d778b8', '416b78e2562b4f3c2103a0cc18909d2ea5e6a118c183cbb79ffe6073e2e55386', '', '', ''],
            'gate6': ['bf067f27952cde99934af4053c333dfd', 'f4e908d93f761325ac686546f2cb73956c209cdad35834565cb90e395c8a5775', '', '', ''],
            'gate7': ['1b6d65cdae67a5f0696b59d60b2d92ff', 'd23606babe1afb52ef6281c530836398d5187416b8d9a99761f324de698dd1cd', '', '', ''],
            'gate8': ['4d9a6bdbe91e1a916da43e8080bb8c35', '0855abfbff35775c2dd8fe6e5d787c6f6cecaa2c0427e6e2b24e11d85a3d087c'],
            'v16': ['2aadeca376ced95ab8ba05f94274b07f', '6b35d68de611ee0553a43a11706aef3fc0e523a0e37aae4252cefae706df86b3'],
            'v16_1': ['75ca71f9ac4fca1c4e3c34ed15d3d597', '394f081e449d98e6d72b6d60048c4fbb87332cf419244a959bad36bad41bff41'],

        }

        self.debug = 0
        self.debugs = []

        apiKey = apiKey if apiKey else Api_Key2
        if apiKey in keys:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        else:
            apiKey = GetApiKey(apiKey, 'Gate')
        self.access_id = apiKey[0]
        self.secret_key = apiKey[1]
        self.symbol = symbols
        try:
            if GateColo:
                log('Gate Rest 使用colo高速线路')
                self.HOST = 'https://apiv4-private.gateapi.io'
            else:
                self.HOST = 'https://api.gateio.ws'
        except:
            self.HOST = 'https://api.gateio.ws'


        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}

    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')


    def generate_signature(self, method, uri, query_param=None, body=None):
        t = time.time()
        m = hashlib.sha512()
        m.update((body or "").encode('utf-8'))
        hashed_payload = m.hexdigest()
        s = '%s\n%s\n%s\n%s\n%s' % (method, uri, query_param or "", hashed_payload, t)
        sign = hmac.new(self.secret_key.encode('utf-8'), s.encode('utf-8'), hashlib.sha512).hexdigest()
        return {'KEY': self.access_id, 'Timestamp': str(t), 'SIGN': sign}



    def set_authorization(self, url, method, params, body):
        url2 = url
        if method == "GET":
            headers = {
                "Content-type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/39.0.2171.71 Safari/537.36"
            }
            query_param = ''
            for i in params:
                query_param += f'{i}={params[i]}&'
            query_param = query_param[:-1]
            sign_headers = self.generate_signature(method, url2, query_param)
            headers.update(sign_headers)

        else:
            headers = {
                "Accept": "application/json",
                "Content-type": "application/json"
            }
            query_param = ''
            if params:
                for i in params:
                    query_param += f'{i}={params[i]}&'
                query_param = query_param[:-1]
                url += "?"+ query_param
            if body:
                body = ujson.dumps(body)

            # print(url2, query_param, "|", body)
            sign_headers = self.generate_signature(method, url2, query_param, body)
            headers.update(sign_headers)

        self.session.headers = headers
        return url


    # 请求API
    def go(self, method, url_path, payload=None, body=None):
    

        # url
        url = self.HOST + self.set_authorization(url_path, method, payload, body)


        
        params = {'url': url, 'timeout': 10}

        if method in ['POST', 'DELETE']:
            if body:
                params['data'] = ujson.dumps(body)
        else:
            params['params'] = payload

        if self.debug:
            t = NowTime_ms()

        try:
            response = self.dispatch_request(method)(**params)
            if url_path == '/api/v4/wallet/sub_account_transfers':
                return response.status_code == 204

            if response.text:
                response2 = response.json()
            else:
                response2 = ''

            # if url_path == '/api/v4/futures/usdt/orders' and 'id' not in response2:
            #     uploadError('Gate下单失败：'+str(method)+' | '+str(url_path)+' | '+str(payload)+' | '+str(body)+' | '+'生成信息：'+url+' | '+str(params)+' 返回'+str(response2))


        except Exception as e:
            traceback.print_exc()

            uploadError(toMsg(['Gate 请求API失败', str(method), str(url_path), str(payload), str(body), '生成信息', url, str(params), '报错', traceback.format_exc()]))
            response2 = ''

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(url, payload, body, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2

    """ 获取USERID"""
    def GetUserId(self):
        data = self.go("GET","/api/v4/wallet/fee", {})
        if 'user_id' not in data:
            log("gate获取user_id 错误", data)
            time.sleep(1)
            return self.GetUserId()
        return data['user_id']


    """ 获取盘口挂单"""
    def GetDepth(self, symbol, limit=1):
        params = {'contract': symbol, 'limit': limit}

        data = self.go('GET','/api/v4/futures/usdt/order_book', params)

        if 'asks' not in data:
            log(Color(symbol+' 获取深度失败', -1), data)
            time.sleep(0.01)
            return self.GetDepth(symbol, limit)
        data['t'] = data['update'] * 1000
        data['contract'] = symbol
        return data


    """获取可用余额"""
    def GetYuer(self, p=1):
        data = self.go("GET","/api/v4/futures/usdt/accounts", {})
        no = 0
        ok = 0
        if 'available' in data:
            ok = data

        if not ok:
            no = 1

        if no:
            print('Gate 获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)

        ok['all'] = Si(float(ok['total'])+float(ok['unrealised_pnl']) if float(ok['total']) > 0.01  else 0.01, 4)
        ok['keyong'] = N(abs(float(ok['available'])) if float(ok['available']) > 0.01 else 0.01, 4)
        ok['yingkui'] = N(float(ok['unrealised_pnl']), 4)
        if p:
            log(p+'  Gate USDT总余额' if type(p) == str else 'Gate USDT总余额', ok['all'], '可用', ok['keyong'], '持仓盈亏', ok['yingkui'])

        return ok


    """设置双向持仓"""
    def SetChicang(self, dual_mode='true'):
        fh = self.go('POST', "/api/v4/futures/usdt/dual_mode", {'dual_mode': dual_mode})
        log('Gate设置持仓', '双向' if dual_mode == 'true' else '单向', fh)


    """获取所有交易对信息"""
    def GetSymbols(self):
        data = self.go('GET', '/api/v4/futures/usdt/contracts', {})
        okdata = []
        for v in data:
            v['symbol'] = v['name']
            v['time'] = NowTime()

            # if v['symbol'] == 'CHR_USDT' or v['symbol'] == 'BTC_USDT':
            #     pprint(v)

            okdata.append(v)
        return okdata


    """ 获取所有交易对资金费率"""
    def GetSymbolFeilv(self, symbol=0):
        if not self.feilvData or not symbol:
            data = self.GetSymbols()
        
            if not data or type(data) != list or type(data[0]) == str:
                print('获取资金失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            if not symbol:
                return data

            for v in data:
                self.feilvData[v['symbol']] = v

        try:
            symbolData = self.feilvData[symbol]
        except:
            symbolData = 0

        if not symbolData or NowTime() >= symbolData['funding_next_apply']+10:
            log(symbol, '更新资金费率',
                 '更新时间', GetTime(symbolData['funding_next_apply'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
                 )

            self.feilvData = {}
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['funding_rate'])*100, 4)



    """设置杠杆倍数和持仓方向"""
    def SetGangGan(self, symbol, beishu):
        if not beishu:
            return
        post = {
            'leverage': str(0), #调整成全仓
            'cross_leverage_limit': str(beishu),
        }
        fh = self.go('POST', f"/api/v4/futures/usdt/positions/{symbol}/leverage", post)
        log(symbol, 'Gate设置杠杠', beishu, 'ok' if 'value' in fh else fh)
        
        if 'detail' in fh:
            beishu = fh['detail'].split(', ')[-1].split(']')[0]
            return self.SetGangGan(symbol, beishu)

        time.sleep(0.1)



    """ 获取所有持仓"""
    def GetPos(self):
        
        data = self.go('GET', '/api/v4/futures/usdt/positions', {})

        try:
            if type(data) == list:
                okdata = data
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['contract']
                    ok['liang'] = abs(v['size'])
                    if not ok['liang']:
                        continue
                    
                    ok['side'] = 'BUY' if v['size'] > 0 else 'SELL' #持仓方向
                    ok['side2'] = 'SELL' if v['size'] > 0 else 'BUY' #持仓方向

                    ok['jiage'] = float(v['entry_price'])
                    ok['nowJiage'] = float(v['mark_price'])    #最新成交价格
                    ok['yingkui'] = N(float(v['unrealised_pnl']), 4)
                    ok['bzj'] = N(float(v['margin']), 4)
                    ok['roe'] = 0

                    data.append(ok)

                return data

            else:
                log(Color("Gate套利 获取持仓失败", -1), data)
                time.sleep(1)
                return self.GetPos()

        except Exception as e:
            uploadError(traceback.format_exc())
        
        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """撤销所有订单"""
    def DeleteAllOrder(self, symbol, msg=''):
        t = NowTime_ms()
        msg = self.go("DELETE", "/api/v4/options/orders", body={'contract':  symbol} )

        log(symbol, "撤销所有挂单", msg, str(NowTime_ms()-t)+'ms')


    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        jiage = str(jiage)

        liang = int(liang) if side == 'BUY' else int(int(liang)*-1)
            
        msg = 'Gate　'+symbol+'　'

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+str(liang)+"　减仓:"+str(jiancang)+"　Type:"+type

        if type == 'normal':
            jiage = '0'
            type = 'ioc'

        if type == 'post_only':
            type = 'poc'

        post = {
            'contract': symbol,
            'size': liang,
            'price': jiage,
            'tif': type,
        }

        if jiancang:
            post['reduce_only'] = True

        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST',"/api/v4/futures/usdt/orders", body=post)
            # log("下单返回", order)
            if 'id' not in order:
                log(symbol+" [!!!] 套利下单失败！！", post, order)
            else:
                orderId = order['id']
                break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg


    """ 子母账户相互转账"""
    def ziHua(self, liang, uid, side, bi='', ziWallet='futures'):
        bi = bi if bi else CcyBi
        data = {
            'currency': bi, 
            'sub_account': str(uid),
            'amount': str(liang),
            'direction': 'to' if side == 'in' else 'from',
            'sub_account_type': ziWallet,
        }

        print(data)
        data = self.go("POST", "/api/v4/wallet/sub_account_transfers", body=data)
        print(data)
        log('[Gate子母]', '主账户转入' if side == 'in' else '子账户转出', '币种', bi, liang, '结果', data, Color('', 1))

        if data == '':
            data = True
        return data
    

    """提现"""
    def Tixian(self, dizhi, liang, bi='', net='TRX'):
        bi = bi if bi else CcyBi
        post = {
            'currency': bi,
            'address': dizhi,
            'amount': str(liang),
            'memo': "",
            'chain': net,
        }

        data = self.go("POST", "/api/v4/withdrawals", body=post)
        log('Gate 提现 地址', dizhi, '数量', liang, '币种', bi, '结果', data)
        return data


    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi

        data = self.go('GET', f'/api/v4/spot/accounts', {'currency': bi})
        if not data:
            log('Gate获取现货失败', data)
            time.sleep(1)
            return self.GetXianhuo(bi)
            
        return Si(data[0]['available']) if type(data) == list else 0.01


    """ 获取USERID"""
    def GetUserId(self):
        fh = self.go('GET', '/api/v4/wallet/fee', {})
        try:
            return int(fh['user_id'])
        except:
            print(self.access_id,self.secret_key)
            uploadError(str(fh))
            time.sleep(5*60)
            return self.GetUserId()


    """市价平仓"""
    def ClosePos(self, symbol, liang=0, jiage=0, side=''):

        t = NowTime_ms()
        
        params = {
            'instId': symbol,
            'mgnMode': 'cross',
        }
        fh = self.go('POST', "/api/v5/trade/close-position", params)

        log("Gate", Color("TakerClose 套利单腿成交", -1), side, symbol,
         "价格", jiage, "量", str(liang)+' ('+STR_N(liang*jiage)+'$)', str(NowTime_ms()-t)+'ms', fh['data'])