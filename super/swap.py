from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from moneyV2.symbol import *

import random
import time

def DeleteOrder(self, symbol, msg=''):
    if self.preJia:
        self.bnb.DeleteAllOrder(symbol, msg=msg)
        # for orderId in self.orderId:
        #     self.bnb.DeleteOrder(symbol, orderId)

        self.preJia = 0
        self.preJia2 = 0
        self.takerCount = 0
        self.orderId = []
        self.preLiang = 0        #上次挂单数量
        Sleep(50)
        self.upYuer()


""" 合约买入卖出"""
def Swap(self, symbol, data, isbnb=0):

    J, J2, L, Min, MZ      = self.symbol['J'], self.symbol['J2'], self.symbol['L'], self.symbol['Min'], self.symbol['Z']
    posGg = self.ganggan

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])
    mid = (bid + ask) * 0.5


    maxPos = MAX

    """ 最大市价单"""
    # if self.symbol['M'] and self.symbol['M']:
    #     maxPos = self.symbol['M']*0.99

    """ 持仓上限制"""
    if 'maxJiazhi' in self.symbol and self.symbol['maxJiazhi']:
        maxPos  = min(maxPos, N(self.symbol['maxJiazhi'] / ((bid+ask) * 0.5) * 0.99, L))       #最大持仓量

    if 'maxLiang' in data and data['maxLiang']:
        maxPos = min(maxPos, data['maxLiang']*0.99)

    """ 持仓价值"""
    jiazhi = 0
    pos  = getCaokongPos(self.ws.pos, Exchange, symbol, bid, MZ, L)

    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, maxPos)

    pos_limit = self.pos_limit
    if pos:
        jiazhi = round(pos['liang']*ask, 2)

        pos_limit -= jiazhi
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1)], 3)
            self.prePos = pos['liang']

    if pos_limit > maxPos*bid:
        tlog('最大持仓量限制', [maxPos, U(maxPos*bid, 1)], 2*60)
        pos_limit = maxPos*bid


    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        return tlog(f'{Name} 策略已暂停...', '', 60)

    elif 'open' in self.side:
        side = 'BUY' if 'Buy' in self.side else 'SELL'
        jiage = bid-J2*2 if side=='BUY' else ask+J2*2       #挂买三 或 卖三
        jiage = N(jiage, J)

        # if jiazhi >= self.pos_limit and pos['side'] == side:
        #     DeleteOrder(self, symbol)
        #     tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(self.ws.usdt*posGg), '目标', U(self.pos_limit)], 3)
        #     return

        if self.stopOpen:
            return tlog('操控趋势已经完成', ['停机'], 60)

        """ 重置"""
        if self.preSide and self.preSide != side:
            DeleteOrder(self, symbol)

        if self.preJia and side == "SELL" and self.preJia <= ask + J2:
            DeleteOrder(self, symbol)
            log(f"preJia {self.preJia}")
            log('SELL 价格已经到卖2了，撤单，下一个BBO重新挂单', '', 3)
            self.time_range_counter.record()
            return

        if self.preJia and side == "BUY" and self.preJia >= bid - J2:
            DeleteOrder(self, symbol)
            log(f"preJia{self.preJia}")
            self.time_range_counter.record()
            log('BUY 价格已经到买2了，撤单，下一个BBO重新挂单', '', 3)
            return

            # 5 秒钟之内反向改单超过三次，则暂停10s中再操作
        if self.time_range_counter.count_recent() > 3:
            log('反向改单超过三次，暂时暂停下单')
            return
        # if self.preJia == ask or self.preJia == bid:
        #     DeleteOrder(self, symbol)
        #     return tlog('差点成交了!!!!!', '', 3)

        if not tlog('差点成交了!!!!!', '', 3, xs=0, look=1):
            return 0

        """ 监控价格是否超过阈值"""
        if self.preJia:

            bili = (abs(jiage-self.startJia)) / jiage * 100
            if bili >= self.bili:
                DeleteOrder(self, symbol)
                log('超过阈值！！！！！！！！！！！！！！！ 停机', StrBi(bili/100))
                self.stopOpen = 1
                return

            elif jiage != self.preJia2:
                log('趋势操控中', '--->', StrBi(bili/100))
                self.preJia2 = jiage

        liang = self.ws.keyong * posGg * 0.99 / mid
        if pos and pos['side'] != side:
            liang += pos['liang']
        if pos and pos['side'] == side:
            maxPos -= pos['liang']
        liang = N(min(liang, maxPos), L)

        if liang*mid > self.pos_limit:
            liang = N(self.pos_limit / mid, L)



        """ 首次挂单"""
        if not self.preJia:
            # print(self.ws.keyong, posGg, maxPos, U(maxPos*jiage))
            order_id, msg = self.bnb.PostOrder(symbol, side, jiage, liang, 'GTX')
            if order_id:
                self.preJia = jiage
                self.preJia2 = jiage
                self.preLiang = liang
                self.preSide = side
                if not self.startJia:
                    self.startJia = jiage
                self.orderId.append(order_id)
            else:
                time.sleep(1)
                os._exit(0)

        #修改价格
        # elif (side == 'BUY' and jiage > self.preJia) or (side == 'SELL' and jiage < self.preJia):
        # 如果当前价格已经不是3档了，直接改单, 包括改低和改高
        elif jiage != self.preJia:
            if side == "BUY" and jiage < self.preJia:
                self.time_range_counter.record()
            if side == "SELL" and jiage > self.preJia:
                self.time_range_counter.record()
            fh = None
            try:
                # 改单的时候应该使用最新计算出来的qty, 否则可能操作最大持仓名义价值限制导致改单失败
                modify_liang = N(min((self.preJia * self.preLiang) / jiage, maxPos), L)
                if modify_liang * jiage < 200:
                    log("反向操作吧，达到最大仓位了！！！！！！！！")
                    log("没钱了!!!!!!!!!!!!!!，已经接近最大仓位了")
                else:
                    fh = self.bnb.EditOrder(side, symbol, jiage, modify_liang, self.orderId[0])
            except:
                os._exit(0)
            if fh:
                self.preJia = jiage
                self.preLiang = modify_liang
            else:
                DeleteOrder(self, symbol)


    else:

        """ 清理所有仓位"""
        if pos and pos['liang'] * bid > 11:

            if 'closeAll' == self.side:
                """ 一次性"""
                # 撤销上次挂单
                self.preJia = bid
                DeleteOrder(self, symbol)

                x = 0.0015
                jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                jiage = N(jiage, J)
                jiage2 = bid  if pos['side'] == 'BUY' else ask

                log(pos['side'], jiazhi, '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'GTC', 1)
                self.preJia = bid

                Sleep(5000)
                upPos(self, self.bnb, self.ws, set=1)

            else:
                #最大砸盘金额
                maxTaker = 3000

                """ 挂卖一"""
                if jiazhi > maxTaker+100 and bid != self.preJia:

                    liang = N((jiazhi-maxTaker) / bid * 0.99, L)
                    if liang * bid > 100:
                        DeleteOrder(self, symbol)
                        order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], ask if pos['side'] == 'BUY' else bid, liang, 'GTX', 1)
                        if order_id:
                            self.preJia = bid

                duishou = bidQty * bid
                isFuck = duishou < 300
                """ 缓慢砸盘"""
                if NowTime_ms() >= self.SujiTime or isFuck:
                    liang = random.randint(50, 300)
                    duishou = bidQty * bid  if pos['side'] == 'BUY' else askQty * ask

                    """ 每6次吃一次对手盘"""
                    if self.takerCount and self.takerCount % 6 == 0:
                        liang = max(duishou*1.1, random.randint(1000, 1500))
                        liang = min(liang, maxTaker)

                    print(f'--------第 {self.takerCount} 次--------')
                    print('对手', U(duishou),
                        ' ',
                        '持仓', U(jiazhi),
                            '-------> 本次砸盘', U(min(jiazhi, liang)),'\n')

                    liang = N(liang / bid, L)
                    liang = min(liang, pos['liang'])

                    DeleteOrder(self, symbol)
                    order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], bid, liang, 'normal', 1)
                    if order_id:
                        self.takerCount += 1

                    waitTime = random.randint(1500, 4800)
                    self.SujiTime = NowTime_ms() + waitTime
                    print('等待', round(waitTime / 1000, 1), 's\n')

                    # Sleep(500)
                    # upPos(self, self.bnb, self.ws, set=1)


        else:
            if tlog('平仓完毕', pos, 60):
                delPos(self, symbol, 'BUY')
                delPos(self, symbol, 'SELL')
                time.sleep(5)
                upPos(self, self.bnb, self.ws, set=1)
