#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *
from moneyV2.money import changePos


def sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return str(base64.b64encode(d), 'utf8')

def pre_hash(timestamp, method, request_path):
    return str(timestamp) + str.upper(method) + str(request_path)


class bitget_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        self.passwd = gate.passwd
        self.depathCallback = depathCallback
        
        self.URL = 'wss://ws.bitget.com/mix/v1/stream'

        all = self.gate.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新订单"""
    def _update_order(self, data):
        # pprint(data)
        try:
            for data2 in data:
                id = data2['ordId']
                side = data2['side'].upper()
                close = '减仓' if (data2['posSide'] == 'long' and side == 'SELL') or (data2['posSide'] == 'short' and side == 'BUY') else '开仓'
                status = data2['status']
                if 'avgPx' not in data2:
                    data2['pnl'] = 0
                    data2['avgPx'] = 0
                    data2['accFillSz'] = 0
                    data2['fillNotionalUsd'] = 0.01
                    
                if 'px' not in data2:
                    data2['px'] = 0

                # 如果是部分成交，会推送cancelled，但是成交量是0，手动补充
                if status == 'cancelled' and id in self.orders:
                    data2['avgPx'] = self.orders[id]['jiage']
                    data2['accFillSz'] = self.orders[id]['liang']
                    data2['pnl'] = self.orders[id]['yingkui']
                    data2['fillNotionalUsd'] = self.orders[id]['jiazhi']

                if status == 'partial-fill' and float(data2['sz']) == float(data2['accFillSz']):
                    status = 'full-fill'

                elif status == 'partial-fill' and id in self.orders and float(data2['accFillSz']) < self.orders[id]['liang']:
                    log('订单成交数据比上一条小', str(data2), Color('', -1))
                    continue

                self.orders[id] = {
                    'id': id,                  #交易对
                    'symbol': data2['instId'],                  #交易对
                    'status': status,                  #状态
                    'side': side,                               #方向
                    'type': data2['force'],                     #订单类型
                    'jiage': float(data2['avgPx']),             #累积成交均价
                    'liang': float(data2['accFillSz']),         #累积成交量
                    'yingkui': float(data2['pnl']),             #盈亏
                    'jiazhi': float(data2['fillNotionalUsd']),  #价值
                    'sxf': float(data2['orderFee'][0]['fee']) * -1,  #手续费
                    'time': NowTime(),                #推送时间
                }

                # pprint(data)
                # pprint(self.orders[id])
                # print("------------------")

                msg = ['[Bitget监听订单]', '', close, side, data2['instId'], data2['ordType'], data2['force'],
                "　ID", id, data2['status'], '杠杆', data2['lever'],
                '　价格', data2['px'], '数量', data2['sz'] + ' ('+N_STR(data2['notionalUsd'], 2)+'$)',
                '　成交价', data2['avgPx'], '成交量', str(data2['accFillSz']) + ' ('+N_STR(data2['fillNotionalUsd'], 2)+'$)'
                '　盈亏', data2['pnl'],
                '　费率', StrBi(float(data2['orderFee'][0]['fee']) / float(data2['fillNotionalUsd']), 4)]

                if data2['accFillSz']:
                    log(*msg)

                    """ 这里不能用status变量，否则会重复成交两次"""
                    # if data2['status'] in self.order_status:
                    #     changePos(self, data2['instId'], side, data2['avgPx'], data2['accFillSz'], close=='减仓')

                else:
                    msg = [GetTime(echoMs=1)]+msg
                    print(*msg)


            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())

    """ 更新持仓"""
    def _update_pos(self, data):
        # pprint(data)
        
        okdata = []
        for v in data:
            ok = {}
            ok['symbol'] = v['instId']
            ok['liang'] = float(v['total'])
            if not ok['liang']:
                continue

            ok['side'] = 'BUY' if v['holdSide'] == 'long' else 'SELL'
            ok['side2'] = 'SELL' if v['holdSide'] == 'long' else 'BUY'
            ok['jiage'] = float(v['averageOpenPrice'])
            ok['nowJiage'] = float(v['markPrice'])    #最新成交价格
            ok['yingkui'] = N(float(v['upl']), 4)
            ok['bzj'] = N(float(v['margin']), 4)
            # ok['create_time'] = int(v['cTime']) / 1000
            ok['roe'] = 0
            ok['time'] = NowTime_ms()

            okdata.append(ok)

        self.pos = okdata
        


    """ 更新余额"""
    def _update_account(self, data):

        for i in data:
            if i['marginCoin'].upper() == CcyBi:
                self.usdt = N(float(i['equity']), 4)
                self.keyong = N(float(i['maxOpenPosAvailable']), 4)
                self.yingkui = N(float(i['equity']) - float(i['available']), 4)

                self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.usdt != self.preUsdt:
                    print('[Bitget余额更新]', self.usdt, '可用', self.keyong, '盈亏', self.yingkui)

                self.preUsdt = self.usdt
        


    """ 更新盘口和费率信息"""
    def _update_data(self, data):
        for v in data:
            if 'bestBid' in v:
                data = {
                    'bidPx': float(v['bestBid']),
                    'askPx': float(v['bestAsk']),
                    'bidQty': float(v['bidSz']),
                    'askQty': float(v['askSz']),
                    'feilv': round(float(v['capitalRate'])*100, 4),
                    't': NowTime_ms()
                }
                
                if self.depathCallback:
                    self.depathCallback(v['symbolId'], data)

                self.data[v['symbolId']] = data
                
            else:
                uploadError('Bitget盘口推送错误:'+ str(data))
                #强制重启
                os._exit(0)


    async def ping(self, conn):
        while True:
            await conn.send_str('ping')
            await asyncio.sleep(5)


    async def main(self, symbols=[]):
        while True:
            # log("Bitget 准备连接ws", symbols)
            try:

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            self.URL,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:

                    channels = []

                    if symbols:
                        for symbol in symbols:
                            channels += [
                                {
                                    "instType": "MC",
                                    "channel": "ticker",
                                    "instId": symbol.replace('_UMCBL', '')
                                }
                            ]

                    else:
                        # 先登录
                        timestamp = str(int(time.time()))
                        sign_str = sign(pre_hash(timestamp, "GET", '/user/verify'), self.secret_key)
                        data = {
                            "op":"login",
                                "args":[
                                    {
                                        "apiKey":self.access_id,
                                        "passphrase":self.passwd,
                                        "timestamp":timestamp,
                                        "sign":sign_str
                                    }
                                ]
                        }
                        await conn.send_str(ujson.dumps(data))
                        res = await conn.receive(timeout=30)
                        log('Bitget 登录ws', res.data)

                        
                        # 先登录
                        channels += [
                            {
                                "instType": "UMCBL",
                                "channel": "account",
                                "instId": "default"
                            },
                            {
                                "instType": "UMCBL",
                                "channel": "positions",
                                "instId": "default"
                            },
                            {
                                "channel": "orders",
                                "instType": "UMCBL",
                                "instId": "default"
                            }
                        ]


                    for i in channels:
                        sub_str = ujson.dumps({"args": [i], "op":"subscribe"})
                        await conn.send_str(sub_str)

                    asyncio.create_task(self.ping(conn))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('Bitget ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Bitget ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        # if not symbols:
                        #     print(msg)
                        if msg == 'pong':
                            continue

                        try:
                            msg = ujson.loads(msg)
                        except:
                            print('解析失败: '+str(msg))
                            break

                        if 'data' in msg:
                            
                            if 'ticker' in msg['arg']['channel']:self._update_data(msg['data'])
                            elif 'account' in msg['arg']['channel']:self._update_account(msg['data'])
                            elif 'positions' in msg['arg']['channel']:self._update_pos(msg['data'])
                            elif 'orders' in msg['arg']['channel']:self._update_order(msg['data'])
                            else: pprint(msg)
    

            except Exception as e:
                log(traceback.format_exc())
                # uploadError(traceback.format_exc())
                log(f'bitget ws连接失败 开始重连...')
                uploadLog(isExit=1)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")