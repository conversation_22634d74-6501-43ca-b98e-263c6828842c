#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint

import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
from hashlib import sha256


from main.config import *
from main.hanshu import *

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]

    
class bitget_spot_gg():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        keys = {
            '提现': ['bg_03ce078056617f563576aefc8772fcbd', 'b93fffdb2b01be9b6cea01560f39e5e97bc7a5d5ee18eeea82881e9e4f669ec0', 'b87d055f'],
            '提现小号': ['bg_45e63ca50d9e4d3e1acd50fe3d153d2c', '3a256208d78a2593044c902ffcccb4bf1279da97d0825a2a8f75a15d61699007', 'b87d055f'],
            '吃了吗': ['bg_2bfeb5def261e897b198943307a7845f', '6f40d783977c6c53ed34c1b25c2112ab6588a61eab4b8007700b9a4a51ace314', 'b87d055f'],
            'bitget2': ['bg_e5fe7411e89457582fa838a12611315c', '3f09318d1a09cab12231a254f9cd07d1c420fc0c6171673b22b3ad74a10f1e8a', 'b87d055f'],
            'bitget3': ['bg_1ce48bd3615ca5e5ccf2361ae3365844', 'c012d4f59ba2eafc51b8120980eb2a39ad96f0e69f6030970777a46bf6e8f382', 'b87d055f'],
            'bitget4': ['bg_1abde7c17981e84ebca1230e0c3e61fd', '7f42e942611803e00336833c7d5d99bdf9949c1a5e19aa49260aa91239264301', 'b87d055f'],
            'spot': ['bg_d927c4754a3cf2c5c237d818cafbb13f', '14823d18eba320e34a48fba45e1b82f1bc9fd2de29e702d620cf7c24f5fdfbb2', 'b87d055f'],
            'bitget13': ['bg_e549e47b3019f767228013d286790cbd', 'dff067a724bd4d02b725c87dbad9f94fa36c7802908c2a4f61edd372c35d4d1f', 'b87d055f'],
            'bitget14': ['bg_b4d545116b33a681ed40f01ca83612eb', 'f7b76f264a50825255644601a4865e9370d93d0d97d85114fc328e5f2a021092', 'b87d055f'],
            'bitget15-spot-球球': ['bg_4fb6810cab1a24b64897ad307175dc09', 'b61705998cacba5f44ef19a9dfbe9f96b075ba9efadb1a3111f6849045ed3015', 'b87d055f'],
            'bitget小号1': ['bg_0324fe2db462fa85bb53f9f617c97b01', '0e8b7d529d4e92acc53a03fa6c50afe3d22ea1b76a921ee8c034ddb7e2de8da2', 'b87d055f'],

            'TEST': ['bg_0b31866aeaa4d8a60f607f5731c3492b', 'eaaf065f18884a4456bc2d79b403779e9427737b8101adbb33ef864f56b455d3', 'b87d055f'],

            '返佣':  ['bg_f2ae3dfa05e8ab0b0d5f9b7d122b8137', '539adbccee27db63deb1714c53b41832e2d0d5b92cc92e45efa9348e55dd604e', 'Q39manc3'],
            
            '提现下级': ['bg_8ddf9818a22986ce07d62fe4cb686eae', '0cc25b110f1e28a3e9f3b4c807e5a9600e04a32df4d429354bc5e5e9e3a82d26', 'kvibvxaa'],
            '下级1': ['bg_b5a8aa15b410d870a2a0c12a50dd1d7f', '63b686adafd7cac126aec4497ba6eb36279a998c55adc1206de979b795193695', 'kvibvxaa'],
            '下级2': ['bg_e981946a5a89a248b0e995b9fd04f9b0', '5c981275637797ea8ed07f9b2127065373dd8893d299aae8a6c94ac303e58ef4', 'kvibvxaa'],
            '下级3': ['bg_0d1a33c59c9ed8da305aff86b46ef3f8', 'fd43370cd7e6c0bfad45e359e06c8c5c5d4c3a6ac87d2c2b1a9f0bffd776c654', 'kvibvxaa'],
            '下级4': ['bg_1b8b26e999fe4a9dd6558bcb52637e9c', 'ece60441a339f5ab327e60f8b1b6019fb4642cb31f5b706c8dc7f446feffb170', 'kvibvxaa'],
            '下级5': ['bg_67533be66e7be76176ea3c44477e3170', 'fa56ced762334f83638eddb74ea20f937c35eb5da7aeda1773dd3449b875e5a5', 'b87d055f'],
            '下级6': ['bg_77c611cb3aa639891b3726c4cc12452e', 'e5d326f6dc0e0900817937cab40d413d7198875855efea37b26eebd85eef70cd', 'kvibvxaa'],
            '下级7': ['bg_c08cbfdebe1ec25b1a366b7050fe9d33', 'a4569ba3fbe873d64b15fe884c98c603fa41d768fd0dc73f63410a8af2afe412', 'b87d055f'],

            '球球返佣': ['bg_23ec0b75aa7617de5d64b74a2bacf80e', 'c1cd6da03702d1af084a685ad54c5fb7d25b474072c81fb424aaf6e7114d4ccd', 'Q39manc3'],
            '球球母': ['bg_722b16901b8cafbbd7949602b7903dd5', '038c50a7fb1620b5e92bfc8f5e09856fb22222ceb2245973d4f7815925385969', 'Password123'],
            '球球1': ['bg_219911f44a263d39162710313c2b7634', '71d5324100ff810487cfaf7ad6840fe1c6cc57aa2be9eaeaebf55fba2ff5b48a', 'Password123'],
            '球球2': ['bg_90c9ff5f6fc1d4dcfd3fde217c8598cf', '0efca7a801d31f30478b0efd4389ab445fa9d25060334c2dee75a4196253430b', 'Password123'],

            'printMoney': ['bg_fafda1a0b96cb2cbbf12042451d9540d', '37b511c77ced5a5645a589a8b26c26ac8ee9550fd54ca76811975ccd7dde70c1', 'asdascad22'],
        }

        self.debug = 0
        self.debugs = []

        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]
        self.passwd = keys[apiKey][2]
        self.tickerData = []

        self.HOST = 'https://api.bitget.com'

        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log(f'{NAME} 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  #指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')


    def get_sign(self, timestamp, method, request_path, params, secret_key):
        if params == None:
            params_str = ""
        else:
            if method == 'POST':
                params_str = ujson.dumps(params)
            else:
                params_str = parse_params_to_str(params)

        message = str(timestamp) + str.upper(method) + request_path + params_str
        mac = hmac.new(bytes(secret_key, encoding='utf-8'), bytes(message, encoding='utf-8'), digestmod='sha256').digest()
        return str(base64.b64encode(mac), 'utf-8')


    # 请求API
    def go(self, method, path, payload=None, needKey=1, headers=0, url=''):
        
        url = self.HOST + path if not url else url


        if not headers:
            if needKey:
                
                timestamp = str(int(time.time()*1000))
                headers = {
                    'ACCESS-SIGN': self.get_sign(timestamp, method, path, payload, self.secret_key),
                    'ACCESS-KEY': self.access_id,
                    'ACCESS-PASSPHRASE': self.passwd,
                    'Content-Type': 'application/json',
                    "ACCESS-TIMESTAMP": timestamp,
                    # 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36',
                }
            else:
                headers = {
                    'Content-Type': 'application/json',
                }


        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'data': ujson.dumps(payload), 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}

        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            if not url:
                time.sleep(1)
                return self.go(method, path, payload, needKey)
            else:
                return False

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2


    """ 获取最佳挂单"""
    def GetTickerAll(self):
        data = self.go("GET","/api/spot/v1/market/tickers", {}, needKey=0)
        # print(data)

        if not data or 'data' not in data:
            print('BitGet 获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        return data['data']


    """ 还款"""
    def HuanKuan(self, liang, symbol='', coin='USDT'):
        symbol = symbol if symbol else SYMBOL
        data = {
            'repayAmount': liang,
            'symbol': symbol,
            'coin': coin,
        }
        fh = self.go("POST", '/api/margin/v1/isolated/account/repay', data)['data']
        log('Bitget现货杠杆还款', data, '返回', fh)
        return fh


    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/api/spot/v1/public/products', {'productType': 'umcbl'}, needKey=0)['data']

        
    """ 获取所有持仓"""
    def GetPos(self, all=0, symbol=''):
        symbol = symbol if symbol else SYMBOL
        
        data = self.go('GET', '/api/margin/v1/isolated/account/assets', {'symbol': symbol})['data']
        try:
            if type(data) == list and 'available' in data[0]:
                okdata = []
                for v in data:
                    if v['coin'] == CcyBi:
                        if all:
                            v['symbol'] = v['coin']
                            okdata.append(v)
                        continue

                    ok = {}
                    ok['symbol'] = v['coin']+'USDT_SPBL' if v['coin'] != CcyBi else CcyBi
                    
                    side = 'SELL' if float(v['available']) < 0 else 'BUY'
                    liang = abs(float(v['available']))
                    ok['liang'] = liang
                    if not ok['liang']:
                        continue

                    ok['side'] = side
                    ok['side2'] = fanSide(side)
                    ok['jiage'] = 0
                    ok['nowJiage'] = 0
                    ok['yingkui'] = 0
                    ok['bzj'] = 0
                    ok['time'] = NowTime_ms()

                    okdata.append(ok)

                return okdata

            else:
                log(Color("BitGet现货 获取持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos(all)

        except Exception as e:
            uploadError(traceback.format_exc())
        
        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)

    """获取可用余额"""
    def GetYuer(self, p=1):
        all = self.GetPos(all=1)
        if not self.tickerData or tlog('BitgetTickers', '', 30, xs=0):
            self.tickerData = self.go('GET', "/api/spot/v1/market/tickers")['data']

        data = 0
        jiazhi = 0
        for pos in all:
            if pos['symbol'] == CcyBi:
                data = pos

            else:
                for v in self.tickerData:
                    if pos['symbol'].replace('_SPBL', '') == v['symbol']:
                        jiazhi += pos['liang'] * float(v['buyOne'])
                        # if pos['liang'] * float(v['buyOne']) > 10:
                        #     print(pos['symbol'], U(pos['liang'] * float(v['buyOne'])))
                        break
                else:
                    log(pos, 'Bitget Spot未找到价格', Color('', -1))


        kejie = self.go('POST', "/api/margin/v1/isolated/account/maxBorrowableAmount", {'symbol': SYMBOL, 'coin': CcyBi})
        keyong = float(kejie['data']['maxBorrowableAmount']) * 0.98
        
        ok = {}
        ok['all'] = Si(float(data['net']) + jiazhi, 4)
        ok['keyong'] = Si(float(data['available'])+keyong, 4)
        ok['jiekuan'] = Si(data['borrow'], 8)
        ok['yingkui'] = 0
        
        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(f'Bitget现货 {SYMBOL}杠杆 总余额', ok['all'], '可用', N(ok['keyong'], 4), '借款', N(ok['jiekuan'], 4), '可借', N(ok['keyong'], 4))

        return ok

    


    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        
        headers = {
            'authority': 'www.bitget.com',
            'sec-ch-ua': '"Chromium";v="21", " Not;A Brand";v="99"',
            'language': 'zh_CN',
            'locale': 'zh_CN',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36',
            'content-type': 'application/json;charset=UTF-8',
            'website': 'mix',
            'accept': 'application/json, text/plain, */*',
            'sec-ch-ua-mobile': '?0',
            'terminaltype': '1',
            'sec-ch-ua-platform': '"Windows"',
            'origin': 'https://www.bitget.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': f'https://www.bitget.com/zh-CN/spot/{SYMBOL}_SPBL?type=isolated',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cookie': '_ga_clientid=567963730.1685243154; _ga_sessionid=1685243154; _dx_kvani5r=cba1ef07d64385aa0636b89ca1667c9843f9195463e55d9fd96368602ebed3b29f4d367d; _gid=GA1.2.1700143391.1685243156; _fbp=fb.1.1685243157701.1317912225; _tt_enable_cookie=1; _ttp=AA_fjoutYcCayoULkHg8vctryAJ; afUserId=e6e150aa-a4de-4585-bba1-e110c014052d-p; AF_SYNC=1685243160243; __zlcmid=1G5lqxpoaZma46B; BITGET_LOCAL_COOKIE={%22bitget_lang%22:%22zh-CN%22%2C%22bitget_showasset%22:true%2C%22bitget_theme%22:%22black%22%2C%22bitget_layout%22:%22right%22%2C%22bitget_valuationunit%22:1%2C%22bitgt_login%22:false}; gt_captcha_v4_user=3b914f7a1fba461a92a1cd2734ea81f8; bt_rtoken=upex:session:id:95a53b390cc5e7b1ad8205ddb3061c458b21268d4815c455232222b8c8e542d5; bt_sessonid=3f8157ef-3b02-4d4d-a063-70adb0174ceb; bt_newsessionid=eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIwMjg0YTE4Ny05Njg1LTQwODgtOGM2Yi00MDk1MTQ5Y2RjN2E5NjI0NzE5NjYiLCJ1aWQiOiJDRW5JOGdXVmhhQVRxQ3JFSUN6OGdBPT0iLCJzdWIiOiJtdWIqKiouY29tIiwiaXAiOiJuYitwNjdvYzBreVZoMTIyMVg2Y2ZnPT0iLCJkaWQiOiJSTENHRDUwT0xOdTNqTjc0QTNrYkZKVUxVSUYzTHU2UzlaSVk3QnEvUFl4MTJSSXNaUUtNNUhCa1M0eVRSaG5XIiwic3RzIjowLCJpYXQiOjE2ODUyNDMyNTcsImV4cCI6MTY5MzAxOTI1NywicHVzaGlkIjoiZGRrU0xHVUNqT1J3WkV1TWswWVoxZz09IiwiaXNzIjoidXBleCJ9.4U0mm5JuiGUIe-Q7x2V-deYyga8gvd-SsQ4rBuc5bYs; _cfuvid=vDFuZJ6e9CDaxfTVLtyA7M1CR1_3FeRDfIomvZ9JRU8-1685245084088-0-604800000; dy_token=6472cfc3KL47zdO4eaZihFFVoQnFr3xSN4Vczpo1; _ga=GA1.2.567963730.1685243154; _ga_Z8Q93KHR0F=GS1.1.1685243154.1.1.1685246180.56.0.0; _ga_B8RNGYK5MS=GS1.1.1685243155.1.1.1685246180.0.0.0; __cf_bm=TP2rcRg7uCtmlxlsLVMJj.ErjwAKOI7Wz0w_yMXk3XM-1685254247-0-AV3UgM+dH18zWybo1fFOZcenISf0wL7Hw56EaKaE6Ft+qjSDpj1Vz+6iKlPMV9aUjhlgPXPU1l8e0olV58pLu1Y=',
        }


        jiazhi = float(liang)*float(jiage)
        if jiazhi < 5:
            return 0, '价值小于5u'
        

        msg = 'Bitget现货　'+symbol+'　'

        if side == 'BUY':
            msg += '开多仓'
            post = {
                'symbolId': symbol,
                'delegateType': '21',
                'orderType': 1,
                'loanAction': 1,
                'delegateAmount': STR_N(jiazhi, 2),
                'languageType': 1,
            }

        else:
            msg += '平多仓'
            post = {
                'symbolId': symbol,
                'delegateType': '22',
                'orderType': 1,
                'loanAction': 2,
                'delegateCount': str(liang),
                'languageType': 1,
            }

        jiage = str(jiage)
        liang = str(liang)
        liang2 = liang+' ('+STR_N(jiazhi)+'$)'
        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　Type:"+type

        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST', '', post, headers=headers, url='https://www.bitget.com/v1/margin/isolated/order/placeOrder')

            if not order or 'data' not in order or not order['data'] or 'orderId' not in order['data']:
                log(symbol+" [!!!] 套利下单失败！！", post, order)
                uploadError(str(order))

            else:
                orderId = order['data']['orderId']
                break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg
    
    
    """ 获取Userid"""
    def GetUserId(self):
        data = self.go("GET", "/api/spot/v1/account/getInfo")
        return data['data']['user_id']