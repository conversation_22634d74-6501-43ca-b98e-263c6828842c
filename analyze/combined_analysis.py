#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合分析脚本：按照filter_rule.txt规则进行筛选
1. 现货交易量小于400万USDT
2. 期货交易量是现货交易量的600%以上
3. tick size占比大于0.001
4. 与BTC的相关性小于0.4
5. 期货的十档挂单量是现货的2倍以上
6. 查询多个交易所的可开数量
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from decimal import Decimal
import warnings
warnings.filterwarnings('ignore')


class CombinedAnalyzer:
    def __init__(self):
        self.spot_base_url = "https://api.binance.com"
        self.futures_base_url = "https://fapi.binance.com"
        self.session = requests.Session()

        # 其他交易所API端点
        self.other_exchanges = {
            'OKX': 'https://www.okx.com',
            'Bybit': 'https://api.bybit.com',
            'Bitget': 'https://api.bitget.com',
            'BingX': 'https://open-api.bingx.com',
            'Huobi': 'https://api.huobi.pro'
        }

    def get_spot_exchange_info(self):
        """获取现货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_exchange_info(self):
        """获取期货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_spot_24hr_ticker(self):
        """获取现货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_24hr_ticker(self):
        """获取期货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_spot_depth(self, symbol, limit=10):
        """获取现货深度数据"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/depth"
                params = {'symbol': symbol, 'limit': limit}
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} 现货深度失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_futures_depth(self, symbol, limit=10):
        """获取期货深度数据"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/depth"
                params = {'symbol': symbol, 'limit': limit}
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} 期货深度失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_okx_depth(self, symbol, limit=10):
        """获取OKX深度数据"""
        try:
            # 转换symbol格式：BTCUSDT -> BTC-USDT
            okx_symbol = f"{symbol[:-4]}-{symbol[-4:]}"
            url = f"{self.other_exchanges['OKX']}/api/v5/market/books"
            params = {'instId': okx_symbol, 'sz': limit}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('code') == '0' and data.get('data'):
                return data['data'][0]
        except Exception as e:
            print(f"  OKX深度获取失败: {e}")
        return None

    def get_bybit_depth(self, symbol, limit=10):
        """获取Bybit深度数据"""
        try:
            url = f"{self.other_exchanges['Bybit']}/v5/market/orderbook"
            params = {'category': 'linear', 'symbol': symbol, 'limit': limit}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('retCode') == 0 and data.get('result'):
                return data['result']
        except Exception as e:
            print(f"  Bybit深度获取失败: {e}")
        return None

    def get_bitget_depth(self, symbol, limit=10):
        """获取Bitget深度数据"""
        try:
            # Bitget的永续合约格式
            bitget_symbol = f"{symbol}_UMCBL"
            url = f"{self.other_exchanges['Bitget']}/api/mix/v1/market/depth"
            params = {'symbol': bitget_symbol, 'limit': limit}
            response = self.session.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '00000' and data.get('data'):
                    return data['data']
        except Exception:
            pass  # 静默处理Bitget错误
        return None

    def get_bingx_depth(self, symbol, limit=10):
        """获取BingX深度数据"""
        try:
            url = f"{self.other_exchanges['BingX']}/openApi/swap/v2/quote/depth"
            params = {'symbol': symbol, 'limit': limit}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('code') == 0 and data.get('data'):
                return data['data']
        except Exception as e:
            print(f"  BingX深度获取失败: {e}")
        return None

    def get_huobi_depth(self, symbol, limit=10):
        """获取Huobi深度数据"""
        try:
            # 转换symbol格式：BTCUSDT -> btcusdt
            huobi_symbol = symbol.lower()
            url = f"{self.other_exchanges['Huobi']}/market/depth"
            params = {'symbol': huobi_symbol, 'depth': limit, 'type': 'step0'}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') == 'ok' and data.get('tick'):
                return data['tick']
        except Exception as e:
            print(f"  Huobi深度获取失败: {e}")
        return None

    def get_kline_data(self, symbol, interval='1m', limit=1440):
        """获取K线数据 (1440分钟 = 24小时)"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/klines"
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': limit
                }
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} K线数据失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def parse_tick_size(self, filters):
        """解析价格精度(tick size)"""
        for filter_item in filters:
            if filter_item['filterType'] == 'PRICE_FILTER':
                return float(filter_item['tickSize'])
        return None

    def calculate_tick_ratio(self, tick_size, last_price):
        """计算tick size / last price比例"""
        if last_price == 0:
            return 0
        return tick_size / last_price

    def format_volume(self, volume):
        """格式化交易量为M/B单位"""
        if volume >= 1_000_000_000:
            return f"{volume / 1_000_000_000:.3f}B"
        elif volume >= 1_000_000:
            return f"{volume / 1_000_000:.3f}M"
        else:
            return f"{volume:.0f}"

    def calculate_depth_quantity(self, depth_data, tick_size, price_range_pct=0.003):
        """
        计算深度数据中指定价格范围内的挂单量
        price_range_pct: 价格范围百分比，默认0.3%（千三滑点）
        """
        if not depth_data or 'bids' not in depth_data or 'asks' not in depth_data:
            return 0, 0

        bids = depth_data['bids']
        asks = depth_data['asks']

        if not bids or not asks:
            return 0, 0

        # 获取最佳买卖价
        best_bid = float(bids[0][0])
        best_ask = float(asks[0][0])
        mid_price = (best_bid + best_ask) / 2

        # 计算价格范围
        price_range = mid_price * price_range_pct
        bid_threshold = mid_price - price_range
        ask_threshold = mid_price + price_range

        # 计算买单量（USDT金额）
        bid_quantity_usdt = 0
        for price_str, qty_str in bids:
            price = float(price_str)
            qty = float(qty_str)
            if price >= bid_threshold:
                bid_quantity_usdt += price * qty  # 转换为USDT金额
            else:
                break

        # 计算卖单量（USDT金额）
        ask_quantity_usdt = 0
        for price_str, qty_str in asks:
            price = float(price_str)
            qty = float(qty_str)
            if price <= ask_threshold:
                ask_quantity_usdt += price * qty  # 转换为USDT金额
            else:
                break

        return bid_quantity_usdt, ask_quantity_usdt

    def normalize_tick_quantity(self, quantity_usdt, from_tick, to_tick, price):
        """
        将挂单量USDT金额从一个tick size标准化到另一个tick size
        由于我们现在使用USDT金额，tick size差异的影响已经在价格中体现，
        所以不需要额外调整
        """
        # USDT金额不需要tick size调整
        return quantity_usdt

    def get_all_exchanges_depth(self, symbol, current_price):
        """
        获取所有交易所千三滑点内的可开数量(USDT)
        current_price: 当前价格，用于计算千三滑点范围
        """
        results = {}

        # 获取各交易所深度数据
        exchanges_data = {
            'OKX': self.get_okx_depth(symbol),
            'Bybit': self.get_bybit_depth(symbol),
            'Bitget': self.get_bitget_depth(symbol),
            'BingX': self.get_bingx_depth(symbol),
            'Huobi': self.get_huobi_depth(symbol)
        }

        # 计算千三滑点范围（0.3%）
        price_range = current_price * 0.003
        min_price = current_price - price_range
        max_price = current_price + price_range

        for exchange, depth_data in exchanges_data.items():
            if depth_data:
                try:
                    available_qty_usdt = self.calculate_available_quantity_in_range(
                        depth_data, min_price, max_price, exchange
                    )
                    results[exchange] = available_qty_usdt
                except Exception as e:
                    print(f"  {exchange}数据处理失败: {e}")
                    results[exchange] = 0
            else:
                results[exchange] = 0

        return results

    def calculate_available_quantity_in_range(self, depth_data, min_price, max_price, exchange):
        """计算指定价格范围内的可开数量(USDT)"""
        total_quantity_usdt = 0

        # 根据不同交易所的数据格式处理
        if exchange == 'OKX':
            bids = depth_data.get('bids', [])
            asks = depth_data.get('asks', [])
        elif exchange == 'Bybit':
            bids = depth_data.get('b', [])
            asks = depth_data.get('a', [])
        elif exchange == 'Bitget':
            bids = depth_data.get('bids', [])
            asks = depth_data.get('asks', [])
        elif exchange == 'BingX':
            bids = depth_data.get('bids', [])
            asks = depth_data.get('asks', [])
        elif exchange == 'Huobi':
            bids = depth_data.get('bids', [])
            asks = depth_data.get('asks', [])
        else:
            return 0

        # 计算买单量（在价格范围内，转换为USDT）
        for price_qty in bids:
            if len(price_qty) >= 2:
                price = float(price_qty[0])
                qty = float(price_qty[1])
                if min_price <= price <= max_price:
                    total_quantity_usdt += price * qty

        # 计算卖单量（在价格范围内，转换为USDT）
        for price_qty in asks:
            if len(price_qty) >= 2:
                price = float(price_qty[0])
                qty = float(price_qty[1])
                if min_price <= price <= max_price:
                    total_quantity_usdt += price * qty

        return total_quantity_usdt

    def process_kline_data(self, kline_data):
        """处理K线数据，提取收盘价"""
        if not kline_data:
            return None

        prices = []
        timestamps = []

        for kline in kline_data:
            timestamp = kline[0]  # 开盘时间
            close_price = float(kline[4])  # 收盘价

            prices.append(close_price)
            timestamps.append(timestamp)

        return pd.Series(prices, index=timestamps)

    def calculate_correlation(self, price_series1, price_series2):
        """
        计算两个价格序列的相关性

        注意：我们计算的是价格变化率（收益率）的相关性，而不是价格绝对值的相关性
        这种方法是正确的，因为：
        1. 价格变化率已经是标准化的：(price_t - price_t-1) / price_t-1
        2. 相关性本身就是标准化的度量，不受价格绝对值影响
        3. 这是金融学中分析资产相关性的标准做法
        """
        if price_series1 is None or price_series2 is None:
            return None

        if len(price_series1) == 0 or len(price_series2) == 0:
            return None

        # 确保两个序列长度相同
        min_length = min(len(price_series1), len(price_series2))
        if min_length < 100:  # 至少需要100个数据点
            return None

        series1 = price_series1.iloc[-min_length:]
        series2 = price_series2.iloc[-min_length:]

        # 计算价格变化率（收益率）
        # 这里自动进行了标准化：无论BTC价格是110,000还是其他币种价格是0.1
        # 变化率都是无量纲的比率
        returns1 = series1.pct_change().dropna()
        returns2 = series2.pct_change().dropna()

        if len(returns1) < 50 or len(returns2) < 50:
            return None

        # 过滤异常值（变化率超过50%的数据点）
        returns1 = returns1[(returns1.abs() <= 0.5)]
        returns2 = returns2[(returns2.abs() <= 0.5)]

        if len(returns1) < 50 or len(returns2) < 50:
            return None

        # 确保两个序列对应相同的时间点
        common_index = returns1.index.intersection(returns2.index)
        if len(common_index) < 50:
            return None

        returns1_aligned = returns1.loc[common_index]
        returns2_aligned = returns2.loc[common_index]

        # 计算皮尔逊相关系数
        correlation = returns1_aligned.corr(returns2_aligned)

        return correlation if not pd.isna(correlation) else None

    def get_correlation_description(self, correlation):
        """获取相关性描述"""
        if correlation is None:
            return "无数据"

        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "强相关"
        elif abs_corr >= 0.6:
            return "中等相关"
        elif abs_corr >= 0.3:
            return "弱相关"
        else:
            return "不相关"

    def analyze_combined(self):
        """综合分析"""
        print("=== 第一步：获取现货和期货基础数据 ===")

        # 获取基础数据
        spot_info = self.get_spot_exchange_info()
        futures_info = self.get_futures_exchange_info()
        spot_24hr = self.get_spot_24hr_ticker()
        futures_24hr = self.get_futures_24hr_ticker()

        if not all([spot_info, futures_info, spot_24hr, futures_24hr]):
            print("获取基础数据失败")
            return []

        print("基础数据获取完成")

        # 构建数据字典
        spot_symbols = {}
        futures_symbols = {}
        spot_24hr_data = {}
        futures_24hr_data = {}

        # 处理现货数据
        for symbol_info in spot_info['symbols']:
            if symbol_info['status'] == 'TRADING':
                symbol = symbol_info['symbol']
                tick_size = self.parse_tick_size(symbol_info['filters'])
                if tick_size:
                    spot_symbols[symbol] = {
                        'tick_size': tick_size,
                        'base_asset': symbol_info['baseAsset'],
                        'quote_asset': symbol_info['quoteAsset']
                    }

        # 处理期货数据
        for symbol_info in futures_info['symbols']:
            if symbol_info['status'] == 'TRADING' and symbol_info['contractType'] == 'PERPETUAL':
                symbol = symbol_info['symbol']
                tick_size = self.parse_tick_size(symbol_info['filters'])
                if tick_size:
                    futures_symbols[symbol] = {
                        'tick_size': tick_size,
                        'base_asset': symbol_info['baseAsset'],
                        'quote_asset': symbol_info['quoteAsset']
                    }

        # 处理24小时数据
        for ticker in spot_24hr:
            spot_24hr_data[ticker['symbol']] = {
                'last_price': float(ticker['lastPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume'])
            }

        for ticker in futures_24hr:
            futures_24hr_data[ticker['symbol']] = {
                'last_price': float(ticker['lastPrice']),
                'volume': float(ticker['volume']),
                'quote_volume': float(ticker['quoteVolume'])
            }

        print(f"现货交易对: {len(spot_symbols)}, 期货交易对: {len(futures_symbols)}")

        # 找出既有现货又有期货的交易对
        common_symbols = set(spot_symbols.keys()) & set(futures_symbols.keys())
        print(f"既有现货又有期货的交易对: {len(common_symbols)}")

        print("\n=== 第二步：应用基础筛选条件 ===")
        print("筛选条件：")
        print("1. 现货24小时交易量 < 5,000,000 USDT")
        print("2. 期货交易量 >= 现货交易量的600%")
        print("3. tick size占比 > 0.001")

        tick_results = []
        filtered_count = 0

        for symbol in common_symbols:
            if symbol in spot_24hr_data and symbol in futures_24hr_data:
                spot_data = spot_symbols[symbol]
                spot_ticker = spot_24hr_data[symbol]
                futures_ticker = futures_24hr_data[symbol]

                # 应用筛选条件
                spot_volume = spot_ticker['quote_volume']
                futures_volume = futures_ticker['quote_volume']

                # 条件1：现货24小时交易量小于5,000,000 USDT
                if spot_volume >= 5_000_000:
                    continue

                # 条件2：期货交易量是现货交易量的600%以上
                if futures_volume < spot_volume * 6:
                    continue

                # 计算现货tick ratio
                spot_tick_ratio = self.calculate_tick_ratio(
                    spot_data['tick_size'],
                    spot_ticker['last_price']
                )

                # 条件3：tick size占比大于0.001
                if spot_tick_ratio <= 0.001:
                    continue

                filtered_count += 1

                # 计算交易量比值
                volume_ratio = futures_volume / spot_volume if spot_volume > 0 else 0

                tick_results.append({
                    'symbol': symbol,
                    'base_asset': spot_data['base_asset'],
                    'spot_tick_size': spot_data['tick_size'],
                    'spot_last_price': spot_ticker['last_price'],
                    'spot_tick_ratio': spot_tick_ratio,
                    'spot_volume_24h': spot_volume,
                    'futures_volume_24h': futures_volume,
                    'volume_ratio': volume_ratio
                })

        print(f"符合筛选条件的币种数量: {filtered_count}")

        # 按现货tick ratio从高到低排序，取前50个（如果不足50个则全部取）
        tick_results.sort(key=lambda x: x['spot_tick_ratio'], reverse=True)
        top_symbols = tick_results[:100] if len(tick_results) > 100 else tick_results

        print(f"按tick size占比排序，选取前{len(top_symbols)}个币种进行相关性分析")

        print("\n=== 第三步：分析这50个币种与BTC的相关性 ===")

        # 获取BTC数据
        print("获取BTC K线数据...")
        btc_kline = self.get_kline_data('BTCUSDT')
        btc_prices = self.process_kline_data(btc_kline)

        if btc_prices is None:
            print("获取BTC数据失败")
            return []

        print(f"BTC数据获取成功，共 {len(btc_prices)} 个数据点")

        # 分析相关性
        final_results = []
        for i, item in enumerate(top_symbols):
            symbol = item['symbol']
            print(f"分析 {symbol} ({i+1}/{len(top_symbols)})")

            # 获取币种K线数据
            kline_data = self.get_kline_data(symbol)
            price_series = self.process_kline_data(kline_data)

            if price_series is None:
                print(f"  跳过 {symbol}: K线数据获取失败")
                continue

            # 计算相关性
            correlation = self.calculate_correlation(btc_prices, price_series)

            if correlation is not None:
                result = item.copy()
                result.update({
                    'correlation': correlation,
                    'abs_correlation': abs(correlation),
                    'correlation_description': self.get_correlation_description(correlation)
                })
                final_results.append(result)
            else:
                print(f"  跳过 {symbol}: 相关性计算失败")

        print(f"\n成功分析了 {len(final_results)} 个币种的相关性")

        print("\n=== 第四步：筛选与BTC低相关的币种 ===")

        # 筛选低相关的币种 (相关性绝对值 < 0.4)
        low_corr_results = [r for r in final_results if r['abs_correlation'] < 0.4]

        print(f"与BTC低相关的币种数量 (|相关性| < 0.4): {len(low_corr_results)}")

        print("\n=== 第五步：分析深度数据和挂单量 ===")

        final_filtered_results = []
        for i, item in enumerate(low_corr_results):
            symbol = item['symbol']
            print(f"分析深度数据 {symbol} ({i+1}/{len(low_corr_results)})")

            # 获取现货和期货深度数据
            spot_depth = self.get_spot_depth(symbol)
            futures_depth = self.get_futures_depth(symbol)

            if spot_depth and futures_depth:
                # 计算挂单量（USDT金额）
                spot_bid_qty_usdt, spot_ask_qty_usdt = self.calculate_depth_quantity(
                    spot_depth, item['spot_tick_size']
                )
                futures_bid_qty_usdt, futures_ask_qty_usdt = self.calculate_depth_quantity(
                    futures_depth, futures_symbols[symbol]['tick_size']
                )

                # 计算期货/现货挂单量比值（USDT金额比值）
                total_spot_qty_usdt = spot_bid_qty_usdt + spot_ask_qty_usdt
                total_futures_qty_usdt = futures_bid_qty_usdt + futures_ask_qty_usdt

                depth_ratio = total_futures_qty_usdt / total_spot_qty_usdt if total_spot_qty_usdt > 0 else 0

                # 条件5：期货的千三滑点挂单量是现货的2倍以上
                if depth_ratio >= 2.0:
                    # 获取其他交易所的千三滑点可开数量
                    current_price = item['spot_last_price']
                    print(f"  获取其他交易所 {symbol} 千三滑点数据...")
                    other_exchanges_qty = self.get_all_exchanges_depth(symbol, current_price)

                    futures_tick_size = futures_symbols[symbol]['tick_size']
                    spot_tick_size = item['spot_tick_size']

                    result = item.copy()
                    result.update({
                        'spot_depth_qty_usdt': total_spot_qty_usdt,
                        'futures_depth_qty_usdt': total_futures_qty_usdt,
                        'depth_ratio': depth_ratio,
                        'futures_tick_size': futures_tick_size,
                        'tick_size_diff': futures_tick_size != spot_tick_size,
                        'current_price': current_price,
                        'okx_qty_usdt': other_exchanges_qty.get('OKX', 0),
                        'bybit_qty_usdt': other_exchanges_qty.get('Bybit', 0),
                        'bitget_qty_usdt': other_exchanges_qty.get('Bitget', 0),
                        'bingx_qty_usdt': other_exchanges_qty.get('BingX', 0),
                        'huobi_qty_usdt': other_exchanges_qty.get('Huobi', 0)
                    })
                    final_filtered_results.append(result)
                else:
                    print(f"  跳过 {symbol}: 期货挂单量比值不足2倍 ({depth_ratio:.2f})")
            else:
                print(f"  跳过 {symbol}: 深度数据获取失败")

        print(f"\n最终符合所有条件的币种数量: {len(final_filtered_results)}")

        return final_results, final_filtered_results

    def display_results(self, all_results, final_results):
        """显示分析结果"""
        print("\n" + "="*180)
        print("综合分析结果：符合基础筛选条件的币种")
        print("基础条件：现货24h交易量<5M USDT 且 期货交易量>=现货600% 且 tick占比>0.001")
        print("="*180)
        print(f"{'排名':<4} {'交易对':<15} {'基础币种':<10} {'Tick占比':<15} {'现货价格':<12} "
              f"{'现货交易量':<15} {'期货交易量':<15} {'交易量比值':<12} {'BTC相关性':<12} {'相关性':<10}")
        print("="*180)

        for i, item in enumerate(all_results, 1):
            spot_volume_str = self.format_volume(item['spot_volume_24h'])
            futures_volume_str = self.format_volume(item['futures_volume_24h'])
            print(f"{i:<4} {item['symbol']:<15} {item['base_asset']:<10} "
                  f"{item['spot_tick_ratio']:<15.8f} {item['spot_last_price']:<12.3f} "
                  f"{spot_volume_str:<15} {futures_volume_str:<15} {item['volume_ratio']:<12.1f} "
                  f"{item['correlation']:<12.4f} {item['correlation_description']:<10}")

        if final_results:
            print("\n" + "="*250)
            print("🎯 最终筛选结果：符合所有filter_rule.txt条件的币种")
            print("按照filter_rule.txt要求的表头格式显示")
            print("="*250)
            print(f"{'交易对':<12} {'Tick占比':<12} {'现货交易量':<12} {'期货交易量':<12} {'交易量比值':<10} "
                  f"{'BTC相关性':<10} {'相关性绝对值':<12} {'现货十档挂单量(USDT)':<20} {'期货十档挂单量(USDT)':<20} "
                  f"{'挂单比值':<10} {'OKX千三(USDT)':<15} {'Bybit千三(USDT)':<16} {'Bitget千三(USDT)':<17} "
                  f"{'BingX千三(USDT)':<16} {'Huobi千三(USDT)':<16}")
            print("="*250)

            for item in final_results:
                spot_volume_str = self.format_volume(item['spot_volume_24h'])
                futures_volume_str = self.format_volume(item['futures_volume_24h'])
                spot_depth_str = self.format_volume(item['spot_depth_qty_usdt'])
                futures_depth_str = self.format_volume(item['futures_depth_qty_usdt'])

                okx_str = self.format_volume(item['okx_qty_usdt']) if item['okx_qty_usdt'] > 0 else "N/A"
                bybit_str = self.format_volume(item['bybit_qty_usdt']) if item['bybit_qty_usdt'] > 0 else "N/A"
                bitget_str = self.format_volume(item['bitget_qty_usdt']) if item['bitget_qty_usdt'] > 0 else "N/A"
                bingx_str = self.format_volume(item['bingx_qty_usdt']) if item['bingx_qty_usdt'] > 0 else "N/A"
                huobi_str = self.format_volume(item['huobi_qty_usdt']) if item['huobi_qty_usdt'] > 0 else "N/A"

                print(f"{item['symbol']:<12} {item['spot_tick_ratio']:<12.6f} {spot_volume_str:<12} "
                      f"{futures_volume_str:<12} {item['volume_ratio']:<10.1f} {item['correlation']:<10.4f} "
                      f"{item['abs_correlation']:<12.4f} {spot_depth_str:<20} {futures_depth_str:<20} "
                      f"{item['depth_ratio']:<10.2f} {okx_str:<15} {bybit_str:<16} {bitget_str:<17} "
                      f"{bingx_str:<16} {huobi_str:<16}")

            print(f"\n✅ 找到 {len(final_results)} 个符合所有filter_rule.txt条件的币种")

            # 显示Tick Size详细信息
            print("\n" + "="*120)
            print("📊 Tick Size详细信息（用于验证期货现货tick size差异）")
            print("="*120)
            print(f"{'交易对':<15} {'现货Tick Size':<20} {'期货Tick Size':<20} {'是否需要换算':<15} {'当前价格':<15}")
            print("="*120)

            for item in final_results:
                need_convert = "是" if item['tick_size_diff'] else "否"
                print(f"{item['symbol']:<15} {item['spot_tick_size']:<20.8f} "
                      f"{item['futures_tick_size']:<20.8f} {need_convert:<15} {item['current_price']:<15.4f}")
        else:
            print("\n❌ 没有找到同时满足所有filter_rule.txt条件的币种")

    def save_to_csv(self, all_results, final_results):
        """保存结果到CSV文件"""
        if all_results:
            df_all = pd.DataFrame(all_results)
            df_all.to_csv("basic_filtered_coins.csv", index=False, encoding='utf-8-sig')
            print(f"\n基础筛选结果已保存到 basic_filtered_coins.csv")

        if final_results:
            df_final = pd.DataFrame(final_results)
            df_final.to_csv("final_filtered_coins.csv", index=False, encoding='utf-8-sig')
            print(f"最终筛选结果已保存到 final_filtered_coins.csv")

            # 按照filter_rule.txt表头格式保存数据
            formatted_data = []
            for item in final_results:
                formatted_data.append({
                    '交易对': item['symbol'],
                    'Tick占比': item['spot_tick_ratio'],
                    '现货交易量': item['spot_volume_24h'],
                    '期货交易量': item['futures_volume_24h'],
                    '交易量比值': item['volume_ratio'],
                    'BTC相关性': item['correlation'],
                    '相关性绝对值': item['abs_correlation'],
                    '现货十档挂单量(USDT)': item['spot_depth_qty_usdt'],
                    '期货十档挂单量(USDT)': item['futures_depth_qty_usdt'],
                    '挂单比值': item['depth_ratio'],
                    'OKX千三滑点可开数量(USDT)': item['okx_qty_usdt'],
                    'Bybit千三滑点可开数量(USDT)': item['bybit_qty_usdt'],
                    'Bitget千三滑点可开数量(USDT)': item['bitget_qty_usdt'],
                    'BingX千三滑点可开数量(USDT)': item['bingx_qty_usdt'],
                    'Huobi千三滑点可开数量(USDT)': item['huobi_qty_usdt']
                })

            if formatted_data:
                df_formatted = pd.DataFrame(formatted_data)
                df_formatted.to_csv("filter_rule_results.csv", index=False, encoding='utf-8-sig')
                print(f"按filter_rule.txt格式的结果已保存到 filter_rule_results.csv")


def main():
    analyzer = CombinedAnalyzer()

    try:
        print("开始执行filter_rule.txt规则筛选...")
        print("规则：")
        print("1. 现货交易量小于500万USDT")
        print("2. 期货交易量是现货交易量的600%以上")
        print("3. tick size占比大于0.001")
        print("4. 与BTC的相关性小于0.4")
        print("5. 期货的千三滑点挂单量是现货的2倍以上（注意tick size换算）")
        print("6. 获取其他交易所千三滑点可开数量(USDT)")
        print("\n表格格式：交易对，Tick占比，现货交易量，期货交易量，交易量比值，BTC相关性，")
        print("相关性绝对值，现货十档挂单量（USDT），期货十档挂单量（USDT），挂单比值，")
        print("各其他交易所千三滑点可开数量(USDT)")

        all_results, final_results = analyzer.analyze_combined()

        if all_results:
            analyzer.display_results(all_results, final_results)
            analyzer.save_to_csv(all_results, final_results)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
