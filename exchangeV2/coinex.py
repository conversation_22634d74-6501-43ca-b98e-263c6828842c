#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import hmac
import hashlib
try:
    import rusthttp_py.adapter as requests
except:
    print('Coinex不支持Rust的Request')
    import requests
    
import time
import json
import traceback        #输出报错
from urllib.parse import parse_qs, urlencode

from main.config import *
from main.hanshu import *

class coinex():

    def __init__(self, apiKey=0, headers={}, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['628B0A63CCA846F786CEA9F486282588','CF6EFA4666F9F69E53FD7F7B560E9C86A5C1AD950F6A1534'],
            'coinex1': ['B125703928454EBA990E287EA0C4C813','23BC5E435EF2E2FFCCC198D86F53D10543E6FAED03A53A15'],
            'coinex2': ['F67AFE12B4724403A7481F9767B7402D','6D1CC69C2E0ACAE5649CDCEC3C8E9F730CCAEF77E0710DBF'],
            'coinex3': ['0B79BE7F614140CD90CDCEEFB6327810','2D02B90AF70DEC7D6DDEC380EE57272C2EFDB303887AEAE3'],
            'coinex4': ['A2F8FD96EEC341B895A553AF59D7CCAE','4C90A067694C753BA914F7FB942D1EA6C12E4BFF8043675C'],
            'coinex5': ['1A56883275E54350AB4CDD62D74E6CFA','7A9D3A29C2EC8517CA44615924685EC7540A0A58DB6632D6'],
            'coinex6': ['E0059E013A2A410D8CC9E12B68394425','1885D583274D3C7D4BE3060870FF109F6F43DFF2CFA56000'],
            'coinex7': ['CF0543B12C88458FA2B3065B16740532','BEC1F4ED5314D8001F6B5F10F59ABB90E5D7769D26DE3FC3'],
            'coinex8': ['6C0D286ABE2944F797948C2BD5405836','867C09AD3EDC87FEBD82AF857D5958D9F571FBE1C81DE1BC'],
            'coinex9': ['01D0CDEE4D3D439C9DE20B6CAC8365F9','33F1A52EAD2ABA15EF7018BE8329CA38630828D6A3536A89'],
            'coinex10': ['2B8E49441EDB4810A1C7FE0C8C21FF49','A135B6C683468F7C61A373AE3E922639989397D02CB8ABB0'],
            'coinex11': ['77CC096EC1E64DA19358B662C668C858','829F3CEF1581CD608BCD76C156D1EEAE4C5BCCF766557CB1'],
            'coinex12': ['338D79FD3BFB4800A60F82C231643D33','429328A99B6782A07497C3391F7F029D0A336312B2738948'],
            'coinex13': ['2A80F0D95FD747518A55DBD5D0BC22E8','BEC2ADA5E7979BDAFA986961EBD5076161FCC239E059586A'],
            'coinex14': ['938DC5D4312F4D5BA5B54E10228F507D','96A74E971733CF3C8F90927868E16A88E296057C05750926'],
            'coinex15': ['E54DAC40412B4E628715B1AAE245D45F','3E88B40957C59BD75110870103318D88B19ABA90EF4E9484'],
            'coinex16': ['C46DD8C575F846A09018C57AB39997F0','CA620D6455B825311D632186A3F301A6A15AE4D9877EC375'],
            'coinex17': ['E860A81912D94A5987EDB8A7F0B76043','9F8438A40970FB2EDD8C0AA54EA33F32B94ECFF0FCE2E7D2'],
            'coinex18': ['E66BB77994B04B73AF701D3CD4812E13','89FEF7D369B2F7966FE07F97BF16E0D6A1B8C341FAE883BA'],
            'coinex19': ['FF4A53ED20D848029BBE858CD5D0731C','86CE76CEA79E9E57CF879997E5672FACA7FECF061D29CE79'],
            'coinex20': ['35802414BDDB4C7E97B3EF583E3D885C','190EF30C4E85B6694DCF03BE1F22759108F7D5BABE67C65A'],
            'coinex21': ['A5554DC36EBA48C4A8B16730604CAB0A','98F784842DC7333D5ABE690001CBF6DB635B958131443946'],
            'coinex22': ['EEDFCE4B63CB41AC82997311E27D7288','DB1230304F0F81417D27B8C75B78E52B94AC9D930B229AE0'],
            'coinex23': ['7100BDC867BE482AA8CF16AFB1353A79','4A7F26BFE97DDFDB52C234274D1A17EC2F8D1B35D82222B5'],
            'coinex24': ['2E40156013CA48BC8FCCE3386F7790A4','77D8D8C8771E4AB0BB32E0B3E637C49DCA517BA370D888FF'],
            'coinex25': ['D645BCB2B8354FF284BB0E0AB266A225','E01C347422035E46D904E0BB998C96A0D1A0EA5B86D3906C'],
            'coinex25': ['D645BCB2B8354FF284BB0E0AB266A225','E01C347422035E46D904E0BB998C96A0D1A0EA5B86D3906C'],    #现货
            'partner96': ['ADCB602A39A44D4892AE9389AB123F2D','329AA342EA2300FC5C924C7E245540FD1B935C938435CACA'],    #现货
            'partner97': ['E8DE113296A944D5BA878AE333E9CD2E','0E4547338A8A46C341EC79EB50DC4054B9A21EA526A7089F'],    #合约
            'partner98': ['E7F385603C934733BCEE87BDD830EB3F','8576A1E8D2317799A98F63A3C3CB1B0ED98A53F2DBA624A6'],    #合约

            '华哥母': ['0FE5A1CE2D3A49E392A9D9D1599B970A','03EBE0995C04593AF74F42D5BD91D4A38ADB303002502F0E'],
            '华哥1': ['1C471D127F4043DC9F978D58F161A4EC','77D8D8C8771E4AB0BB32E0B3E637C49DCA517BA370D888FF'],

            '克时1': ['2CCFBEB569F5439E88DFB2A5CC4AEA90','ECC183BD83A281F9D26D456956B5C576283C7ACCDE21C491'],
        }

        self.debug = 0
        self.debugs = []
        self.ccyBi = ccyBi if ccyBi else CcyBi
        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]

        self.url = 'https://api.coinex.com'
        self.session = requests.Session()
        
        """ 多出口IP"""
        try:
            ips = DuoIp
        except:
            ips = []

        self.i = 0
        self.ips = []
        if len(ips) > 1:
            log('Coinex 使用多IP获取行情', ips)
            for ip in ips:
                from requests_toolbelt.adapters import source  #指定出口IP
                sb = requests.Session()
                new_source = source.SourceAddressAdapter(ip)
                sb.mount('http://', new_source)
                sb.mount('https://', new_source)
                self.ips.append(sb)

    
    def dispatch_request(self, http_method):

        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(http_method, 'GET')

    @staticmethod
    def get_sign(params, secret_key):
        sort_params = params
        data = []
        for item in sort_params:
            data.append(item + '=' + str(params[item]))
        str_params = "{0}&secret_key={1}".format('&'.join(data), secret_key)
        token = hashlib.sha256(str_params.encode("utf8")).hexdigest()
        return token

    """ 获取现货Sgin"""
    @staticmethod
    def get_sign_spot(params, secret_key):
        sort_params = sorted(params)
        data = []
        for item in sort_params:
            data.append(item + '=' + str(params[item]))
        str_params = "{0}&secret_key={1}".format('&'.join(data), secret_key)
        token = hashlib.md5(str_params.encode("utf8")).hexdigest().upper()
        return token
    
    def set_authorization(self, params, spot=0):
        if spot:
            params['access_id'] = self.access_id
            params['tonce'] = int(time.time()*1000)
            sgin = self.get_sign_spot(params, self.secret_key)
        else:
            params['timestamp'] = int(time.time()*1000)
            sgin = self.get_sign(params, self.secret_key)

        self.session.headers = {
            'Authorization': sgin,
            'Content-Type': 'application/json; charset=utf-8',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36',
            "AccessId":self.access_id
        }

    


    # 请求API
    def go(self, http_method, url_path, payload={}, url='https://api.coinex.com', needKey=1, spot=0):


        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= len(self.ips):
                self.i = 0

        if needKey:
            self.set_authorization(payload, spot)
            
        query_string = urlencode(payload)
        query_string = query_string.replace('%27', '%22')

        
        url2 = url + url_path
        
        if http_method in ['GET', 'DELETE']:
            url2 += '?' + query_string
        
        params = {'url': url2, 'timeout': 5}

        if http_method == 'POST':
            params['data'] = json.dumps(payload) if spot else payload

        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        # print(params)
        try:
            response = self.dispatch_request(http_method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败',url2)
            time.sleep(1)
            return self.go(http_method,url_path, payload,url,needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(url_path, payload, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2

    """获取可用余额"""
    def GetYuer(self, p=1, ref=1, ccyBi=0, symbols={}):
        ccyBi = ccyBi if ccyBi else self.ccyBi

        data = self.go("GET","/perpetual/v1/asset/query")

        no = 0
        ok = 0
        for bi in data['data']:
            if bi == ccyBi: #USDT
                ok = data['data'][bi]
                break
        if not ok:
            no = 1

        if no:
            print('Coinex获取余额失败', data)
            if ref:
                time.sleep(1)
                return self.GetYuer(p)
            else:
                return 0

        ok['all'] = float(ok['available'])+float(ok['frozen'])+float(ok['margin'])+float(ok['profit_unreal'])
        ok['keyong'] = N(ok['available'], 4)
        ok['yingkui'] = N(ok['profit_unreal'], 4)

        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01
        if p:
            log(p+'  Coinex USDT总余额' if type(p) == str else 'Coinex USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                 '持仓盈亏', N(ok['yingkui'], 4))

        return ok


    """设置杠杆倍数"""
    def SetGangGan(self, symbol, beishu):
        beishu = str(int(beishu))
        fh = self.go("POST","/perpetual/v1/market/adjust_leverage",{'market': symbol,'leverage': beishu, 'position_type': 2})
        log('Coinex', symbol, '设置杠杠', beishu, fh)
        return fh


    """ 获取仓位限制"""
    def GetLimit(self):
        fh = self.go("GET","/perpetual/v1/market/limit_config",{})['data']
        return fh

    """ 获取交易规则限制"""
    def GetSymbols(self):
        fh = self.go("GET", "/perpetual/v1/market/list", {})['data']
        return fh


    """ 全部撤单"""
    def DeleteAllOrder(self, symbol):
        return
        # fh = self.go('DELETE', f'/api/v1/orders?symbol={id}', {})
        # log('Kucoin 全部撤单', symbol, id, fh)
        # return fh

    """ 获取所有交易对价格、挂单盘口"""
    def GetTickerAll(self):
        data = self.go("GET","/perpetual/v1/market/ticker/all", {}, needKey=0)

        try:
            return data['data']['ticker']
        
        except:
            print("Coinex 获取ticker出错")
            return {}


    """ 获取所有持仓"""
    def GetPos(self, symbol=0):
        
        data = self.go("GET","/perpetual/v1/position/pending", {'market': symbol} if symbol else {})

        try:
            if 'message' in data and data['message'] == 'OK':
                okdata = data['data']
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['market']
                    ok['position_id'] = v['position_id']
                    ok['side'] = 'SELL' if v['side'] == 1 else 'BUY'
                    ok['side2'] = 'BUY' if v['side'] == 1 else 'SELL'
                    ok['liang'] = float(v['amount'])
                    ok['jiage'] = float(v['open_price'])
                    ok['yingkui'] = round(float(v['profit_unreal']), 4)
                    data.append(ok)

                return data[0] if symbol else data

            else:
                log(Color("Coinex套利 获取持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos(symbol)


        except Exception as e:
            uploadError(traceback.format_exc())

        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)



    """限价单开仓
    effect_type	委托生效类型，1: 一直有效直至取消, 2: 立刻成交或取消, 3: 完全成交或取消。默认为1
    option	1: 只下maker单, 2: 隐藏委托, 3: 只下maker单并隐藏委托。默认为0
    """
    def PostOrder(self, symbol, side, jiage, liang, orderType=1, option=0, msg=''):
        o = str(orderType)

        if orderType == 'ioc':
            orderType = 2
        
        elif orderType == 'post_only':
            orderType = 1
            option = 1

        elif orderType == 'normal':
            return self.MakePos(symbol, side, liang, jiage)
        
        jiage = float(jiage)
        t = NowTime_ms()
        post = {
            'market': symbol,
            'side': 2 if side == 'BUY' else 1,
            'price': jiage,
            'amount': liang,
            'effect_type': orderType,
            'option': option,
        }
        fh = 0
        for i in range(1):
            fh2 = self.go("POST","/perpetual/v1/order/put_limit", post)

            if 'code' in fh2 and fh2['code'] == 0:
                fh = fh2['data']['order_id']
                break
            else:
                log('限价OpenError', post, fh2, Color("", -1))

                if 'code' in fh2 and fh2['code'] not in [3008, 4006, 3109, 3127, 3108, 3007]: 
                    #4006 authorization fail
                    #3109 没钱了
                    #3127 数量太少
                    #3108 金额超过限制
                    #3007 结算费率
                    uploadError(symbol+' Coinex下单失败：'+str(post)+'  '+str(fh2))

                break
                if 'code' in fh2 and fh2['code'] == 3108:
                    post['amount'] /= 2

                if 'code' in fh2 and (fh2['code'] == 3127 or fh2['code'] == 3109):
                    break
                


        msg = ["Coinex", Color("限价Open", 1), side, symbol, '方式', o, "价格", jiage, "量", str(liang)+' ('+STR_N(float(liang)*jiage)+'$)',
            orderType, option, msg, fh, str(NowTime_ms()-t)+'ms']

        return fh, msg


    """撤销所有挂单"""
    def DeleteSymbolOrder(self, symbol):
        t = NowTime_ms()
        data = self.go("POST", "/perpetual/v1/order/cancel_all", {'market': symbol})

        print(GetTime('', echoMs=1), 'Coinex撤单', symbol, data, str(NowTime_ms()-t)+'ms')
        
        return data
        

    """限价单平仓
    effect_type	委托生效类型，1: 一直有效直至取消, 2: 立刻成交或取消, 3: 完全成交或取消。默认为1
    option	1: 只下maker单, 2: 隐藏委托, 3: 只下maker单并隐藏委托。默认为0
    """
    def LimitColsePos(self, symbol, position_id, jiage, liang, orderType=1, option=0, msg2=''):
        o = str(orderType)

        if orderType == 'ioc':
            orderType = 2
        
        elif orderType == 'post_only':
            orderType = 1
            option = 1

        t = NowTime_ms()
        post = {
            'market': symbol,
            'position_id': position_id,
            'price': jiage,
            'amount': liang,
            'effect_type': orderType,
            'option': option,
        }
        for i in range(1):
            fh2 = self.go("POST","/perpetual/v1/order/close_limit", post)
            if 'code' in fh2 and fh2['code'] == 0:
                fh = fh2['data']['order_id']
                break
            else:
                log('限价CloseError', fh2, Color("", -1))

                fh = 0
                if 'code' in fh2 and fh2['code'] == 3127:
                    fh = -1
                    break

                # if 'code' not in fh2 or fh2['code'] not in [3008, 4006]: #4006 authorization fail
                #     uploadError(symbol+' Coinex平仓失败：'+str(post)+'  '+str(fh2))
                    

        msg = ["Coinex", Color("LimitClose", -1), position_id, '方式', o, symbol, "价格", jiage, "量", str(liang)+' ('+STR_N(float(liang)*jiage)+'$)',
         orderType, option, Color(msg2, 1), fh, str(NowTime_ms()-t)+'ms']

        return fh, msg


    """市价开仓"""
    def MakePos(self, symbol, side, liang, jiage=0):
        t = NowTime_ms()
        data = {
            'market': symbol,
            'side': 2 if side == 'BUY' else 1,
            'amount': liang,
        }
        for i in range(5):
            fh2 = self.go("POST","/perpetual/v1/order/put_market", data)
            if 'code' in fh2 and fh2['code'] == 0:
                fh = fh2['data']['order_id']
                break
            else:
                log('市价开仓Error', fh2, Color("", -1))
                fh = 0
                if 'code' in fh2 and fh2['code'] == 3109:
                    break

        log("Coinex", Color("市价Open", 1), side, symbol, "价格", jiage, "量", str(liang)+' ('+STR_N(float(liang)*float(jiage))+'$)', str(NowTime_ms()-t)+'ms')

        return fh, ['']



    """市价平仓"""
    def ClosePos(self, symbol, position_id, liang=0, jiage=0, side=''):
        t = NowTime_ms()
        data = {
            'market': symbol,
            'position_id': position_id,
        }

        ###########################################################
        # if liang:
        #     data['amount'] = liang

        for i in range(5):
            fh2 = self.go("POST","/perpetual/v1/order/close_market", data)
            if 'code' in fh2 and fh2['code'] == 0:
                fh = fh2['data']['order_id']
                break
            else:
                log('套利市价平仓Error', data, fh2, Color("", -1))
                fh = 0
                if 'code' in fh2 and fh2['code'] == 3127:
                    break

        msg = ["Coinex", Color("TakerClose 套利单腿成交", -1), side, symbol, position_id,
         "价格", jiage, "量", str(liang)+' ('+STR_N(float(liang)*jiage)+'$)', str(NowTime_ms()-t)+'ms']

        return fh, msg



    """提现"""
    def Tixian(self, dizhi, liang, bi='USDT', net='TRC20'):
        post = {
            'coin_type': bi, 
            'smart_contract_name': net, 
            'coin_address': dizhi, 
            'transfer_method': "onchain",     #1.onchain – 链上转账 2.local – 站内转账
            'actual_amount': liang, 
        }
        data = self.go("POST", "/v1/balance/coin/withdraw", post, spot=1)
        log('Coinex 提现 地址', dizhi, '数量', liang, '币种', bi, '结果', data)
        return data


    """ 子母账户现货相互转账，“in”表示转入, “out”表示转出"""
    def ziTixian(self, liang, zi, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'coin_type': bi,
            'amount': str(liang),
        }
        if zi:
            data['transfer_account'] = zi
        if side:
            data['transfer_side'] = side

        data = self.go("POST","/v1/sub_account/transfer", data, spot=1)

        log('[Coinex子母现货]', zi, '子转母' if side == 'in' else '母转子', bi, liang, '结果', data, Color('', 1))
        return 'message' in data and data['message'] == 'Success'


    """ 现货和合约之间的划转"""
    def huazhuan(self, liang, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'coin_type': bi,
            'transfer_side': side,
            'amount': str(liang),
        }

        data = self.go("POST","/v1/contract/balance/transfer", data, spot=1)

        log('[Coinex子账户]', '现货转入合约' if side == 'in' else '合约转入现货', '币种', bi, liang, '结果', data, Color('', 1))
        
        fh = 0
        if 'message' in data and data['message'] == 'Success':
            fh = 1
        if 'message' in data and data['message'] == 'Futures transfer failed':
            fh = 1
        return fh
        

    
    """获取可用余额"""
    def GetXianhuo(self, p=1):
        data = self.go("GET", "/v1/balance/info", spot=1)

        no = 0
        ok = 0
        for bi in data['data']:
            if bi == CcyBi: #USDT
                ok = data['data'][bi]
                break
        if not ok:
            no = 1

        if no:
            print('Coinex获取现货余额失败', data)
            return 0


        return N(ok['available'])

    """ 创建ApiKey"""
    def CreateApiKey(self, username, ips):
        post = {
            "allow_trade": True,
            "allowed_ips": ips,
            "sub_user_name": username,
        }
        data = self.go('POST', '/v1/sub_account/auth/api', post, spot=1)
        
        try:
            data = data['data']
            print(f"'{username}': ['"+data['access_id']+"','"+data['secret_key']+"'],")

        except:
            print(post, '创建api', data)
    