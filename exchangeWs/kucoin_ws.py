#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *
from moneyV2.money import changePos


from moneyV2.money import changePos

class kucoin_ws():
    def __init__(self, coinex, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.coinex = coinex
        self.access_id = coinex.access_id
        self.secret_key = coinex.secret_key
        self.passwd = coinex.passwd

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.symbol = coinex.symbol
        self.data = {}
        self.pos = []
        self.order_status = ['done']

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新订单"""
    def _update_order(self, data2):
        try:
            """
            status訂單狀態說明
                "match": 訂單爲taker時與買賣盤中訂單成交，此時該taker訂單狀態爲match；
                "open": 訂單存在於買賣盤中；
                "done": 訂單完成；
            type消息類型說明
                "open": 訂單進入買賣盤時發出的消息；
                "match": 訂單成交時發出的消息；
                "filled": 訂單因成交後狀態變爲DONE時發出的消息；
                "canceled": 訂單因被取消後狀態變爲DONE時發出的消息；
                "update": 訂單因被修改發出的消息；
            """
            id = data2['orderId']
            symbol = data2['symbol']
            side = data2['side'].upper()

            if 'price' not in data2 or not data2['price']:    #委托价格
                if id in self.orders:
                    data2['price'] = self.orders[id]['price']
                    data2['size'] = self.orders[id]['size']
                else:
                    data2['price'] = '0'
                    data2['size'] = '0'

            if 'matchPrice' not in data2 or not data2['matchPrice']:   #成交价格
                if id in self.orders:
                    data2['matchPrice'] = self.orders[id]['jiage']
                else:
                    data2['matchPrice'] = float(data2['price'])

            data2['orderType'] = data2['orderType'] if 'orderType' in data2 else '-'

            jiage = float(data2['matchPrice'])
            liang = float(data2['filledSize'])

            self.orders[id] = {
                'id': id,                  #交易对
                'symbol': symbol,                  #交易对
                'status': data2['status'],                  #状态
                'side': side,                               #方向
                'type': data2['orderType'],                     #订单类型
                'jiage': jiage,             #累积成交均价
                'liang': liang,         #累积成交量
                'price': data2['price'],
                'size': data2['size'],
                'yingkui': 0,             #盈亏
                'jiazhi': 0,  #价值
                'time': NowTime(),                #推送时间
            }

            # pprint(self.orders[id])
            # print("------------------")

            #价格小数位

            if symbol in self.symbol:
                MZ = self.symbol[symbol]['Z']
            else:
                MZ = 1


            msg = ['[KuCoin监听订单]', GetTime(echoMs=1), '', side, symbol, data2['orderType'], data2['type'],
            "　ID", id, data2['status'], '面值', MZ,
            '　价格', data2['price'], '数量', str(data2['size']) + ' ('+U(float(data2['price'])*float(data2['size'])*MZ)+')',
            '　成交价', jiage, '成交量', str(liang) + ' ('+U(jiage*liang*MZ)+')']

            if liang:
                log(*msg)
                if data2['status'] in self.order_status:
                    # print(self.pos)
                    changePos(self, symbol, side, jiage, liang)
                    # print(self.pos, '-----> new')
            else:
                print(*msg)


            
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
            
        except Exception as e:
            uploadError(traceback.format_exc())



    """ 更新余额"""
    def _update_account(self, data):
        pass
        


    """ 更新盘口和费率信息"""
    def _update_depth(self, v):
        s = v['symbol']
        if s not in self.data:
            self.data[s] = {'feilv': self.coinex.GetSymbolFeilv(s)}

        self.data[s]['bidPx']  = float(v['bestBidPrice'])
        self.data[s]['bidQty'] =  float(v['bestBidSize'])
        self.data[s]['askPx']  = float(v['bestAskPrice'])
        self.data[s]['askQty'] = float(v['bestAskSize'])
        self.data[s]['t']      = int(v['ts'] / 1000000)

        if self.depathCallback:
            self.depathCallback(s, self.data[s])
            
    """ 更新费率"""
    def _update_feilv(self, msg):
        data = msg['data']
        s = msg['topic'].split(':')[-1]

        if data['granularity'] == ********:
            self.data[s]['feilv'] = float(data['fundingRate']) * 100
            

    async def ping(self, conn):
        while True:
            msg = {
                'id': str(int(time.time() * 1000)),
                'type': 'ping'
            }
            await conn.send_str(ujson.dumps(msg))
            await asyncio.sleep(10)


    async def main(self, symbols=[]):
        while True:
            try:

                url = self.coinex.GetWsUrl(0 if symbols else 1)
                # print(url, symbols)

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:
                    # print(url, symbols, '成功')

                    if symbols:
                        channels = []
                        for symbol in symbols:
                            channels += [
                                f"/contractMarket/tickerV2:{symbol}"
                            ]
                            channels += [
                                f"/contract/instrument:{symbol}"
                            ]

                    else:
                        # 订阅
                        channels = [
                            f"/contractMarket/tradeOrders",
                        ]

                    for i in channels:
                        sub_str = ujson.dumps({"topic": i, "type":"subscribe"})
                        await conn.send_str(sub_str)

                    asyncio.create_task(self.ping(conn))

                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('Ku ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Ku ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        # if not symbols:
                        #     print(msg)

                        try:
                            msg = ujson.loads(msg)

                        except:
                            print('Ku 解析失败: '+str(msg))
                            break

                        if 'data' in msg:

                            if 'orderChange' in msg['subject']:self._update_order(msg['data'])
                            elif 'tickerV2' in msg['subject']:self._update_depth(msg['data'])
                            elif 'funding.rate' in msg['subject']:self._update_feilv(msg)


            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'Ku ws连接失败 开始重连...', symbols)

                #强制重启
                os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")