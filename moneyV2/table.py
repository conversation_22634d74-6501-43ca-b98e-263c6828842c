from main.hanshu import *
from main.config import *
from moneyV2.money import *


def RestDayTongji(self, s):
    day = GetTime(0, "%m-%d")
    if self.api.d['tongji'][s]['today']['time'] != day:
        self.api.d['tongji'][s]['today'] = {
            'time': day, 
            'volume': 0,
            'count': 0,         #次数
            'shenglv': 0,       #胜率
            'm': 0,             #盈利
            'ok_count': 0,      #成功次数
            'ok_m': 0,          #盈利金额
            'no_count': 0,
            'no_m': 0,
            'bian_feilv': 0,
            'coinex_feilv': 0,
        }



# 统计
def Tongji(self, open, pos, pos2, order, order2, yingkui=0, jiazhi=0, s=0, openPos=0, feilv=0):

    if pos:
        s = pos['symbol']

    if s and s not in self.api.d['tongji']:
        self.api.d['tongji'][s] = {
            'volume': 0,
            'count': 0,         #次数
            'shenglv': 0,       #胜率
            'm': 0,             #盈利
            'ok_count': 0,      #成功次数
            'ok_m': 0,          #盈利金额
            'no_count': 0,
            'no_m': 0,
            'bian_feilv': 0,
            'coinex_feilv': 0,
            'today': {  #今日统计
                'time': 0, 
            },
            'no_lianxu': 0,     #连续失败次数
        }
        
        RestDayTongji(self, s)



    """ 只是为了重置当日统计"""
    if feilv:
        if feilv == Exchange:
            self.api.d['bian_feilv'] += yingkui
            self.api.d['today']['bian_feilv'] += yingkui
            self.api.d['tongji'][s]['bian_feilv'] += yingkui
            self.api.d['tongji'][s]['today']['bian_feilv'] += yingkui
        else:
            self.api.d['coinex_feilv'] += yingkui
            self.api.d['today']['coinex_feilv'] += yingkui
            self.api.d['tongji'][s]['coinex_feilv'] += yingkui
            self.api.d['tongji'][s]['today']['coinex_feilv'] += yingkui

        log(s, feilv, '资金费率 -->', U(yingkui), StrBi(yingkui/self.ws.usdt, 5, kuo=1))
        return

    
    """ 开仓和平仓交易量统计分开统计"""
    if openPos:
        self.api.d['tongji'][s]['volume'] += jiazhi
        self.api.d['tongji'][s]['today']['volume'] += jiazhi
        self.api.d['volume'] += jiazhi
        self.api.d['today']['volume'] += jiazhi
        return


    if not yingkui and pos:
        pos, pos2 = pos.copy(), pos2.copy()
        sxf = BianSxf + CoinexSxf
        if self.name == 'CoinexSpot' and pos2 and self.symbol2[pos2['symbol']]['amm']:
            sxf = BianSxf + AmmCoinexSxf

        posLiang = pos['liang']
        pos2Liang = pos2['liang']
        openTime = pos2['create_time'] if 'create_time' in pos2 else 0
        minute = round((NowTime() - openTime) / 60, 1) if openTime else 0
        jiazhi = max(round(order['liang']*order['jiage'], 2), 0.001)
        jiazhi2 = round(order2['liang']*order2['jiage'], 2)
        if self.fuckbg:
            jiazhi = jiazhi2
        jiazhi_pos = round(posLiang*order['jiage'], 2)
        jiazhi2_pos = round(pos2Liang*order2['jiage'], 2)
        pos['bzj'], pos['liang'] = jiazhi, order['liang']
        pos2['bzj'], pos2['liang'] = jiazhi2, order2['liang']
        bianYingkui, bianRoe = GetRoe(order['jiage'], pos)
        coinexYingkui, coinexRoe = GetRoe(order2['jiage'], pos2)
        yingkui3 = bianYingkui + coinexYingkui
        yingkui = yingkui3 - (jiazhi * sxf)
        shouyilv = yingkui3 / jiazhi


        log("第", self.api.d['count']+1, "次套利完成", pos['symbol'],
            '<br/>开仓', open +' ('+GetTime(openTime, '%m-%d %H:%M:%S')+') ('+STR_N(minute, 1)+')',

            '<br/><br/>'+Exchange, pos['side'], '开仓价', pos['jiage'], '平仓价', order['jiage'],
            '<br/>持仓量', str(posLiang)+ ' (' + STR_N(jiazhi_pos, 2)+'$)'+ ' (' + StrBi(jiazhi_pos/self.ws.usdt)+')',
                '平仓量', str(order['liang'])+ ' (' + STR_N(jiazhi, 2)+'$)'+ ' (' + StrBi(jiazhi/self.ws.usdt)+')',
            '<br/>盈亏', str(bianYingkui)+ ' (' + StrBi(bianRoe/100)+')',
                '订单盈亏', str(order['yingkui'])+ ' (' + StrBi(order['yingkui']/jiazhi)+')',
                
            '<br/><br/>'+self.name, pos2['side'], '开仓价', pos2['jiage'], '平仓价', order2['jiage'],
            '<br/>持仓量', str(pos2Liang)+ ' (' + STR_N(jiazhi2_pos, 2)+'$)'+ ' (' + StrBi(jiazhi2_pos/self.ws.usdt)+')',
                '平仓量', str(order2['liang'])+ ' (' + STR_N(jiazhi2, 2)+'$)'+ ' (' + StrBi(jiazhi2/self.ws.usdt)+')',
            '<br/>盈亏', str(coinexYingkui)+ ' (' + StrBi(coinexRoe/100)+')',
                '订单盈亏', str(order2['yingkui'])+ ' (' + StrBi(order2['yingkui']/jiazhi)+')',

            '<br/><br/>净利润', STR_N(yingkui3)+ ' (' +StrBi(shouyilv)+')'+ ' (' +StrBi(yingkui3/self.ws.usdt)+')',
            '<br/>手续费', STR_N(yingkui)+ ' (' +StrBi(shouyilv-sxf)+')'+ ' (' +StrBi(yingkui/self.ws.usdt)+')',

            )
        
        self.api.d['bian_m'] += bianYingkui - (jiazhi * BianSxf)
        self.api.d['coinex_m'] += coinexYingkui - (jiazhi2 * CoinexSxf)

        #上传数据到Web，做大数据可视化
        # logPos({
        #     'symbol': pos['symbol'], 'open': open, 'time':  openTime, 'sxf': sxf, 'yuer': self.ws.usdt,
        #     'bnb_side': pos['side'], 'bnb_price': pos['jiage'], 'bnb_close_price': order['jiage'], 
        #     'bnb_pos': posLiang, 'bnb_pos_jiazhi': jiazhi_pos,
        #     'bnb_close_pos': order['liang'],  'bnb_close_pos_jiazhi': jiazhi,
        #     'bnb_profit': bianYingkui, 'bnb_profit_roe': bianRoe, 
        #     'bnb_order_profit': order['yingkui'], 'bnb_order_profit_roe': N(order['yingkui']/jiazhi, 4),

        #     'coinex_side': pos2['side'], 'coinex_price': pos2['jiage'], 'coinex_close_price': order2['jiage'], 
        #     'coinex_pos': pos2Liang, 'coinex_pos_jiazhi': jiazhi2_pos,
        #     'coinex_close_pos': order2['liang'],  'coinex_close_pos_jiazhi': jiazhi2,
        #     'coinex_profit': coinexYingkui, 'coinex_profit_roe': coinexRoe,
        #     'coinex_order_profit': order2['yingkui'], 'coinex_order_profit_roe': N(order2['yingkui']/jiazhi, 4),

        #     'profit': yingkui3,  'profit_roe': shouyilv, 'profit_roe2': N(yingkui3/self.ws.usdt, 4),
        #     'profit_sxf': yingkui, 'profit_sxf_roe': N(shouyilv-sxf, 5), 'profit_sxf_roe2': N(yingkui/self.ws.usdt, 4),
        # })


    elif jiazhi > 5:
        self.api.d['dantui'] += 1
        self.api.d['today']['dantui'] += 1

    if s:
        if yingkui >= 0:
            self.api.d['tongji'][s]['no_lianxu'] = 0
            self.api.d['tongji'][s]['ok_count'] += 1
            self.api.d['tongji'][s]['ok_m'] += yingkui
            self.api.d['tongji'][s]['today']['ok_count'] += 1
            self.api.d['tongji'][s]['today']['ok_m'] += yingkui
        else:
            self.api.d['tongji'][s]['no_lianxu'] += 1
            self.api.d['tongji'][s]['no_count'] += 1
            self.api.d['tongji'][s]['no_m'] += yingkui
            self.api.d['tongji'][s]['today']['no_count'] += 1
            self.api.d['tongji'][s]['today']['no_m'] += yingkui

        #胜率统计
        self.api.d['tongji'][s]['volume'] += jiazhi
        self.api.d['tongji'][s]['today']['volume'] += jiazhi
        self.api.d['tongji'][s]['count'] = self.api.d['tongji'][s]['ok_count'] + self.api.d['tongji'][s]['no_count']
        self.api.d['tongji'][s]['shenglv'] = N(self.api.d['tongji'][s]['ok_count'] / self.api.d['tongji'][s]['count'] * 100,2) if self.api.d['tongji'][s]['ok_count'] and self.api.d['tongji'][s]['count'] else 0
        self.api.d['tongji'][s]['m'] = N(self.api.d['tongji'][s]['ok_m'] + self.api.d['tongji'][s]['no_m'],2)

        #今日胜率统计
        self.api.d['tongji'][s]['today']['count'] = self.api.d['tongji'][s]['today']['ok_count'] + self.api.d['tongji'][s]['today']['no_count']
        self.api.d['tongji'][s]['today']['shenglv'] = N(self.api.d['tongji'][s]['today']['ok_count'] / self.api.d['tongji'][s]['today']['count'] * 100, 2) if self.api.d['tongji'][s]['today']['ok_count'] and self.api.d['tongji'][s]['today']['count'] else 0
        self.api.d['tongji'][s]['today']['m'] = N(self.api.d['tongji'][s]['today']['ok_m'] + self.api.d['tongji'][s]['today']['no_m'], 2)

    #统计信息
    if yingkui >= 0:
        self.api.d['no_lianxu'] = 0
        self.api.d['ok_count'] += 1
        self.api.d['ok_m'] += yingkui
        self.api.d['today']['ok_count'] += 1
        self.api.d['today']['ok_m'] += yingkui
    else:
        self.api.d['no_lianxu'] += 1
        self.api.d['no_count'] += 1
        self.api.d['no_m'] += yingkui
        self.api.d['today']['no_count'] += 1
        self.api.d['today']['no_m'] += yingkui
        self.api.d['today']['no_m2'] += yingkui #重启清0

    yuerall = self.ws.usdt + self.ws2.usdt
    if yuerall > self.api.d['maxYuer']:
        self.api.d['maxYuer'] = yuerall

    #胜率统计
    self.api.d['volume'] += jiazhi
    self.api.d['today']['volume'] += jiazhi
    self.api.d['count'] = self.api.d['ok_count'] + self.api.d['no_count']
    self.api.d['shenglv'] = N(self.api.d['ok_count'] / self.api.d['count'] * 100,2) if self.api.d['ok_count'] and self.api.d['count'] else 0
    self.api.d['m'] = N(self.api.d['ok_m'] + self.api.d['no_m'],2)
    self.api.d['m'] = N(self.api.d['ok_m'] + self.api.d['no_m'],2)

    #今日胜率统计
    self.api.d['today']['count'] = self.api.d['today']['ok_count'] + self.api.d['today']['no_count']
    self.api.d['today']['shenglv'] = N(self.api.d['today']['ok_count'] / self.api.d['today']['count'] * 100, 2) if self.api.d['today']['ok_count'] and self.api.d['today']['count'] else 0
    self.api.d['today']['m'] = N(self.api.d['today']['ok_m'] + self.api.d['today']['no_m'], 2)
    tongji_msg = '总'+str(self.api.d['count'])+" 成功"+str(self.api.d['ok_count'])+" 失败"+str(self.api.d['no_count'])+" 连续失败"+str(self.api.d['no_lianxu'])+' 胜率'+str(self.api.d['shenglv'])+"%"
    tongji_msg += '　亏损'+str(N(self.api.d['no_m'],2))+' 盈利'+str(N(self.api.d['ok_m'],2))+' 收益'+str(self.api.d['m'])
    tongji_msg += '　币安'+str(N(self.api.d['bian_m'],2))+f' {Exchange2}'+str(N(self.api.d['coinex_m'],2))
    log(tongji_msg)

    self.api.SaveJson()
    updateTable(self, p=1)



""" 决定是否上传Table"""
def ifUpdateTable(self):

    """
    FUCKBG
    FUCKBG
    FUCKBG    
    """
    fuckBg(self)
    
    self.bianJiazhi = 0
    self.bianYingkui = 0
    # self.bianPosYingkui = 0
    self.coinexJiazhi = 0
    self.coinexYingkui = 0
    # self.coinexPosYingkui = 0
    self.bianFeilv = 0
    self.coinexFeilv = 0
    self.buy = 0
    self.tables['rows'] = []

    for pos in self.ws.pos:
        if pos['side']+pos['symbol'] in self.pos_tables:
            data = self.pos_tables[pos['side']+pos['symbol']]

        elif pos['symbol'] in self.pos_tables:
            data = self.pos_tables[pos['symbol']]
            
        else:
            continue

        self.tables['rows'].append(data['rows'].copy())
        self.bianJiazhi += data['bian_jiazhi']
        self.bianYingkui += data['bian_yingkui']
        # self.bianPosYingkui += data['bian_pos_yingkui']
        self.coinexJiazhi += data['coinex_jiazhi']
        self.coinexYingkui += data['coinex_yingkui']
        # self.coinexPosYingkui += data['coinex_pos_yingkui']
        self.bianFeilv += data['bian_feilv']
        self.coinexFeilv += data['coinex_feilv']
        if pos['side'] == 'SELL':
            self.buy += data['coinex_jiazhi']

        
        t1 = int(GetTime(NowTime() , "%H"))
        t2 = int(GetTime(NowTime() + 5 , "%H"))
        if t1 not in self.feilv_time and t2 in self.feilv_time and tlog("feilv1"+pos['symbol'] , '', 15, xs=0):
            if Exchange == 'Dydx':
                data['bian_feilv'] = data['bian_feilv'] / 8
            Tongji(self, 0, 0, 0, 0, 0, s=pos['symbol'], yingkui=data['bian_feilv'], feilv=Exchange)

        elif t1 not in self.feilv_time2 and t2 in self.feilv_time2 and tlog("feilv2"+pos['symbol'] , '', 15, xs=0):
            if Exchange2 == 'Dydx':
                data['coinex_feilv'] = data['coinex_feilv'] / 8
            Tongji(self, 0, 0, 0, 0, 0, s=pos['symbol'], yingkui=data['coinex_feilv'], feilv=Exchange2)
    
    # if 'One' in ServerName:
    #     for n in self.pos_tables:
    #         data = self.pos_tables[n]
    #         self.tables['rows'].append(data['rows'].copy())
    #         self.coinexJiazhi += data['coinex_jiazhi']

    
    """ 显示Table"""
    if self.coinexJiazhi:
        

        """排序"""
        tmp = self.tables['rows']  # 得到列表: L=[('a', 1), ('c', 3), ('b', 2)]
        try:
            tmp.sort(key=lambda x: x[-1], reverse=False)
        except:
            pass

        bh = 0
        for v in tmp:
            try:
                self.tables['rows'][bh][0] = str(bh+1)+'.'+v[0]
                self.tables['rows'][bh][-1] = GetTime(v[-1],'%m-%d %H:%M')
            except:
                pass
            bh += 1


        fh = [
            '汇总:', STR_N(self.bianJiazhi) + '$ (' +StrBi(self.bianJiazhi/self.ws.usdt)+')',
            '', '', '', STR_N(self.bianYingkui) + ' (' +StrBi(self.bianYingkui/self.ws.usdt)+')',
            '', STR_N(self.coinexJiazhi) + '$ (' +StrBi(self.coinexJiazhi/self.ws2.usdt)+')' + ' (多' +StrBi(self.buy/max(0.01, self.coinexJiazhi))+')',
            '', '',  '',
            STR_N(self.coinexYingkui) + ' (' +StrBi(self.coinexYingkui/self.ws.usdt)+')',
            STR_N(self.bianYingkui+self.coinexYingkui),
            '', '', STR_N(self.bianFeilv)+'$'+' | '+STR_N(self.coinexFeilv)+'$', '',
        ]
        self.api.d['yingkui'] = self.bianYingkui+self.coinexYingkui
        self.api.d['bian_feilv'] = self.bianFeilv
        self.api.d['coinex_feilv'] = self.coinexFeilv
        self.api.d['pos_jiazhi2'] = round(self.coinexJiazhi / self.ws2.usdt, 1)

        self.tables['rows'].append(fh)

    else:
        
        self.api.d['yingkui'] = 0
        self.api.d['bian_feilv'] = 0
        self.api.d['coinex_feilv'] = 0
        self.api.d['pos_jiazhi2'] = 0

    # 不使用标记价格计算余额
    # self.ws.usdt = self.ws.usdt - self.bianPosYingkui + self.bianYingkui
    # self.ws2.usdt = self.ws2.usdt - self.coinexPosYingkui + self.coinexYingkui
    updateTable(self)



""" 重置统计"""
def SetZero(self):
    self.api.d = self.api.d_start
    self.api.SaveJson(qiangzhi=1)
    print('---------------------------重置')
    print('---------------------------重置')
    print('---------------------------重置')


""" 上传Table"""
def updateTable(self, p=0):

    tixian = self.api.d['huazhuan']
    yuer = self.ws.usdt + self.api.fanyong
    yuer2 = max(self.ws2.usdt, 0.01)

    if 'One' in ServerName:
        yuer = 100
    
    yuer = max(yuer, 0.01)
    yuer2 = max(yuer2, 0.01)
    
    yuerall = round(yuer+yuer2, 2)
    yuerall2 = round(self.api.d['yuer'] + self.api.d['coinex'], 2)



    #-------云端划转
    if glo.Huazhuan > 0 and glo.Huazhuan != glo.preHuazhuan:
        log('云端划转', glo.Huazhuan)
        self.api.d['huazhuan'] += glo.Huazhuan
        self.api.d['maxYuer'] = 0
        self.api.d['today']['yuer'] -= glo.Huazhuan / 2
        self.api.d['today']['coinex'] -= glo.Huazhuan / 2
        self.api.SaveJson(qiangzhi=1)
        
        if ServerNameId == '1':
            log(self.bnb.Huazhuan(glo.Huazhuan))
            print("提现余额：", glo.Huazhuan)
            
        uploadLog(isExit=1)


    #-------失败次数太多
    if self.api.d['today']['no_m2'] <= LianxuStop and not self.fuckbg:
        
        self.tables['rows'].append([Color("账户强停", -1) for i in range(len(self.tables['cols']))])
        tlog(Color('账户强停', -1), [self.api.d['today']['no_m2'], LianxuStop, GetTime(0, '%m-%d'), Color('禁止开仓...', -1)], 30*60)

    #-------订单连续失败次数太多
    if self.errorOrderTj > 15:
        # uploadError('订单连续失败，可能出现了bug')
        upPos(self, set=1)
        self.errorOrderTj = 0
    
    # 策略已经暂停
    if glo.Stop:
        self.tables['rows'].append([Color("彻底停机", -1) for i in range(len(self.tables['cols']))])

    # 策略已经暂停交易
    if glo.StopTrade :
        self.tables['rows'].append([Color("暂停交易", -1) for i in range(len(self.tables['cols']))])

    # 策略缓慢停机
    if glo.StopKai:
        self.tables['rows'].append([Color("缓慢停机", -1) for i in range(len(self.tables['cols']))])

    self.api.d['nowYuer'] = yuer    #给Web端的
    self.api.d['coinexNow'] = yuer2    #给Web端的
    self.api.d['keyong'] = self.ws.keyong    #给Web端的
    self.api.d['coinexKeyong'] = self.ws2.keyong    #给Web端的
    self.api.d['chengben'] = round((BianSxf+CoinexSxf) * 100, 3)
    self.api.d['OpenRoe'] = OpenRoe
    self.api.d['Kai_Bili'] = Kai_Bili
    self.api.d['FeilvRoe'] = FeilvRoe
    self.api.d['GangGan'] = GangGan
    self.api.d['RunTime'] = RunTime


    day = GetTime(0, "%m-%d")
    if self.api.d['today']['time'] != day:
        
        self.api.d['today'] = {  #重置今日统计
            'time': day, 
            'volume': 0,        #交易量
            'yuer': yuer,       #12点的余额
            'coinex': yuer2,       #12点的余额
            'count': 0,         #次数
            'shenglv': 0,       #胜率
            'm': 0,             #盈利
            'ok_count': 0,      #成功次数
            'ok_m': 0,          #盈利金额
            'no_count': 0,
            'no_m': 0,
            'no_m2': 0,         #这个值重启清0
            'dantui': 0,
            'bian_feilv': 0,
            'coinex_feilv': 0,
        }

    cha_day, cha_str = GetChaTime(self.api.d['time'])  #策略运行时长
    bianhua = N(yuer - self.api.d['yuer'] + (tixian / 2), 2)         #未提现变化
    bianhua2 = N(yuer - self.api.d['today']['yuer'], 2)         #当日变化
    bianhua_c = N(yuer2 - self.api.d['coinex'] + (tixian / 2), 2)         #未提现变化
    bianhua2_c = N(yuer2 - self.api.d['today']['coinex'], 2)         #当日变化
    bianhuaall  =  N(bianhua + bianhua_c, 2)
    bianhuaall2  =  N(bianhua2 + bianhua2_c, 2)

    #-------云端更新起始余额
    if glo.Yuer:
        log('修改起始余额', glo.Yuer)
        self.api.d['maxYuer'] = 0
        self.api.d['yuer'] += bianhuaall - glo.Yuer
        self.api.d['today']['yuer'] += bianhuaall - glo.Yuer
        self.api.SaveJson(qiangzhi=1)
        uploadLog(isExit=1)
        
    if p:
        return logProfit(bianhuaall)
    
    self.api.d['coinex'] = max(0.01, self.api.d['coinex'])
    self.api.d['today']['coinex'] = self.api.d['today']['coinex'] if self.api.d['today']['coinex'] else 0.01
    str_bianhua = str(bianhua)+' ('+StrBi(bianhua / self.api.d['yuer'])+')'
    str_bianhua2 = str(bianhua2)+' ('+StrBi(bianhua2 / self.api.d['today']['yuer'])+')'
    str_bianhua_c = str(bianhua_c)+' ('+StrBi(bianhua_c / self.api.d['coinex'])+')'
    str_bianhua2_c = str(bianhua2_c)+' ('+StrBi(bianhua2_c / self.api.d['today']['coinex'])+')'
    str_bianhua_all = str(bianhuaall)+' ('+StrBi(bianhuaall / yuerall)+')'
    str_bianhua_day  = str(bianhuaall2)+' ('+StrBi(bianhuaall2 / yuerall)+')'

    day1 = N(bianhuaall / cha_day, 2)           #日化
    day1 = str(day1)+' ('+StrBi(day1 / yuerall2)+')'

    day30 = N(bianhuaall / cha_day * 30, 2)     #月化
    day30 = StrBi(day30 / yuerall2)

    day365 = N(bianhuaall / cha_day * 365, 2)   #年化
    day365 = StrBi(day365 / yuerall2)

    stop = GetStop(self.api.d['maxYuer'])

    table = [self.tables.copy()]
    table.append( {
        "title" : "风控统计",
        "cols" : [
            "杠杆", '账户', '盘口', '计费币', '手续费', '总 / 开仓',
            '资金费 / 比例',
            "巅峰余额 (收益%) (回撤%) / 回撤强停 (回撤%)",
            '总', "成功", "失败", "连续", '单腿', '日均', '胜率', "盈利", "亏损", '亏损强停', "总收益", '交易量',
            '费1',  '费2', '总费',
        ],
        "rows" : [[
            str(GangGan), Api_Key,
             Exchange+' ('+self.api.feilv+')', CcyBi, StrBi(BianSxf), StrBi(BianSxf+CoinexSxf), StrBi(FeilvRoe/100),
            '今日：', 
            self.api.d['today']['count'], self.api.d['today']['ok_count'], self.api.d['today']['no_count'],
            '', self.api.d['today']['dantui'], '', str(self.api.d['today']['shenglv'])+'%',
            N(self.api.d['today']['ok_m'], 1), N(self.api.d['today']['no_m'], 1), STR_N(self.api.d['today']['no_m2'], 1)+' / '+str(LianxuStop),
             Color(self.api.d['today']['m']), str_of_num(self.api.d['today']['volume']),
             Color(N(self.api.d['today']['bian_feilv'], 1)), Color(N(self.api.d['today']['coinex_feilv'], 1)),
            Color(N(self.api.d['today']['bian_feilv']+self.api.d['today']['coinex_feilv'], 1))
        ],[ 
            str(SetGangGan),
            Api_Key2, 
            Exchange2, 
            CcyBi2, 
            StrBi(CoinexSxf),
            StrBi(OpenRoe/100),
            StrBi(Kai_Bili/100),
            STR_N(self.api.d['maxYuer'], 2)+' ('+StrBi((self.api.d['maxYuer']-yuerall2)/yuerall2)+') ('+StrBi((yuerall-self.api.d['maxYuer'])/yuerall2)+') / '+str(stop)+') ('+str(int((1-QiangPing)*-100))+'%)',
            self.api.d['count'], self.api.d['ok_count'], self.api.d['no_count'], str(self.api.d['no_lianxu']), self.api.d['dantui'],
            str(round(self.api.d['count'] / cha_day, 1))+' 次', str(self.api.d['shenglv'])+'%',
            N(self.api.d['ok_m'], 1), N(self.api.d['no_m'], 1), '', Color(self.api.d['m'], 1),
            str_of_num(self.api.d['volume']/cha_day) + ' | ' +  str_of_num(self.api.d['volume']),
             Color(N(self.api.d['bian_feilv'], 1)), Color(N(self.api.d['coinex_feilv'], 1)), 
             Color(N(self.api.d['bian_feilv']+self.api.d['coinex_feilv'], 1))
        ]],
    } )

    table.append( {
        "title" : "账户信息",
        "cols" : [
            '起始日期', '当前时间', '至今', '起始', '提现', '可用', '当前', '当日收益', '收益', '计算收益', '平均日化', '月化', '年化',
        ],
        "rows": [
            [
                GetTime(self.api.d['time'],'%m-%d %H:%M:%S'), GetTime(0,'%m-%d %H:%M:%S'), cha_str,
                STR_N(self.api.d['yuer']), STR_N(tixian/2)+' ('+StrBi(tixian / 2 / yuer)+')',
                STR_N(self.ws.keyong)+' ('+StrBi(self.ws.keyong / yuer)+')', STR_N(yuer),
                Color(str_bianhua2, bianhua2), Color(str_bianhua, bianhua), Color(N(self.api.d['bian_m'], 1)), '', '', '',
                
            ],
            [
                '', '', self.name+' -->', STR_N(self.api.d['coinex']),
                STR_N(tixian/2)+' ('+StrBi(tixian / 2 / yuer2)+')',
                STR_N(self.ws2.keyong)+' ('+StrBi(self.ws2.keyong / yuer2)+')', STR_N(yuer2), 
                Color(str_bianhua2_c, bianhua2_c), Color(str_bianhua_c, bianhua_c), Color(N(self.api.d['coinex_m'], 1)), '', '', '',
            ],
            [
                '', '', '总 -->',
                round(self.api.d['yuer']+self.api.d['coinex'], 2), 
                STR_N(tixian)+' ('+StrBi(tixian / (yuer+yuer2))+')',
                STR_N(self.ws.keyong + self.ws2.keyong)+' ('+StrBi((self.ws.keyong+self.ws2.keyong) / (yuer+yuer2))+')', yuerall, 
                    Color(str_bianhua_day, bianhuaall2), Color(str_bianhua_all, bianhuaall), '',
                day1, day30, day365
                
            ],
        ] })

    # table.append( {
    #     "title" : "禁止交易对",
    #     "cols" : [
    #         '禁止交易对'
    #     ],
    #     "rows": [
    #         [v for v in self.noSymbol]
    #     ] })
    
    tables = []
    for s in list(self.api.d['tongji'].keys()):
        RestDayTongji(self, s)
        data = self.api.d['tongji'][s]
        posBili = getSymbolBili(self, s)
        posBili2 = round(posBili / Kai_Bili, 1)
        tables.append([
            s, StrBi(posBili/100, v=0)+' / '+str(posBili2), 
            data['today']['count'], data['today']['ok_count'], data['today']['no_count'], str(data['today']['shenglv'])+'%', 
            N(data['today']['ok_m'], 1), N(data['today']['no_m'], 1), Color(data['today']['m']), str_of_num(data['today']['volume']),
            Color(N(data['today']['bian_feilv'], 1)), Color(N(data['today']['coinex_feilv'], 1)), 
            Color(N(data['today']['bian_feilv']+data['today']['coinex_feilv'], 1)),

            data['count'], data['ok_count'], data['no_count'], str(round(data['count'] / cha_day, 1))+' 次', str(data['shenglv'])+'%', 
            N(data['ok_m'], 1), N(data['no_m'], 1), Color(data['m']), str_of_num(data['volume']),
            Color(N(data['bian_feilv'], 1)), Color(N(data['coinex_feilv'], 1)),
            Color(N(data['bian_feilv']+data['coinex_feilv'], 1)),
        ])
    
    """排序"""
    tables.sort(key=lambda x: float(x[-5].split('#')[0]), reverse=True)
    
    table.append( {
        "title" : "交易对统计",
        "cols" : [
            '交易对',  '仓位',
            '今日总', "成功", "失败", '胜率', "盈利", "亏损", "总收益", '交易量',  '费1', '费2', '总费',
            '总', "成功", "失败", '日均', '胜率', "盈利", "亏损", "总收益", '交易量', '费1', '费2', '总费',
        ],
        "rows": tables
            })

    if self.api.fanyong_table:
        table.append(self.api.fanyong_table)

    logTable(table, self.api.d.copy())




"""
# import os
# import shutil
# dirp = '/root/pys/'
# # 按照修改时间排序
# sorted_files = sorted(os.listdir(dirp), key=lambda x: os.stat(dirp+x).st_mtime, reverse=True)
# for file in sorted_files:
#     path = os.path.join(dirp, file)
#     if '2023-08-30' in path:
        
#         for file2 in os.listdir(path):
#             path2 = os.path.join(path, file2)

#             if '.json' in path2:
#                 stats = os.stat(path2)
#                 stats2 = os.stat(self.api.json_path)
#                 log(path2, stats.st_size, '现在', stats2.st_size)
#                 if stats.st_size > 10000 and stats.st_size > stats2.st_size:
#                     log(path2, stats.st_size, '现在', stats2.st_size)
#                     shutil.copyfile(path2, self.api.json_path)
#                     uploadLog(isExit=1)
"""

