# -*- coding: utf-8 -*-
import os
import uuid
import platform
import time
import json
import shutil       #复制文件
import traceback
import math
import threading

from ws4py.client.threadedclient import WebSocketClient

from main.hanshu import *
from main.config import *
from exchangeV2.bian import bian

symbols_jiage = {}
order_status = {}


def Tongji(self, yingkui, sxf, jiazhi, symbol=0):

    if symbol:
        if symbol not in self.api.d['tongji']:
            self.api.d['tongji'][symbol] = self.api.d_start.copy()

        d = self.api.d['tongji'][symbol]
    else:
        d = self.api.d
    
    if yingkui > 0:
        d['ok_count'] += 1
        d['ok_m'] += yingkui
        d['today']['ok_count'] += 1
        d['today']['ok_m'] += yingkui
    
    if yingkui < 0:
        d['no_count'] += 1
        d['no_m'] += yingkui
        d['today']['no_count'] += 1
        d['today']['no_m'] += yingkui

    if not symbol:
        profit = N(d['nowYuer'] - d['yuer'] + d['huazhuan'] + yingkui+sxf,4)
        logProfit(profit)
    
    day = GetTime(0, "%m-%d")
    if d['today']['time'] != day:
        d['today'] = self.api.d_start['today'].copy()
        d['today']['time'] = day

    d['count'] = d['ok_count'] + d['no_count']
    d['volume'] += jiazhi
    d['today']['volume'] += jiazhi
    d['sxf'] += sxf
    d['today']['sxf'] += sxf

    #胜率统计
    d['count'] = d['ok_count'] + d['no_count']
    d['shenglv'] = N(d['ok_count'] / d['count'] * 100,2) if d['ok_count'] and d['count'] else 0
    d['m'] = N(d['ok_m'] + d['no_m'],2)

    #今日胜率统计
    d['today']['count'] = d['today']['ok_count'] + d['today']['no_count']
    d['today']['shenglv'] = N(d['today']['ok_count'] / d['today']['count'] * 100, 2) if d['today']['ok_count'] and d['today']['count'] else 0
    d['today']['m'] = N(d['today']['ok_m'] + d['today']['no_m'], 2)
    
    tongji_msg = '总'+str(d['count'])+" 成功"+str(d['ok_count'])+" 失败"+str(d['no_count'])+' 胜率'+str(d['shenglv'])+"%"
    if symbol:
        tongji_msg = symbol+'　'+tongji_msg

    tongji_msg += '　亏损'+str(N(d['no_m'],2))+' 盈利'+str(N(d['ok_m'],2))+' 收益'+str(d['m'])
    tongji_msg += '　交易量'+U(d['volume'])+' 手续费'+U(d['sxf'],2)
    log(tongji_msg)

    tongji_msg = '今日总'+str(d['today']['count'])+" 成功"+str(d['today']['ok_count'])+" 失败"+str(d['today']['no_count'])+' 胜率'+str(d['today']['shenglv'])+"%"
    if symbol:
        tongji_msg = symbol+'　'+tongji_msg
    tongji_msg += '　亏损'+str(N(d['today']['no_m'],2))+' 盈利'+str(N(d['today']['ok_m'],2))+' 收益'+str(d['today']['m'])
    tongji_msg += '　交易量'+U(d['today']['volume'])+' 手续费'+U(d['today']['sxf'],2)
    log(tongji_msg)

    self.api.SaveJson()


class WS_order_client(WebSocketClient):
    def opened(self):
        self.heartBeatHandler = Timer(10,self.xingtiao)
    
    def xingtiao(self):
        self.send("pong")

    def closed(self, code, reason=None):
        if hasattr(self,"heartBeatHandler"): self.heartBeatHandler.cancel()

    def received_message(self, data):
        global order_status
        data = json.loads(str(data))
        tlog("[订单监听] 心跳", data, 120 * 60) #2h

        if len(order_status) > 10000:
            for id in list(order_status.keys()):
                if "time" not in order_status[id].keys() or type(order_status[id]['time']) != int:
                    order_status = {}
                    break
                    
                if order_status[id] or t - order_status[id]['time'] > 4 * 60 * 60 * 1000:   #4h
                    del order_status[id]

        if 'e' in data.keys():
            if data['e'] == 'listenKeyExpired':
                tlog("[订单监听] Key到期", '', 15)
                self.close()

            elif data['e'] == 'ORDER_TRADE_UPDATE':

                data2 = data['o']
                symbol = data2['s']
                data2['rp'] = float(data2['rp'])
                data2['n'] = float(data2['n']) if 'n' in data2 else 0
                order_status[data2['i']] = {
                    'symbol': symbol,            #交易对
                    'status': data2['X'],            #状态
                    'jiage': float(data2['L']),      #价格
                    'liang': float(data2['z']),      #数量
                    'side': data2['S'],              #方向
                    'yingkui': data2['rp'],   #盈亏
                    'time': data['E'],              #推送时间
                }
                name = '开空' if data2['S'] == 'SELL' else '开多'
                if data2['R']:
                    name = '平多' if data2['S'] == 'SELL' else '平空'

                jiazhi = float(data2['z'])*float(data2['L'])
                if symbol[-4:] == CcyBi:
                    msg = [GetTime(echoMs=1), '[币安订单]', symbol, name, data2['o'], data2['f'], "　ID", data2['i'], data2['X'], '　方向', data2['S'],
                    '　价格', data2['p'],'　数量',data2['q'],
                    '　成交价格',data2['L'],'　成交数量', str(data2['z'])+'('+U(jiazhi)+')','　盈亏', data2['rp'],'　手续费', data2['n']]

                    if float(data2['z']) and data2['X'] == 'FILLED':
                        del msg[0]
                        log(*msg)

                        if hasattr(self,"api") and self.api:
                            sxf = float(data2['n'])
                            yingkui = float(data2['rp'])
                            Tongji(self, yingkui, sxf, jiazhi)
                            Tongji(self, yingkui, sxf, jiazhi, symbol)

                    else:
                        print(*msg)

                # else:
                #     print('[币安订单]', symbol, 'Error', CcyBi)


            elif data['e'] == 'ACCOUNT_UPDATE':
                for v in data['a']['B']:
                    if v['a'] == CcyBi:
                        print("[监听订单] 余额更新", v['wb'])



jiageTime1 = 0
jiageTime2 = 0
class WS_jiage_client(WebSocketClient):
    def received_message(self, data):
        global symbols_jiage, jiageTime1, jiageTime2
        try:
            data = json.loads(str(data))['data']
            #print(data, GetTime(data['E'], echoMs=1), GetTime(NowTime_ms(), echoMs=1))
            
            symbol = data['s']
            data['E'] = NowTime_ms()
            symbols_jiage[symbol] = data
            tlog('[价格监听] 心跳', [symbol, data['a']], 60 * 120)

        except:
            log('价格监听报错', data)

        if jiageTime1 != jiageTime2:
            tlog("[价格监听] 准备重连", [GetTime(jiageTime1), GetTime(jiageTime2)], 10)
            self.close(1001)
            exit(0) #强制退出，写代码报错也可以




class api():
    
    def __init__(self, bnb, symbols, isBnb=1):
        global Data_ID
        self.d_start = {
            'yuer': 0,          #起始余额
            'nowYuer': 0,       #实时余额
            'preYuer': 0,       #开仓前余额
            'maxYuer': 0,       #历史最大余额，用于计算回撤
            'time': 0,          #起始时间
            'huazhuan': 0,      #已划转资金
            'count': 0,         #次数
            'shenglv': 0,       #胜率
            'm': 0,             #盈利
            'bian_m': 0,             #盈利
            'coinex_m': 0,             #盈利
            'ok_count': 0,      #成功次数
            'ok_m': 0,          #盈利金额
            'no_count': 0,
            'no_m': 0,
            'volume': 0,
            'sxf': 0,
            'today': {  #今日统计
                'time': 0, 
                'yuer': 0.01,       #12点的余额
                'count': 0,         #次数
                'shenglv': 0,       #胜率
                'm': 0,             #盈利
                'ok_count': 0,      #成功次数
                'ok_m': 0,          #盈利金额
                'no_count': 0,
                'no_m': 0,
                'no_m2': 0,
                'volume': 0,
                'sxf': 0,
                'feilv': 0,
                'bian_feilv': 0,
                'coinex_feilv': 0,
            },
            'no_lianxu': 0,     #连续失败次数
            'pos': {},          #开仓后持仓信息存储 symbol: [pos, side、symbol、seting、zhisun、zhiying、sideID，time]
            'tongji': {},
            'feilv': 0,
            'bian_feilv': 0,
            'coinex_feilv': 0,
        }
        self.d = self.d_start.copy()
        self.d_one = 0
        
        
        if platform.system().lower() == 'windows':
            self.json_dir = 'C:\\data\\'
        elif platform.system().lower() == 'linux':
            self.json_dir = '/root/data/'
        
        name2 = ServerName.replace('套利_', '套利').replace('套利2_', '套利').replace('套利V2_', '套利')
        fenge = name2.split('_')
        if len(fenge) >= 3 and fenge[1] in ['1', '2', '3']:
            del fenge[1]
            name = '_'.join(fenge)
        else:
            name = name2

        """ 重置：此处+1，然后下面的d_one 设置为1"""
        if '球球' in ServerName:
            Data_ID += 5

        if '梧桐' in ServerName:
            Data_ID += 1

        if '骨刀' in ServerName:
            Data_ID += 1

        if '克时' in ServerName:
            Data_ID += 1

        if 'clm' in ServerName and 'Bitget' in ServerName:
            Data_ID += 1

        jsonName = name+'_'+Api_Key+'_'+str(Data_ID)+'.json'
        self.json_name = jsonName
        self.json_path = self.json_dir+jsonName
        self.json_path_bak = os.path.dirname(os.path.abspath(__file__+"/../"))+"/"+jsonName
        self.json_lock = threading.Lock()

        print(self.json_path)

        if 1==1: #'球球' not in ServerName
            self.SetJson()
        else:
            self.SaveJson()
            self.d_one = 1
        
        #备份Json
        shutil.copyfile(self.json_path, self.json_path_bak) #备份Json文件
        
        if not bnb:
            return
        
        self.bnb = bnb
        if isBnb:
            self.feilv = self.bnb.GetFeilv()    #获取手续费，wait显示
            #设置持仓
            self.bnb.SetChicang(True)
        else:
            self.feilv = '-'
            
        self.symbol = symbols #小数位
        self.posAll = []      #价格监听用
        self.runTime = NowTime()
        self.chuci = 0

        #首次运行前统计下余额
        if not self.d['yuer']:      #初始化起始余额
            self.d['yuer'] = self.bnb.GetYuer()['all']
            self.chuci = 1
            if not self.d['yuer']:
                self.d['yuer'] = 0.01   #防止除法报错
            self.d['nowYuer'] = self.d['yuer']
            self.d['time'] = NowTime()
            self.SaveJson()

        else:
            self.d['nowYuer'] = self.bnb.GetYuer()['all']
            if not self.d['nowYuer']:
                self.d['nowYuer'] = 0.01
            
        if not glo.preYuer:
            glo.preYuer = self.d['nowYuer']

        if not self.d['maxYuer']:
            self.d['maxYuer'] = self.d['nowYuer']

        #12-8 日update：一天最多亏损限制，重启后重新计数

        try:
            if 'today' in self.d and ('no_m2' not in self.d['today'] or self.d['today']['no_m2'] <= LianxuStop):
                self.d['today']['no_m2'] = 0
        except:
            pass
            
        self.t = 0              #循环耗时
        self.preYuer = self.d['nowYuer']        #3-7日 16:33 20ms 查询持仓，然后100ms更新余额
        self.keyong = self.d['nowYuer']
        self.d['preYuer'] = self.preYuer            #开仓前余额
        self.jiqun = ServerName.split("_")[0] == 'jiqun'
        self.fanyong = 0        #返佣余额
        self.fanyong_table = 0

        if isBnb:
            if not self.jiqun:
                """监听订单并且定时重启"""
                t = threading.Thread(target=self.WS_order)
                t.setDaemon(True)
                t.start()

            """定时更新余额"""
            t = threading.Thread(target=self.upYuer)
            t.setDaemon(True)
            t.start()

            """返佣线程"""
            try:
                if ApiKeyFanYong:
                    self.fanyong_bnb = bian("BTCUSDT", ApiKeyFanYong)
                    t = threading.Thread(target=self.FanYong)
                    t.setDaemon(True)
                    t.start()

            except:
                pass
        
    
    """ 给外部调用"""
    def toUpYuer(self):
        nowYuer = self.bnb.GetYuer(0)
        self.keyong = nowYuer['keyong']
        nowYuer = nowYuer['all']+nowYuer['yingkui']
        self.preYuer = nowYuer


    """定时更新余额线程"""
    def upYuer(self):
        while True:
            try:
                self.toUpYuer()
                #1s更新一次
                time.sleep(2)

            except Exception as e:
                tlog("报错", traceback.format_exc(), 15)
                traceback.print_exc()


    """风控操作"""
    def FengKong(self, posAll):
        guaTable =  {
            "title" : "挂单信息",
            "cols" : [
                '交易对', '方向', '指标止盈 (差)', '固定止盈 (差)', '指标止损 (差)', '固定 止损 (差)', '量',
                 "当前价", '开仓时间', '当前时间', '平仓时间', '周期', '操作',
            ],
        }
        guaTable2 = []


        #-------云端更新起始余额
        if glo.Yuer and glo.Yuer != self.d['yuer']:
            cha = self.d['yuer'] - glo.Yuer
            log('修改起始余额', '当前', self.d['yuer'], '修改后', glo.Yuer, "差", cha)
            self.d['yuer'] = glo.Yuer
            self.d['maxYuer'] = self.preYuer
            self.d['today']['yuer'] = self.preYuer
            self.SaveJson()
        
        #-------云端停止起始余额
        if glo.Stop:
            guaTable2.append([Color("策略已暂停", -1) for i in range(len(guaTable['cols']))])
            tlog('策略已暂停', '禁止开仓...', 60*10)

        #-------云端上传成交历史
        if glo.trades and glo.trades != glo.preTrades:
            glo.preTrades = glo.trades
            t = threading.Thread(target=self.bnb.GetUserTrades)
            t.setDaemon(True)
            t.start()

        #-------云端划转
        if glo.Huazhuan != glo.preHuazhuan and self.bnb.Huazhuan(glo.Huazhuan):
            self.d['huazhuan'] += glo.Huazhuan
            self.d['maxYuer'] -= glo.Huazhuan
            self.d['today']['yuer'] -= glo.Huazhuan
            glo.preHuazhuan = glo.Huazhuan
            self.SaveJson()

        #-------云端充值
        if glo.Huazhuan2 != glo.preHuazhuan2 and tlog("划转", '', 10, xs=0) and self.bnb.Huazhuan(glo.Huazhuan2, 'MAIN_UMFUTURE'):
            if self.d['huazhuan'] >= glo.Huazhuan2:
                self.d['huazhuan'] -= glo.Huazhuan2
            else:
                self.d['yuer'] += glo.Huazhuan2
                
            self.d['maxYuer'] += glo.Huazhuan2
            self.d['today']['yuer'] += glo.Huazhuan2
            glo.preHuazhuan2 = glo.Huazhuan2
            self.SaveJson()

        #-------云端平仓
        if glo.Pingcang:
            log('云端平仓')
            t = []
            posAll2 = []
            for pos in posAll:
                if glo.Pingcang == '1' or glo.Pingcang.lower() == pos['symbol'].lower():
                    t.append( threading.Thread(target=self.Pingcang, args=(pos, '云端平仓', )) )
                else:
                    posAll2.append(pos)

            for i in t:
                i.setDaemon(True)
                i.start()

            for i in t:
                i.join()

            glo.Pingcang = 0
            posAll = posAll2
        

        nowYuer = self.preYuer
        glo.nowYuer = nowYuer
        if not posAll and not self.d['pos']:
            self.d['preYuer'] = nowYuer

        #对比差
        for pos in posAll:
            if pos['symbol'] not in self.d['pos'].keys():
                guaTable2.append([pos['symbol']] + [Color("异常开仓", -1) for i in range(len(guaTable['cols'])-2) ]+ ['<span onclick="symbol(\''+pos['symbol']+'\')">切换</a>'])
                #tlog(Color(pos['symbol']+" 异常开仓", -1), [], 120)
        

        #-----风控强停，先获取余额，必须放在下面
        if IsStop(nowYuer, self.d['maxYuer']):
            guaTable2.append([Color("账户强停", -1) for i in range(len(guaTable['cols']))])
            stop = GetStop(self.d['maxYuer'])
            tlog(Color('账户强停平仓', -1), [nowYuer, stop], 60)
            for pos in posAll:
                self.Pingcang(pos, msg='账户强停 '+STR_N(nowYuer, 2)+'/'+STR_N(stop, 2))


        huizong = [[0, 0], [0, 0], [0, 0], [0, 0]]
        for tmpPos in list(self.d['pos']):
            try:
                tmpPos = self.d['pos'][tmpPos]  #先一步平仓删除
            except:
                continue

            if self.jiqun or NowTime()-self.runTime <= 10: #5-21 防止一重启就把仓位给全部删除
                continue

            side, symbol, kTime = tmpPos['pos']['side'], tmpPos['symbol'], tmpPos['seting']['kTime']
            pos = GetPos(symbol, posAll)
            gaibian = 0

            if pos:
                side2 = pos['side']
                sideMsg = '▲做多#32CD32' if pos['side'] == 'BUY' else '▼做空#FF4500'    #方向

                table = [symbol, sideMsg, 0, 0, 0, 0, 0, pos['nowJiage'],
                         tmpPos['time'], GetTime(), tmpPos['endTime'], tmpPos['seting']['kTime'],
                        '<span onclick="symbol(\''+symbol+'\')">切换</a>']

                if side != side2:
                    log(symbol, "仓位方向改变", "原", side, "现", side2, Color('', -1))
                    #4-11日 禁用
                    #gaibian = tmpPos

                else:

                    for z in tmpPos['zhiying']:
                        cha = z['Jia']-pos['nowJiage']
                        bi1 = cha / pos['jiage']
                        bi2 = (z['Jia']-pos['jiage']) / pos['jiage']
                        t = str(z['Jia'])+' ('+ STR_N(cha, WeiShu(z['Jia'])) +') ('+StrBi(bi1)+') ('+StrBi(bi2)+')'
                        if not table[2]:
                            table[2] = t
                            huizong[0][0] += abs(bi1)        #汇总
                            huizong[0][1] += abs(bi2)
                        elif not table[3]:
                            table[3] = t
                            huizong[1][0] += abs(bi1)
                            huizong[1][1] += abs(bi2)

                        if not table[6]:
                            table[6] = z['liang']
                        

                        #止盈挂单不撤销
                        if (side2 == 'BUY' and pos['nowJiage'] >= z['Jia']) or (side2 == 'SELL' and pos['nowJiage'] <= z['Jia']):
                            log(Color('风控止盈', -1), side2, symbol, '条件', z['Jia'], '当前', pos['roe'], '当前价', pos['nowJiage'])
                            t = threading.Thread(target=self.Pingcang, args=(pos, str(z['Jia'])+' '+kTime+' 风控止盈', ))
                            t.setDaemon(True)
                            t.start()

                    for z in tmpPos['zhisun']:
                        cha = z['Jia']-pos['nowJiage']
                        bi1 = cha / pos['jiage']
                        bi2 = (z['Jia']-pos['jiage']) / pos['jiage']
                        t = str(z['Jia'])+' ('+ STR_N(cha, WeiShu(z['Jia'])) +') ('+StrBi(bi1)+') ('+StrBi(bi2)+')'
                        if not table[4]:
                            table[4] = t
                            huizong[2][0] += abs(bi1)
                            huizong[2][1] += abs(bi2)
                        elif not table[5]:
                            table[5] = t
                            huizong[3][0] += abs(bi1)
                            huizong[3][1] += abs(bi2)

                        if not table[6]:
                            table[6] = z['liang']

                        if (side2 == 'BUY' and pos['nowJiage'] <= z['Jia']) or (side2 == 'SELL' and pos['nowJiage'] >= z['Jia']):
                            log(Color('风控止损', -1), side2, symbol, '条件', z['Jia'], '当前', pos['roe'], '当前价', pos['nowJiage'])
                            t = threading.Thread(target=self.Pingcang, args=(pos, str(z['Jia'])+' '+kTime+' 风控止损', ))
                            t.setDaemon(True)
                            t.start()

                guaTable2.append(table)

            #5-12 多线程开仓，这里会直接给异常开仓了，加个1秒判断
            if 'time2' not in tmpPos:
                tmpPos['time2'] = NowTime()

            if NowTime()-tmpPos['time2']>5 and ( not pos or gaibian ):
                okOrder = 0
                msg2 = side+' '+symbol
                xs = tlog(side+" "+symbol+" 交易", '', 20, xs=0, look=1)
                if xs:
                    log(msg2, Color('仓位异常被平', 1))

                for z in tmpPos['zhiying']:
                    order = self.bnb.GetOrder(z['ID'], symbol)
                    log(tmpPos['symbol'], '止盈ID', z['ID'], "止盈价", z['Jia'], order)
                    if order and (order['status'] == 'FILLED' or order['status'] == 'PARTIALLY_FILLED'):
                        order['yingkui'] = 1
                        okOrder = order
                        break
                
                if not okOrder:
                    for z in tmpPos['zhisun']:
                        if z['ID'] == 8888:
                            continue
                        order = self.bnb.GetOrder(z['ID'], symbol)
                        log(tmpPos['symbol'], '止损ID', z['ID'], "止损价", z['Jia'], order)
                        if order and (order['status'] == 'FILLED' or order['status'] == 'PARTIALLY_FILLED'):
                            order['yingkui'] = -1
                            okOrder = order
                            break
                
                pingJia = 0
                msg = tmpPos['seting']['kTime']+" "
                if okOrder:
                    pingJia = okOrder['jiage']
                    msg = str(okOrder['cJiage'])+" "+msg
                    msg += '止盈挂单成交' if okOrder['yingkui'] > 0 else '止损挂单成交'
                    log(msg2, msg, okOrder, Color('', 1 if okOrder['yingkui'] > 0 else -1))

                #平仓线程是否运行中
                elif xs: 
                    pingJia = self.GetNowJiage(symbol)
                    msg += '仓位异常被平'
                    log(msg2, msg, "当前价格", pingJia, Color('', -1))

                if pingJia:
                    self.Pingcang_Tongji(0, pingJia, tmpPos['pos'], msg=msg)
                    self.bnb.DeleteAllOrder(tmpPos['symbol'])

            if gaibian:
                gaibian = {'sideId': gaibian['sideId'], 'seting': gaibian['seting'], 'endTime': gaibian['endTime']}
                self.Kaicang_Tongji(pos, "仓位方向改变", gaibian)
        
        if guaTable2:
            if len(guaTable2) > 1:
                guaTable2.append(['', '汇总：',
                                   (StrBi(huizong[0][0])+' ('+StrBi(huizong[0][1])+')' if huizong[0][0]!=0 else 0),
                                   (StrBi(huizong[1][0])+' ('+StrBi(huizong[1][1])+')' if huizong[1][0]!=0 else 0),
                                   (StrBi(huizong[2][0])+' ('+StrBi(huizong[2][1])+')' if huizong[2][0]!=0 else 0),
                                   (StrBi(huizong[3][0])+' ('+StrBi(huizong[3][1])+')'  if huizong[3][0]!=0 else 0),
                                    '', '', '', '', '', '', ''])
            guaTable['rows'] = guaTable2

        else:
            guaTable = 0

        return nowYuer, guaTable



    """ 自动从返佣账号转账到当前账号"""
    def FanYong(self):
        log('[线程] ***********买BNB线程启动...')
        # self.fanyong_bnb.SetZhanNei()

        while 1:
            try:
                if 'fanyong' not in self.d or 'pre' not in self.d['fanyong']:
                    self.d['fanyong'] = {
                        'time': 0.00,        #今日
                        'dayleiji': 0.00,   #今日返佣
                        'leiji': 0.00,      #累积返佣
                        'wait': 0.00,       #待从现货划转到合约的余额
                        'pre': 0.00,        #上次余额
                    }
                
                day = GetTime(0, "%m-%d")
                if self.d['fanyong']['time'] != day:
                    self.d['fanyong']['time'] = day
                    self.d['fanyong']['dayleiji'] = 0.00

                #从合约划转到现货
                yuer = 0#float(self.fanyong_bnb.GetYuer(p=0, bi=SxfBi)['availableBalance'])
                yuerZi = float(self.bnb.GetYuer(p=0, bi=SxfBi)['keyong'])
                if yuer > self.d['fanyong']['pre']:
                    self.d['fanyong']['dayleiji'] += yuer - self.d['fanyong']['pre']
                    self.d['fanyong']['leiji'] += yuer - self.d['fanyong']['pre']

                elif yuer < self.d['fanyong']['pre']:
                    self.d['fanyong']['dayleiji'] += yuer
                    self.d['fanyong']['leiji'] += yuer

                self.d['fanyong']['pre'] = yuer
                

                #现货提现到子账号
                xianhuo = 0#self.fanyong_bnb.GetXianhuo(SxfBi)
                
                if SxfBi != 'BNB':
                    xianhuo = int(xianhuo * 1000000) / 1000000
                    jiage = 1

                else:
                    jiage = self.bnb.GetNowJiage('BNBUSDT')
                    jiage = max(100, jiage)

                #买BNB
                buy = 0

                fenge = ServerName.split('_')
                gobuy = 0 if len(fenge) >= 3 and fenge[1] in ['2', '3'] else 1

                if SxfBi == 'BNB' and yuerZi * jiage < 10 and self.keyong > 60 and gobuy:
                    usdt = float(self.bnb.GetXianhuo(CcyBi))
                    self.bnb.Huazhuan(round(30.1 - usdt, 6))
                    log('买bnb', self.bnb.go("POST", '/api/v3/order', {"symbol": "BNBUSDT", 'side': "BUY", 'type': "MARKET" , 'quoteOrderQty': 30}, url='https://api.binance.com'))
                    time.sleep(2)
                    buy = 1

                yuerZiXianhuo = float(self.bnb.GetXianhuo("BNB"))
                if yuerZiXianhuo > 0:
                    self.bnb.Huazhuan(yuerZiXianhuo, 'MAIN_UMFUTURE', 'BNB') 
                    yuerZi += yuerZiXianhuo
                    
                usdt = float(self.bnb.GetXianhuo(CcyBi))
                if usdt > 0 and usdt < 1:
                    self.bnb.Huazhuan(usdt, 'MAIN_UMFUTURE') 


                # if (SxfBi != 'BNB' and yuer > 2) or (SxfBi == 'BNB' and yuer*jiage > 2):
                #     log("父账号准备划转")
                #     self.fanyong_bnb.Huazhuan(yuer, bi=SxfBi)

                    
                # if (SxfBi != 'BNB' and xianhuo > 10): # or (SxfBi == 'BNB' and xianhuo*jiage > 10)
                    
                #     post = {
                #         'coin': SxfBi,   #U本位合约钱包转向现货钱包
                #         'address': Address,
                #         'network': AddressNetwork,
                #         'amount': xianhuo,
                #         'transactionFeeFlag': True, #当站内转账时免手续费, true: 手续费归资金转入方; false: 手续费归资金转出方; . 默认 false.
                #     }
                #     log("父账号现货余额", str(xianhuo)+' ('+STR_N(xianhuo*jiage)+'$)', "准备转账", post)
                #     data = self.fanyong_bnb.go("POST","/sapi/v1/capital/withdraw/apply", post, 'https://api.binance.com')
                #     if data and 'id' in data:
                #         log(Color('转账成功', 1), data)
                #         self.d['fanyong']['wait'] += xianhuo
                #     else:
                #         log(Color('转账失败', -1), data)
                
                #子账号现货划转到合约
                xianhuo2 = float(self.bnb.GetXianhuo(SxfBi))
                if xianhuo2 > 0:
                    huazhuan = xianhuo2 if xianhuo2 <= self.d['fanyong']['wait'] else self.d['fanyong']['wait']
                    if huazhuan > 0:
                        log("子账号准备划转")
                        if self.bnb.Huazhuan(huazhuan, 'MAIN_UMFUTURE'):
                            self.d['fanyong']['wait'] = 0

                if SxfBi == 'BNB':
                    self.fanyong = yuerZi*jiage
                    if self.chuci:
                        if not buy:
                            self.d['yuer'] -= self.fanyong
                            self.SaveJson()
                        self.chuci = 0

                self.fanyong_table =  {
                    "title" : "返佣信息",
                    "cols" : [
                        '更新时间', SxfBi+' (30%)', '父账号合约余额', '父账号现货余额', '子账号现货余额', '子账号待划转', '今日返佣', '累积返佣',
                    ],
                    'rows': [[
                        GetTime(0,'%m-%d %H:%M:%S'), STR_N(yuerZi, 4)+' ('+STR_N(yuerZi*jiage)+'$)', 
                        STR_N(yuer, 4)+' ('+STR_N(yuer*jiage)+'$)'+' ('+StrBi(yuer*jiage / self.d['nowYuer'])+')',
                        STR_N(xianhuo, 4)+' ('+STR_N(xianhuo*jiage)+'$)'+' ('+StrBi(xianhuo*jiage / self.d['nowYuer'])+')',
                        STR_N(xianhuo2, 4)+' ('+STR_N(xianhuo2*jiage)+'$)'+' ('+StrBi(xianhuo2*jiage / self.d['nowYuer'])+')',
                        STR_N(self.d['fanyong']['wait'], 4)+' ('+STR_N(self.d['fanyong']['wait']*jiage)+'$)'+' ('+StrBi(self.d['fanyong']['wait']*jiage / self.d['nowYuer'])+')',
                        STR_N(self.d['fanyong']['dayleiji'], 4)+' ('+STR_N(self.d['fanyong']['dayleiji']*jiage)+'$)'+' ('+StrBi(self.d['fanyong']['dayleiji']*jiage / self.d['nowYuer'])+')',
                        STR_N(self.d['fanyong']['leiji'], 4)+' ('+STR_N(self.d['fanyong']['leiji']*jiage)+'$)'+' ('+StrBi(self.d['fanyong']['leiji']*jiage / self.d['nowYuer'])+')',
                    ]],
                }

                time.sleep(60)

            except Exception as e:
                uploadError(traceback.format_exc())
                time.sleep(30)



    """更新Table"""
    def wait(self, tmpPosAll=0, datas=0, sleepTime=6, table2=0):
        table = []
        posAll = []
        posAll2 = [] #更新后的持仓信息
        tablePos = []
        posTj = {'bzj': 0, 'jiazhi': 0, 'yingkui': 0, 'roe': 0, 'diefu': 0,}

        #筛选哪些仓位需要显示
        # for pos in tmpPosAll:
        #     if pos:
        #         #其他集群只需要管好自己的仓位
        #         if len(ServerName.split("_")) > 2 and int(ServerName.split("_")[2]) > 1 and\
        #              pos['symbol'] not in self.symbol.keys():
        #              continue

        #         posAll.append(pos)


        # self.posAll = posAll    #价格监听用
        # i = 1
        # tmpYuer = glo.preYuer * len(JiQun) if self.jiqun else glo.preYuer
        # for pos in posAll:
        #     nowJiage = self.GetNowJiage(pos['symbol'])
            
        #     pos['nowJiage'] = nowJiage
        #     pos['yingkui'], pos['roe'] = GetRoe(nowJiage, pos)

        #     posAll2.append(pos)
            
        #     msg = '▲做多#32CD32' if pos['side'] == "BUY" else '▼做空#FF4500'    #方向
        #     jiage = pos['jiage']       #开仓价
        #     j_xs = self.symbol[pos['symbol']]['J'] if pos['symbol'] in self.symbol else WeiShu(pos['nowJiage'])
        #     jiazhi = abs(pos['liang'] * jiage)
        #     diefu = pos['yingkui'] / jiazhi * 100
            
        #     pos['roe'] = round(pos['yingkui'] / tmpYuer * 100, 2)
        #     posTj['bzj'] += pos['bzj']
        #     posTj['jiazhi'] += jiazhi
        #     posTj['yingkui'] += pos['yingkui']
        #     posTj['roe'] += pos['roe']
        #     posTj['diefu'] += diefu

        #     tablePos.append([
        #         str(i)+'.'+pos['symbol'], msg, jiage, nowJiage, STR_N(nowJiage-jiage, j_xs), N(pos['qiangJiage'], j_xs),
        #         pos['liang'], pos['leverage'], STR_N(pos['bzj'],2)+' '+CcyBi+' ('+StrBi(pos['bzj'] / tmpYuer)+')',
        #         STR_N(jiazhi,2)+' '+CcyBi+' ('+StrBi(jiazhi / tmpYuer)+')',
        #         Color(pos['yingkui']), Color(pos['roe'],None,'%'), Color(N(diefu, 2),None,'%'), 
        #         '<span onclick="symbol(\''+pos['symbol']+'\')">切换</a>',
        #     ])
        #     i += 1

        # if tablePos:
        #     if len(tablePos) > 1:
        #         tablePos.append(['','','','','','','',
        #          "汇总：",  STR_N(posTj['bzj'],2)+' '+CcyBi+' ('+StrBi(posTj['bzj'] / tmpYuer)+')',
        #          STR_N(posTj['jiazhi'],2)+' '+CcyBi+' ('+StrBi(posTj['jiazhi'] / tmpYuer)+')',
        #          Color(N(posTj['yingkui'], 4)), Color(N(posTj['roe'], 2),None,'%'), Color(N(posTj['diefu'], 2),None,'%'), ''])

        #     table.append( {
        #         "title" : "持仓状态",
        #         "cols" : [
        #             '交易对','方向',"开仓价","当前价","价差",'强平价', "持仓量","杠杆", "保证金", "持仓价值", "浮动盈亏", "回报率", "涨跌幅", "操作"
        #         ],
        #         "rows" : tablePos,
        #     } )

        # yuer, guaTable = self.FengKong(posAll2)
        yuer = self.preYuer
        yuer = 0.01 if not yuer else yuer
        # if guaTable:
        #     table.append(guaTable)

        self.d['nowYuer'] = yuer    #给Web端的
        day = GetTime(0, "%m-%d")
        if self.d['today']['time'] != day:
            
            self.d['today'] = {  #重置今日统计
                'time': day, 
                'yuer': self.d['nowYuer'],       #12点的余额
                'count': 0,         #次数
                'shenglv': 0,       #胜率
                'm': 0,             #盈利
                'ok_count': 0,      #成功次数
                'ok_m': 0,          #盈利金额
                'no_count': 0,
                'no_m': 0,
                'volume': 0,
                'sxf': 0,
            }

        cha_day, cha_str = GetChaTime(self.d['time'])  #策略运行时长
        zong_yuer = N(yuer + self.d['huazhuan'], 2)    #总余额
        tixian = glo.Huazhuan if glo.Huazhuan != glo.preHuazhuan else 0
        bianhua = N(zong_yuer - self.d['yuer'], 2)     #已提现变化
        bianhua2 = N(yuer - self.d['yuer'], 2)         #未提现变化
        bianhua3 = N(self.d['nowYuer'] - self.d['today']['yuer'], 2)         #当日变化
        

        str_bianhua = str(bianhua)+' ('+StrBi(bianhua / self.d['yuer'])+')'
        str_bianhua2 = str(bianhua2)+' ('+StrBi(bianhua2 / self.d['yuer'])+')'
        str_bianhua3 = str(bianhua3)+' ('+StrBi(bianhua3 / self.d['today']['yuer'] if self.d['today']['yuer'] else 0)+')'

        day1 = N(bianhua / cha_day, 2)           #日化
        day1 = str(day1)+' ('+StrBi(day1 / self.d['yuer'])+')'

        day30 = N(bianhua / cha_day * 30, 2)     #月化
        day30 = StrBi(day30 / self.d['yuer'])

        day365 = N(bianhua / cha_day * 365, 2)   #年化
        day365 = StrBi(day365 / self.d['yuer'])

        stop = GetStop(self.d['maxYuer'])
        table.append( {
            "title" : "风控统计",
            "cols" : [
                 "运行时间", "杠杆", '账户', '计费币 (手续费)', "巅峰余额 (收益%) (回撤%) / 回撤强停 (收益%) (回撤%)",
                '总', "成功", "失败", "连续失败", '日均', '胜率', "亏损", "盈利", "收益", '手续费', '利润',
                '交易量', 
                '延迟', '循环',
            ],
            "rows" : [[
                '', '', '', '', '今日：', 
                self.d['today']['count'], self.d['today']['ok_count'], self.d['today']['no_count'],
                 '','', str(self.d['today']['shenglv'])+'%',
                N(self.d['today']['no_m'], 2), N(self.d['today']['ok_m'], 2), Color(self.d['today']['m']), 
                U(self.d['today']['sxf']), Color(N(self.d['today']['m']-self.d['today']['sxf'], 1)), U(self.d['today']['volume']), '', '',
            ],[
                GetChaTime(self.runTime)[1], glo.ganggan,
                ServerName.replace('sb_', '')+' ('+Api_Key+')', CcyBi+' ('+self.feilv+')',
                STR_N(self.d['maxYuer'], 2)+' ('+StrBi((self.d['maxYuer']+self.d['huazhuan']-self.d['yuer'])/self.d['yuer'])+') ('+StrBi((yuer-self.d['maxYuer'])/(self.d['maxYuer']+self.d['huazhuan']))+') / '+str(stop)+' ('+StrBi((stop+self.d['huazhuan']-self.d['maxYuer'])/self.d['yuer'])+') ('+str(int((1-QiangPing)*-100))+'%)',
                self.d['count'], self.d['ok_count'], self.d['no_count'], self.d['no_lianxu'],
                str(round(self.d['count'] / cha_day, 1))+' 次', str(self.d['shenglv'])+'%',
                N(self.d['no_m'], 2), N(self.d['ok_m'], 2), Color(self.d['m']),
                U(self.d['sxf']), Color(N(self.d['m']-self.d['sxf'], 1)), U(self.d['volume']),
                sleepTime, str(NowTime_ms()-self.t)+'ms',
            ]],
        } )

        member = []
        try:
            if TouZi:
                for v in TouZi:
                    bili = v['usdt'] / self.d['yuer']
                    member.append(
                        [
                            '', '', v['name']+' -->', STR_N(v['usdt'], 2)+' ('+StrBi(bili)+')',
                            STR_N(glo.preYuer * bili, 2)+' ('+StrBi((glo.preYuer - self.d['yuer']) / yuer)+')',
                            STR_N(yuer*bili, 2)+' ('+StrBi((yuer-glo.preYuer) / yuer)+')',
                            N(self.d['huazhuan'] * bili, 2), N(tixian * bili),
                            N(zong_yuer*bili, 2), Color(JieQu(str_bianhua3, bili), bianhua3),
                            Color(JieQu(str_bianhua2, bili), bianhua2), Color(JieQu(str_bianhua, bili), bianhua),
                            JieQu(day1, bili), '', ''
                        ]
                    )
        except:
            pass
        
        member.append([
                GetTime(self.d['time'],'%m-%d %H:%M:%S'), GetTime(0,'%m-%d %H:%M:%S'), cha_str,
                STR_N(self.d['yuer'], 2),
                STR_N(self.keyong, 2)+' ('+StrBi(self.keyong / yuer)+')',
                STR_N(yuer, 2),
                N(self.d['huazhuan'], 2), tixian,
                zong_yuer, Color(str_bianhua3, bianhua3), Color(str_bianhua2, bianhua2), Color(str_bianhua, bianhua),
                day1, day30, day365
        ])
        table.append( {
            "title" : "账户信息",
            "cols" : [
                '起始日期', '当前时间', '至今',
                '起始余额', '可用余额', '当前余额', '已提现', '待提现',
                '总余额', '当日收益', '未提现', '总收益', '平均日化', '月化', '年化',
            ],
            "rows": member })
        

        if self.fanyong_table:
            table.append(self.fanyong_table)


        if table2:
            table.append(table2)

        if datas:
            table_ce = []
            j = 1
            for data in datas:
                seting = data['seting']
                #print(j, seting['symbol'], seting['kTime'])
                side = data['side']
                J = self.symbol[seting['symbol']]['J']
                nowJiage = self.GetNowJiage(seting['symbol'])
                if side != '-' and data['limit']: #计算差距
                    cha = data['limit'] - nowJiage
                    chaBi = cha / data['limit']
                    chaBi = StrBi(chaBi if side == 'BUY' else chaBi*-1)+' ('+STR_N(cha if side == 'BUY' else cha*-1, J)+')'
                else:
                    chaBi = '-'

                kaiLiang = GetKaiLiang(self.d['nowYuer'], nowJiage, self.symbol[seting['symbol']]['L'], seting)

                table_ce.append([
                        str(j)+'.'+seting['symbol'], seting['kTime'],
                        Color(bool(seting['buy']),1 if seting['buy'] else -1), str(seting['buyZhiying'])+'%', str(seting['buyZhisun'])+'%',
                        Color(bool(seting['sell']),1 if seting['sell'] else -1), str(seting['sellZhiying'])+'%', str(seting['sellZhisun'])+'%',
                        side, data['limit'] if data['seting']['name'] == 'Sb' else data['close'], chaBi,
                        str(J)+ ','+ str(self.symbol[seting['symbol']]['L']), kaiLiang,
                        str(round(kaiLiang*nowJiage,2))+' ('+StrBi(kaiLiang*nowJiage / self.d['nowYuer'])+')',
                        #str(data['zhiying'])+' ('+StrBi((data['zhiying']-nowJiage)/nowJiage if data['zhiying'] else 0)+')',
                        #str(data['zhisun'])+' ('+StrBi((data['zhisun']-nowJiage)/nowJiage if data['zhisun'] else 0)+')',
                        nowJiage, data['time'], '<span onclick="symbol(\''+seting['symbol']+'\')">切换</a>',
                ])
                j += 1
            
            table.append( {
                "title" : "策略信息",
                "cols" : [
                    '交易对', '周期', '做多', "多止盈", "多止损", '做空', '空止盈', '空止损',
                    '开仓信号',  '挂单价', "差距", '价量', #, '平仓信号'
                    '开仓数量', '开仓价值', #'指标止盈', '指标止损',
                    '当前价格', '信号时间', '操作'
                ],
                "rows" : table_ce,
                } )
        
        
        if self.d['tongji']:
            tables = []
            for s in list(self.d['tongji'].keys()):
                data = self.d['tongji'][s]
                tables.append([
                    s, data['today']['count'], data['today']['ok_count'], data['today']['no_count'], str(data['today']['shenglv'])+'%', 
                    N(data['today']['ok_m'], 1), N(data['today']['no_m'], 1), Color(data['today']['m']), U(data['today']['sxf']), Color(N(data['today']['m']-data['today']['sxf'])), str_of_num(data['today']['volume']),

                    data['count'], data['ok_count'], data['no_count'], str(round(data['count'] / cha_day, 1))+' 次', str(data['shenglv'])+'%', 
                    N(data['ok_m'], 1), N(data['no_m'], 1), Color(data['m']), U(data['sxf']), Color(N(data['m']-data['sxf'])), str_of_num(data['volume'])

                            ])
            
            """排序"""
            tables.sort(key=lambda x: float(x[-2].split('#')[0]), reverse=True)
                
            table.append( {
                "title" : "交易对统计",
                "cols" : [
                    '交易对', '今日总', "成功", "失败", '胜率', "盈利", "亏损", "收益", '手续费', '利润', '交易量',
                     '总', "成功", "失败", '日均', '胜率', "盈利", "亏损", "收益", '手续费', '利润', '交易量',
                ],
                "rows": tables
                    })

        logTable(table, self.d.copy())
        

        # if NowTime_ms()-self.t > 200:
        #     uploadError("机器人循环耗时异常 "+str(NowTime_ms()-self.t)+"ms")
        
        # if not sleepTime:
        #     sleep_time = glo.ServerCount*10 if glo.ServerCount else 1000       #循环等待时间
        # else:
        #     sleep_time = sleepTime
        Sleep(sleepTime)



    """ 查询订单信息，备用Res"""
    def GetOrder(self, orderId, symbol=0, data=0):
        global order_status

        status = False
        order_liang = 0
        order_jia = 0

        if orderId in order_status.keys():
            if data:
                return order_status[orderId]

            status = order_status[orderId]['status']
            order_liang = order_status[orderId]['liang']
            order_jia = order_status[orderId]['jiage']

        else:
            order = self.bnb.GetOrder(orderId, symbol)
            if data:
                return order

            if order:
                status = order['status']
                order_liang = order['liang']
                order_jia = order['jiage']
                order_status[orderId] = order
            else:
                status = 'False'


        return status, order_liang, order_jia


    """市价平仓"""
    def Pingcang(self, pos, msg=''):
        if not pos:
            return 0

        #标记平仓，防止一个线程多重运行，并且防止平仓后被标记异常平仓
        if not tlog(pos['side']+" "+pos['symbol']+" 交易", msg, 20):
            return 0

        symbol = pos['symbol']
        side = 'SELL' if pos['side'] == 'BUY' else 'BUY'  #平仓方向
        posSide  = 'long' if pos['side'] == 'BUY' else 'short'
        liang = pos['liang']
        kaishi  = NowTime_ms()   #循环延迟起始时间
        try:
            xs = self.symbol[symbol]['L']   #交易对数量小数位
        except:
            xs = 2

        maxLiang = self.symbol[symbol]['M'] if symbol in self.symbol else 99999999   #最大市价单数量

        liangOk = 0      #求平均成交价格
        preJiage = 0

        for i2 in range(3):
            jiage = self.GetNowJiage(symbol)

            if liang <= 0:
                if self.bnb.GetPos(symbol, pos['side']):
                    log(posSide, symbol, Color('平仓异常1', -1), '已经全部下单 但是持仓')
                    time.sleep(0.2)
                    continue
                else:
                    return self.Pingcang_Tongji(liangOk, jiage, pos, msg, kaishi)
        
            if liang > maxLiang:
                log(posSide, symbol, Color('平仓异常', -1), '市价单过大', liang, '>', maxLiang)

            orderId = self.bnb.PostOrder(side, jiage, min(liang, maxLiang), jiancang=1, symbol=symbol, posSide=posSide)
            order_time = NowTime_ms()

            if not orderId:
                if self.bnb.GetPos(symbol, pos['side']):
                    log(posSide, symbol, Color('平仓异常2', -1), '已经全部下单 但是持仓')
                    time.sleep(0.2)
                    continue
                else:
                    return self.Pingcang_Tongji(0, preJiage, pos, msg, kaishi)

            while 1:
                now_time = NowTime_ms()
                waitTime = 5000
                status, order_liang, order_jia = self.GetOrder(orderId, symbol=symbol)

                if status == 'FILLED':
                    log('全部成交', order_liang, '成交价', order_jia)
                    liang -= order_liang
                    liangOk += order_liang*order_jia
                    break

                if status == 'PARTIALLY_FILLED':
                    log('部分成交', order_liang, '成交价', order_jia, '总需要', liang)
                    liang -= order_liang
                    liang = round(liang, xs)
                    liangOk += order_liang*order_jia
                    break

                if (status and status != 'NEW') or now_time - order_time > waitTime:
                    log("撤单", status)
                    break
                
                time.sleep(0.01)
            
            self.bnb.DeleteAllOrder(symbol=symbol)
            preJiage = order_jia if order_jia else jiage
        
        if not self.bnb.GetPos(symbol, pos['side']):
            return self.Pingcang_Tongji(liangOk, jiage, pos, msg, kaishi)
        else:
            log(posSide, symbol, Color('平仓失败！', -1))
            return 0


    """撤销订单，并且判断是否撤销成功"""
    def DeleteOrders(self, symbol, ids):
        delete = 1
        if ids:
            for order_id in ids:
                fh = self.bnb.DeleteOrder(symbol, order_id) #先撤销挂单 防止挂单占用资金
                if "code" in fh:
                    delete = 0
                    log(symbol, "撤销订单失败 不开仓", Color(fh, -1))
                else:
                    delete = fh

        return delete


    """平仓统计"""
    def Pingcang_Tongji(self, liangOkAll, jiage, pos, msg, kaishi=0):
        if kaishi:
            msg = str(msg)+" 耗时 "+str(NowTime_ms()-kaishi)+'ms'

        if not liangOkAll and not jiage:
            log('平仓数据异常')
            return 0
        
        side, liang = pos['side'], pos['liang']
        if liangOkAll:  #成交均价
            jiage = R(liangOkAll / abs(liang), self.symbol[pos['symbol']]['J'] if pos['symbol'] in self.symbol else 2)
        
        yingkui, roe = GetRoe(jiage, pos)

        #统计信息
        if yingkui >= 0:
            self.d['no_lianxu'] = 0
            self.d['ok_count'] += 1
            self.d['ok_m'] += yingkui
            self.d['today']['ok_count'] += 1
            self.d['today']['ok_m'] += yingkui
        else:
            self.d['no_lianxu'] += 1
            self.d['no_count'] += 1
            self.d['no_m'] += yingkui
            self.d['today']['no_count'] += 1
            self.d['today']['no_m'] += yingkui

        #胜率统计
        self.d['count'] = self.d['ok_count'] + self.d['no_count']
        self.d['shenglv'] = N(self.d['ok_count'] / self.d['count'] * 100,2) if self.d['ok_count'] and self.d['count'] else 0
        self.d['m'] = N(self.d['ok_m'] + self.d['no_m'],2)

        #今日胜率统计
        self.d['today']['count'] = self.d['today']['ok_count'] + self.d['today']['no_count']
        self.d['today']['shenglv'] = N(self.d['today']['ok_count'] / self.d['today']['count'] * 100, 2) if self.d['today']['ok_count'] and self.d['today']['count'] else 0
        self.d['today']['m'] = N(self.d['today']['ok_m'] + self.d['today']['no_m'], 2)
        
        tongji_msg = '总'+str(self.d['count'])+" 成功"+str(self.d['ok_count'])+" 失败"+str(self.d['no_count'])+" 连续失败"+str(self.d['no_lianxu'])+' 胜率'+str(self.d['shenglv'])+"%"
        tongji_msg += '　亏损'+str(N(self.d['no_m'],2))+' 盈利'+str(N(self.d['ok_m'],2))+' 收益'+str(self.d['m'])
        log(tongji_msg)
        log(
            pos['side'], pos['symbol'], '平仓完成', '　开仓价', pos['jiage'], '　平仓价', jiage,
            '　持仓', liang, '　保证金', pos['bzj'], '　盈亏', yingkui, '　回报率'+str(roe)+"%",
             '　涨跌幅'+str(N(roe/glo.ganggan, 2))+"%", msg, Color('', roe),
        )

        profit = N(glo.preYuer - self.d['yuer'] + self.d['huazhuan'] + yingkui,4)

        logPos({'type': '卖出' if side == "BUY" else '买入', 'name': '平多' if side == "BUY" else '平空',
                'bzj': pos['bzj'], 'liang': liang, 'kaiJia': pos['jiage'], 'yuer': glo.preYuer, 'yuer2': self.d['yuer'],
                'pingJia': jiage, 'profit': yingkui, 'symbol': pos['symbol'], 'server_name': ServerName,
                'zong_profit': profit, 'zong_profit_bi': round(profit / self.d['yuer'] * 100,2) if self.d['yuer'] > 0 else 0,
                'msg': msg,
                })

        #self.d['preYuer'] += yingkui

        #统计最大余额
        if glo.preYuer > self.d['maxYuer']:
            self.d['maxYuer'] = glo.preYuer

        #盈利画图
        #logProfit(profit)

        self.d['pos'] = DelPos(pos['symbol'], self.d['pos'])
        
        self.SaveJson()  #保存Json数据

        return pos['symbol']




    """开仓统计"""
    def Kaicang_Tongji(self, pos, msg, data=0, kaishi=0, nomsg=0):
        if kaishi:
            msg = str(msg)+" 耗时 "+str(NowTime_ms()-kaishi)+'ms'

        side, symbol = pos['side'], pos['symbol']
        if not nomsg:
            log(pos['side'], pos['symbol']+'　'+'开仓成功'+'　价格', pos['jiage'], '　数量',pos['liang'], msg, Color('', 1))

            zong_profit = N(self.d['nowYuer'] - self.d['yuer'] + self.d['huazhuan'],4)

            logPos({'type': '买入' if side=='BUY' else '卖出', 'name': '开多' if side=='BUY' else '开空',
                    'bzj': pos['bzj'], 'liang': pos['liang'], 'kaiJia': pos['jiage'], 'pingJia': '', 'profit': '',
                    'symbol': symbol, 'yuer': glo.preYuer, 'server_name': ServerName,
                    'zong_profit': zong_profit, 'zong_profit_bi': round(zong_profit / self.d['yuer'] * 100,2), 'msg': msg,
            })

        if data:

            posSave = {
                'side': side,
                'pos': pos,
                'symbol': symbol,
                'sideId': data['sideId'],
                'seting': data['seting'],
                'endTime': data['endTime'],
                'zhiying': [],
                'zhisun': [],
                'time': GetTime(0, "%H:%M:%S"),
                'time2': NowTime(),
            }
            if 'endTime2' in data:
                posSave['endTime2'] = data['endTime2']

            self.d['pos'][symbol] = posSave
            
            self.SaveJson()  #保存Json数据

        return pos


    #-----------------以下为万年不改
    """ 挂单"""
    def Orders(self, pos, data):
        try:
            if not pos:
                return

            side, symbol = pos['side'], pos['symbol']

            #3-6日 12:28：经常出现多线程重复挂单
            if not tlog(side+symbol+'止损止盈', 0, 30, xs=0):
                return

            if not tlog(side+" "+symbol+" 交易", '', 20, xs=0, look=1):
                return tlog(side+" "+symbol+" 交易线程运行中", Color("禁止挂单", -1), 20)

            if symbol in self.d['pos'].keys():
                
                liang = pos['liang']
                pingSide = 'SELL' if pos['side'] == 'BUY' else 'BUY'  #平仓方向
                posSide  = 'long' if pos['side'] == 'BUY' else 'short'

                seting, zhiying, zhisun = data['seting'], data['zhiying'], data['zhisun']
                
                zhisunSet = seting['buyZhisun'] if side == 'BUY' else seting['sellZhisun']
                zhiyingSet = seting['buyZhiying'] if side == 'BUY' else seting['sellZhiying']

                zhiying = [zhiying, GetRoeJia(zhiyingSet, pos, self.symbol[symbol]['J'])]
                zhisun = [zhisun, GetRoeJia(-zhisunSet, pos, self.symbol[symbol]['J'])]

                #撤销之前的挂单，更新
                update = 0
                Yuan = [v['Jia'] for v in self.d['pos'][symbol]['zhiying']+self.d['pos'][symbol]['zhisun']]
                YuanL = [v['liang'] if 'liang' in v.keys() else 0 for v in self.d['pos'][symbol]['zhiying']+self.d['pos'][symbol]['zhisun']]
                Now = zhiying+zhisun

                #挂单量更新
                if YuanL and liang not in YuanL:
                    update = 1

                #挂单价格更新
                else:
                    for v in Now:
                        if v and v not in Yuan:
                            update = 1
                            break

                if update == 0:
                    return
                
                if len(self.d['pos'][symbol]['zhiying'] + self.d['pos'][symbol]['zhisun']) and data['noUpdate']:
                    return log(side, symbol, '策略已标记不更新挂单', "当前", Now, "原", Yuan, Color('', -1))

                if Yuan:
                    self.bnb.DeleteAllOrder(symbol)
                    log(side, symbol, "更新挂单")
                else:
                    log(side, symbol, "初次挂单")

                okzhiying = []
                okzhisun = []

                #6-11 风控的价格是标记价格，会导致异常挂单止损
                nowJiage = self.GetNowJiage(symbol)

                #10-11日 8:20：止损和止盈的挂单用市价会有滑点，统一限价
                for z in zhiying:
                    if z:
                        if (side == 'BUY' and nowJiage >= z) or (side == 'SELL' and nowJiage <= z):
                            log(Color('挂单风控止盈', -1), pos['symbol'], side, '条件', z, '当前价', nowJiage)
                            return self.Pingcang(pos, msg='挂单风控止盈')

                        #3-17 8:53，极速行情导致止盈失败，亏损2%
                        #3-18 9:02，每笔的滑点远远超过2%
                        zId = self.bnb.PostOrder(pingSide, z, liang, type='TAKE_PROFIT', stopPrice=z, jiancang=1, timeInForce="GTC", symbol=symbol, posSide=posSide)  #TAKE_PROFIT_MARKET
                        if not zId:
                            log(side, symbol, Color("止盈下单失败", -1))

                        okzhiying.append({'ID': zId if zId else 8888, 'Jia': z, 'liang': liang})

                for z in zhisun:
                    if z:
                        kaiPosSide = 'short' if side == 'BUY' else 'long'
                        if (side == 'BUY' and nowJiage <= z) or (side == 'SELL' and nowJiage >= z):
                            log(Color('挂单风控止损', -1), pos['symbol'], side, '条件', z, '当前价', nowJiage)
                            self.bnb.PostOrder(pingSide, z, liang, type='MARKET', timeInForce="GTC", symbol=symbol, posSide=kaiPosSide)
                            return self.Pingcang(pos, msg='挂单风控止损')

                        #开反仓
                        #self.bnb.PostOrder(pingSide, z, liang, type='STOP_MARKET', stopPrice=z, timeInForce="GTC", symbol=symbol, posSide= kaiPosSide)

                        if data['seting']['name'] == 'Sb':
                            zId = 8888
                        else:
                            zId = self.bnb.PostOrder(pingSide, z, liang, type='STOP_MARKET', stopPrice=z, jiancang=1, timeInForce="GTC", symbol=symbol, posSide=posSide) #STOP STOP_MARKET
                            
                        if zId:
                            okzhisun.append({'ID': zId, 'Jia': z, 'liang': liang})
                        else:
                            log(side, symbol, Color("止损下单失败", -1))

                log(side, symbol, "止盈", okzhiying, "止损", okzhisun)

                if okzhiying or okzhisun:
                    #if symbol in self.d['pos']:    #3-4日 16:44 ATOM Sell止盈挂单成交后，这里继续挂单了，艹
                    self.d['pos'][symbol]['zhiying'] = okzhiying
                    self.d['pos'][symbol]['zhisun'] = okzhisun

        except Exception as e:
            log("报错", side, symbol, traceback.format_exc(), Color('', -1))
            traceback.print_exc()
            #3-1 18:50 报错会遗留开仓挂单
            self.bnb.DeleteAllOrder(symbol)


    """保存data到本地"""
    def SaveJson(self, qiangzhi=0):
        if not self.json_path:
            return False

        #3s最多保存一次
        if not qiangzhi and not tlog('Json保存', '', 3, xs=0):
            return False

        if not os.path.exists(self.json_dir):
            os.makedirs(self.json_dir)
        
        try:
            if self.json_lock.acquire(blocking=False):
                tmpFile = self.json_path+f'.{uuid.uuid4()}.tmp'
                with open(tmpFile,'w') as f:
                    json.dump(self.d, f)
                    shutil.move(tmpFile, self.json_path)
                    
                    if qiangzhi:
                        log(Color("保存Json成功", -1))

                # 释放锁
                self.json_lock.release()


        except Exception as e:
            uploadError(traceback.format_exc())
            log(Color("保存Json失败", -1))


    """读取本地文件数据"""
    def SetJson(self):
        if not self.json_path:
            return False

        if not os.path.exists(self.json_path):
            return self.SaveJson()


        with open(self.json_path, "r") as f:
            try:
                datas = json.load(f)
                f.close()

            except Exception as e:
                return False

            #log(datas)
            for k in datas.keys():
                self.d[k] = datas[k]

    
    """启动监听订单变化"""
    def WS_order(self):
        global order_status
        while True:
            try:
                listen_key = self.bnb.GetlistenKey()
                tlog('[监听订单] 连接...', listen_key, 120)

                #订阅账户信息
                ws = WS_order_client('wss://fstream.binance.com/ws/'+listen_key)
                if '多因子' in ServerName or '韭菜' in ServerName or 'mad' in ServerName:
                    ws.api = self

                ws.connect()
                ws.run_forever()

            except Exception as e:
                order_status = {}
                tlog("报错", traceback.format_exc(), 15)
                traceback.print_exc()


    """订阅交易对最新价格"""
    def WS_jiage(self):
        global jiageTime1, jiageTime2

        while True:
            try:

                url = "wss://fstream.binance.com/stream?streams="
                symbols = []
                for symbol in self.symbol:
                    symbols.append(symbol.lower())

                for pos in self.posAll:
                    symbols.append(pos['symbol'].lower())
                
                jiageTime2 = jiageTime1
                fenge = math.ceil(len(symbols) / 5) if len(symbols) > 5 else 30
                symbols2 = arr_size(symbols, fenge)
                ts = []
                tlog('[价格监听'+str(len(symbols2))+"]  共 "+str(round(fenge,1))+'/'+str(len(symbols))+" 个", '', 15)
                for symbols22 in symbols2:
                    if not symbols22:
                        continue
                    
                    urls = ''
                    for symbol in symbols22:
                        urls += symbol+"@aggTrade/"
                    urls = urls.strip('/')
                    
                    ws = WS_jiage_client(url+urls)
                    ws.connect()
                    t = threading.Thread(target=ws.run_forever)
                    t.setDaemon(True)
                    t.start()
                    ts.append(t)

                for t in ts:
                    t.join()

            except Exception as e:
                tlog("报错", traceback.format_exc(), 15)
                traceback.print_exc()
        

    """获取最新价格，并且加上超时检查"""
    def GetNowJiage(self, symbol):
        global symbols_jiage, jiageTime1, jiageTime2

        jiage = 0
        if symbol in symbols_jiage.keys():
            data = symbols_jiage[symbol]
            if NowTime_ms() - data['E'] < 60000:    #10秒
                jiage = float(data['p'])
            else:
                tlog('[价格监听] 数据超时', [symbol, GetTime(echoMs=1), GetTime(data['E'], echoMs=1)], 30)
                jiageTime1 = NowTime()

        else:
            tlog(Color('[价格监听] 未找到此交易对', -1), symbol, 120)
            jiageTime1 = NowTime()

        okjiage = jiage if jiage else self.bnb.GetNowJiage(symbol)

        if not jiage:
            symbols_jiage[symbol] = {
                'E': NowTime_ms(),
                'p': okjiage,
            }
        return okjiage