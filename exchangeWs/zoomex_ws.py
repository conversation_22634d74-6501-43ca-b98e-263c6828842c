#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback
import gzip

from main.config import *
from main.hanshu import *
from pprint import pprint

from moneyV2.money import changePos

class zoomex_ws():
    def __init__(self, gate=None, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.userToken = gate.userToken
        # self.access_id = gate.access_id
        # self.secret_key = gate.secret_key
        # self.passwd = gate.passwd

        self.order_status = ['Cancelled', 'Filled']
        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新订单"""
    def _update_order(self, data):

        try:
            for data2 in data:
                id = data2['orderId']
                side = data2['side'].upper()
                close = '减仓' if data2['reduceOnly'] else '开仓'

                jiage = float(data2['lastExecPrice'])
                liang = float(data2['cumQty'])

                jiazhi = max(round(float(data2['lastExecPrice']) * float(data2['cumQty']), 2), 0.0001)
                self.orders[id] = {
                    'id': id,                  #交易对
                    'symbol': data2['symbol'],                  #交易对
                    'status': data2['orderStatus'],                  #状态
                    'side': side,                               #方向
                    'type': data2['orderType'],                     #订单类型
                    'jiage': jiage,             #累积成交均价
                    'liang': liang,         #累积成交量
                    'yingkui': 0,             #盈亏
                    'jiazhi': jiazhi,  #价值
                    'sxf': 0,  #手续费
                    'time': int(data2['updatedAtE3']),                #推送时间
                }

                # pprint(data)
                pprint(self.orders[id])
                # print("------------------")
                
                msg = ['[Zoomex监听订单]', GetTime(echoMs=1), '', side, close, data2['symbol'], data2['orderType'], data2['tpOrderType'],
                "　ID", id, data2['orderStatus'],
                '　价格', data2['price'], '数量', str(data2['qty']) + ' ('+U(float(data2['price'])*float(data2['qty']))+')',
                '　成交价', jiage, '成交量', str(liang) + ' ('+U(jiage*liang)+')']

                if liang:
                    log(*msg)
                    if data2['orderStatus'] in self.order_status:
                        changePos(self, data2['symbol'], side, jiage, liang)
                else:
                    print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())

    """ 更新盘口和费率信息"""
    def _update_data(self, v):

        data = v['data']
        # print(data)
        s = data['s']
        if s not in self.data:
            if 'b1' in data['index'] and 'a1' in data['index']:
                self.data[s] = {'bidQty': 0, 'askQty': 0, 'feilv': 0}
            else:
                return

        if 'b1' not in data['index'] and 'a1' not in data['index']:
            return

        if 'b1' in data['index']:
            self.data[s]['bidPx'] = float(data['index']['b1'])
        if 'a1' in data['index']:
            self.data[s]['askPx'] = float(data['index']['a1'])

        self.data[s]['t'] = int(v['ts'] / 1000)

        if self.depathCallback:
            self.depathCallback(s, self.data[s])


    async def ping(self, conn):
        while True:
            msg = {"op":"ping","args":[NowTime_ms()]}
            await conn.send_str(ujson.dumps(msg))
            await asyncio.sleep(10)


    async def main(self, symbols=[]):
        while True:
            # log("Bitget现货 准备连接ws", symbols)
            try:
                connector = aiohttp.TCPConnector(limit=50, keepalive_timeout=120, ssl=False)
                async with aiohttp.ClientSession(connector=connector) as session:
                    async with session.ws_connect(
                            "wss://ws2.zoomex.com/realtime_private_9_10?v=2&timestamp=" + str(NowTime_ms()) if not symbols else "wss://ws2.zoomex.com/realtime_loc_w?timestamp="+str(NowTime_ms()),
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                    ) as conn:
                    
                        if symbols:
                            for symbol in symbols:
                                symbol = symbol.replace('_UMCBL', '')
                                x = {"op":"subscribe","args":[f"index_quote_20.H.{symbol}.1"]}
                                await conn.send_str(ujson.dumps(x))
                                
                        else:
                            # 在这里添加
                            login_msg = {"op": "login", "args": [
                                self.userToken]}
                            subscribe_msg = {"op": "subscribe", "args": ["private.order"]}

                            await conn.send_str(ujson.dumps(login_msg))
                            await conn.send_str(ujson.dumps(subscribe_msg))

                        asyncio.create_task(self.ping(conn))
                        while True:
                            # 接受消息
                            try:
                                msg = await conn.receive(timeout=30) #
                            except asyncio.TimeoutError:
                                log(Color('zoomex ws长时间没有收到消息 准备重连...', -1))
                                break
                            except:
                                print(traceback.format_exc())
                                log(Color('zoomex ws出现错误 准备重连...', -1))
                                time.sleep(3)
                                break

                            msg = msg.data
                            # print("msg:%s"%msg)
                            try:
                                msg = gzip.decompress(msg).decode('utf-8')
                            except:
                                pass

                            try:
                                msg = ujson.loads(msg)
                                # pprint(msg)
                            except:
                                print('解析失败: '+str(msg))
                                break



                            if 'topic' in msg :
                                if 'index_quote_20' in msg['topic']: self._update_data(msg)
                                elif 'private.order' in msg['topic']: self._update_order(msg['data'])
                                else: pprint(msg)

                                # if not symbols:
                                #     pprint(msg)

                await session.close()  # Close session
            except Exception as e:
                log(f'zoomex ws连接失败 开始重连...', traceback.format_exc())

                #强制重启
                os._exit(0)


if __name__ == "__main__":

    a = zoomex('TEST')
    
    asyncio.run(zoomex_ws(a).main(['OPUSDT']))
    print("websocket demo")