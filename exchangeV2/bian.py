from main.config import *

import hmac
import hashlib
try:
    import rusthttp_py.adapter as requests
except:
    input('不支持Rust的Request')
import requests
import time
import json
import traceback        #输出报错
from urllib.parse import parse_qs, urlencode
import netifaces as ni
from main.hanshu import *

class bian():
    
    def __init__(self, apiKey=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['pIuA71b1oqwA6Gb3xWjBLvCVHaExHpeI2HQgzEyjC5MWjWdVSIC8lTs4rt3uL0qn', 'e8WuLsvjVWAVpexjphs4uKW5mDTqu6iGWM7FDzMKazf7U3DYrz4claRQidxTaNOj'],
        }

        self.url =  "https://fapi.binance.com"

        self.ws_url = "wss://fstream.binance.com/ws/"
        self.ApiName = apiKey
        apiKey = apiKey if apiKey else Api_Key
        self.ccyBi = ccyBi if ccyBi else CcyBi
        
        if apiKey in keys:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        else:
            apiKey = GetApiKey(apiKey, 'Bian')

        self.ApiKey = apiKey[0]
        self.SecretKEY = apiKey[1]
        
        self.klines_dir = '/root/klines/'
        self.debug = 0
        self.spot = 0
        self.bnb_jiazhi = 0
        self.chicangModel = True
        self.debugs = []

        """ 优先使用缓存数据"""
        self.delData = {}
        self.session = requests.Session()

        """ 缓存费率数据"""
        self.feilvData = {}
        self.setsData = {}

        """ 多出口IP"""
        self.duoips = []
        # print('检测服务器网络配置')
        for dev in ni.interfaces():
            if 'ens' in dev or 'eth' in dev or 'enp' in dev:
                if len(ni.ifaddresses(dev)) > 2:
                    # print(ni.ifaddresses(dev))
                    for i in ni.ifaddresses(dev)[2]:
                        ip=i['addr']
                        if ip not in self.duoips:
                            self.duoips.append(ip)

        self.i = 0
        self.ips = []
        if len(self.duoips) > 1:
            tlog('币安 使用多IP获取行情', self.duoips, 10)
            for ip in self.duoips:
                from requests_toolbelt.adapters import source  #指定出口IP
                sb = requests.Session()
                new_source = source.SourceAddressAdapter(ip)
                sb.mount('http://', new_source)
                sb.mount('https://', new_source)
                self.ips.append(sb)

        # else:
        #     log('Error: 币安 没有多IP', ips)

        if '套利' in ServerName:
            self.SetChicang(True)

    
        
    def hashing(self,query_string):
        return hmac.new(self.SecretKEY.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()

    def get_timestamp(self):
        return int(time.time() * 1000)

    def dispatch_request(self, http_method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(http_method, 'GET')


    # 请求API
    def go(self, http_method, url_path, payload={}, url=0, needKey=1, spot=0, httpData=0):
        headers = {
            'Content-Type': 'application/json;charset=utf-8',
            'X-MBX-APIKEY': self.ApiKey
        }
        if spot and not url:
            url = 'https://api.binance.com'

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= len(self.ips):
                self.i = 0

        query_string = urlencode(payload)
        query_string = query_string.replace('%27', '%22')

        url2 = url
        if not url2:
            url2 = self.url

        if needKey:
            if query_string:
                query_string = "{}&timestamp={}".format(query_string, self.get_timestamp())
            else:
                query_string = 'timestamp={}'.format(self.get_timestamp())
            url2 += url_path + '?' + query_string + '&signature=' + self.hashing(query_string)
        else:
            url2 += url_path + '?' + query_string

        # print(url2)
        if self.debug:
            t = NowTime_ms()

        params = {'url': url2, 'params': {}, 'timeout': 5}
        self.session.headers.update(headers)

        if httpData:
            return {
                "method": http_method,
                "url": url2,
                "headers": headers,
                "data": None
            }

        try:
            response = self.dispatch_request(http_method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败',url2)
            time.sleep(1)
            return self.go(http_method, url_path, payload, url, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(url_path, payload, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2


    """ 获取所有交易对价格、挂单盘口"""
    def GetTickerAll(self):

        data = self.go("GET","/fapi/v1/ticker/bookTicker", {}, needKey=0)

        if not data or type(data) != list:
            print('获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        if self.ips:
            self.session = self.ips[0]

        return data


    """ 获取所有交易对资金费率"""
    def GetSymbolFeilv(self, symbol=0):
        if not self.feilvData or not symbol:
            data = self.go("GET","/fapi/v1/premiumIndex", {})
        
            if not data or type(data) != list:
                print('获取资金失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            if not symbol:
                return data

            for v in data:
                self.feilvData[v['symbol']] = v

        try:
            symbolData = self.feilvData[symbol]
        except:
            symbolData = 0

        if not symbolData or NowTime_ms() >= symbolData['nextFundingTime']+500:      #太长了会错误的资金费率
            log(symbol, 'BNB更新资金费率',
                 '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
                 )
                 
            time.sleep(0.5)
            self.feilvData = {}
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['lastFundingRate'])*100, 4)

    """最大杠杆"""
    def GetMaxGangGan(self, symbol):
        
        symbolGG = self.GetGanggan()
        gg = 0
        for v in symbolGG:
            try:
                if v['symbol'] == symbol:
                    gg = v['brackets'][0]['initialLeverage']
            except:
                pprint(symbolGG)

        if not gg:
            log(symbol, '未找到此交易对的杠杆规则')
        log(f'Bian {symbol} 最大杠杠', gg)
        return int(gg)

    # 获取手续费率
    def GetFeilv(self):
        symbol = 'BTCUSDT' if self.ccyBi == 'USDT' else 'BTCBUSD'
        data = self.go("GET","/fapi/v1/commissionRate", {'symbol': symbol})
        try:
            ok = StrBi(float(data['takerCommissionRate']), 3)
            log("当前币安账户手续费", StrBi(float(data['makerCommissionRate']), 3)+"/"+StrBi(float(data['takerCommissionRate']), 3))
        except:
            log("获取手续费失败", data)
            time.sleep(60)
            return self.GetFeilv()
            
        return ok
        

    """开启/关闭站内转账"""
    def SetZhanNei(self, status=True):
        if status:
            fh = self.go("POST","/sapi/v1/account/enableFastWithdrawSwitch", url='https://api.binance.com')
            log("开启站内转账", fh)
        else:
            fh = self.go("POST","/sapi/v1/account/disableFastWithdrawSwitch", url='https://api.binance.com')
            log("关闭站内转账", fh)
        return fh
    
    

    """撤销所有订单"""
    def DeleteAllOrder(self, symbol, msg='', httpData=0):
        fh = self.go("DELETE","/fapi/v1/allOpenOrders",{'symbol': symbol}, httpData=httpData)
        if httpData:
            return fh

        log(symbol, "撤销所有挂单", msg)
        
        return fh


    """撤销指定订单"""
    def DeleteOrder(self, symbol, orderId):
        t = NowTime_ms()
        
        if not orderId:
            return 0

        name = f"{symbol} {orderId}"
        if name in self.delData.keys():
            log(symbol, "撤销挂单", orderId, Color('已经撤销过了！缓存结果:', -1), self.delData[name])
            data = self.delData[name].copy()
            if len(self.delData) > 10000:
                self.delData = {}
            return data

        
        data = self.go("DELETE","/fapi/v1/order",{'symbol': symbol, 'orderId': orderId})
        fh = data['status'] if 'status' in data else '失败'
        log(symbol, "撤销挂单", orderId, "结果", fh, str(NowTime_ms()-t)+'ms')
        self.delData[name] = data
        return data


    """批量撤销订单"""
    def DeleteOrders(self, orderIds, symbol):
        if not orderIds:
            return 0

        fh = 0
        for i in range(0,int(len(orderIds))+1,10):
            c=orderIds[i:i+10]
            if c:
                c = [id for id in c]
                c = json.dumps(c).replace(' ', '')
                log(symbol, "批量撤销挂单", c)
                fh = self.go("DELETE","/fapi/v1/batchOrders",{'symbol': symbol, 'orderIdList': c})

        return fh

    """ 修改订单"""
    def EditOrder(self, side, symbol, jiage, liang, orderId, httpData=0):
        data = {
            'side': side,
            'symbol': symbol,
            'orderId': orderId,
            'quantity': liang,
            'price': jiage,
        }
        t = NowTime_ms()
        fh = self.go('PUT', '/fapi/v1/order', data, httpData=httpData)
        if httpData:
            return fh

        data['newOrderId'] = fh['orderId'] if 'orderId' in fh else 0
        log(self.ApiName, side, symbol, '修改订单 ->', '价格', jiage, '量',
             liang, U(liang*jiage), '返回', fh, NowTime_ms()-t, 'ms')
        return data


    """下单
    有效方式 (timeInForce):
    GTC - Good Till Cancel 成交为止
    IOC - Immediate or Cancel 无法立即成交(吃单)的部分就撤销
    FOK - Fill or Kill 无法全部立即成交就撤销
    GTX - Good Till Crossing 无法成为挂单方就撤销
    """
    def PostOrder(self, symbol, side, jiage, liang, type='normal', jiancang=0, msg2=''):
        
        x = X(symbol)
        if x == 1000:
            if symbol in self.setsData:
                J, L = self.setsData[symbol]['J'], self.setsData[symbol]['L']
                print(symbol, J, L)

            else:
                J = WeiShu(jiage) * 1000        #可能会有问题
                L = WeiShu(liang) / 1000
                log(symbol, '未找到规则', J, L)

            jiage = N(float(jiage) * 1000, J)
            liang = N(float(liang) / 1000, L)

        jiage = str(jiage)
        liang = str(liang)
        jiazhi = float(liang)*float(jiage)
        liang2 = liang+U(jiazhi, kuo=1)

        t = NowTime_ms()

        post = {
            'symbol': symbol,  #交易对
            'side': side,       #方向
            'quantity': liang, #数量
        }

        #双向持仓，持仓方向
        if self.chicangModel:
            if jiancang:
                post['positionSide'] = 'short' if side == 'BUY' else 'long'
            else:
                post['positionSide'] = 'long' if side == 'BUY' else 'short'
        # else:
        #     post['reduceOnly'] = True


        if jiage == '0' or type == 'normal' or (jiancang and jiazhi < 5.1):
            post['type'] = 'MARKET'

        elif type in ['stop', 'take_profit']:
            post['stopPrice'] = jiage
            post['price'] = jiage
            post['type'] = type.upper()
            post['timeInForce'] = 'GTC'

        else:
            post['type'] = 'LIMIT'
            post['price'] = jiage
            if type == 'limit':
                post['timeInForce'] = 'GTC'
            if type == 'post_only':
                post['timeInForce'] = 'GTX'
            else:
                post['timeInForce'] = type.upper()

        
        msg = "币安　"+symbol+'　'+post['type']+'　'
        
        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　减仓:"+str(jiancang)+"　Type:"+type
        if self.chicangModel:
            msg += "　posSide:"+post['positionSide']


        if (not jiancang or not float(liang)) and jiage != '0' and jiazhi < 5.1:
            log(msg, Color(msg2, -1), '开仓下单价值太小，跳过')
            return 0, '跳过'



        orderId = 0
        yc = 0
        for x in range(1):
            order = self.go("POST","/fapi/v1/order", post)
            yc = NowTime_ms()-t

            msg = [msg, Color(msg2, 1), str(yc)+'ms']

            if not 'orderId' in order.keys():

                log(side, symbol, Color('套利币安下单失败', -1), post, order, msg2)
                if '多因子' in ServerName or '韭菜' in ServerName:
                    return 0, msg
                
                # if -1001 != order['code']:
                #     uploadError(symbol+' 币安币安下单失败：'+str(post)+'  '+str(order))

                # if -2027 == order['code']:
                    # self.SetGangGan(symbol, SetGangGan)
                    # uploadLog(isExit=1)

            else:
                orderId = order['orderId']
                break

        return orderId, msg



    """批量下单"""
    def PostOrders(self, orderS):
        if not orderS:
            return log("批量下单 空的", orderS)

        i = 1
        okorderS = []
        for post in orderS:
            post['price'], post['quantity'] = str(post['price']), str(abs(post['quantity']))

            msg = str(i)+"."+post['symbol']+'　'+post['type']+'　'

            if 'reduceOnly' not in post.keys():
                post['reduceOnly'] = False
                
            if post['type'] == 'MARKET':
                post['timeInForce'] = 'Market'

            posSide = post['positionSide'] if 'positionSide' in post else 0
            if post['reduceOnly'] or (posSide == 'long' and post['side'] == 'SELL') or (posSide == 'short' and post['side'] == 'BUY'):
                msg += '平空仓' if post['side'] == 'BUY' else '平多仓'
            else:
                msg += '开多仓' if post['side'] == 'BUY' else '开空仓'

            msg += "　方向:"+post['side']+"　价格:"+post['price']+"　量:"+post['quantity']
            msg += "　减仓:"+str(post['reduceOnly'])+"　Maker:"+post['timeInForce']

            if 'stopPrice' in post.keys():
                post['stopPrice'] = str(post['stopPrice'])
                typeMsg = '止损' if 'STOP' in post['type'] else '止盈'
                msg = typeMsg+"　触发价:"+post['stopPrice']+'　'+msg

            log(msg)
            i += 1
            if not post['reduceOnly']:
                del post['reduceOnly']

            if 'MARKET' in post['type']:
                del post['price']
                del post['timeInForce']

            okorderS.append(post)

        log("批量下单", len(orderS), okorderS[-1])
        
        fh2 = []
        for i in range(0,int(len(okorderS))+1,5):
            c=okorderS[i:i+5]
            if c:
                fh = self.go("POST","/fapi/v1/batchOrders", {'batchOrders': json.dumps(c)})
                if type(fh) == list:
                    for x in fh:
                        fh2.append(x)
                else:
                    return fh
                
        return fh2


    """获取深度信息"""
    def GetDepth(self, limit=5, symbol=0):
        # 0是价格
        # 1是数量
        # 可选值:[5, 10, 20, 50, 100, 500, 1000]
        data = self.go("GET","/fapi/v1/depth",{'symbol':symbol,'limit':limit})
        if not isinstance(data,dict) or 'bids' not in data.keys():
            time.sleep(0.1)
            tlog(symbol, [limit, '获取深度失败', data], 1)
            return self.GetDepth(limit, symbol)

        okdata = []
        for d in data['bids']:
            d[0] = float(d[0])
            d[1] = float(d[1])
            okdata.append(d)
        data['bids'] = okdata

        okdata = []
        for d in data['asks']:
            d[0] = float(d[0])
            d[1] = float(d[1])
            okdata.append(d)
        data['asks'] = okdata
        data['t'] = NowTime_ms()
        return data
    

    """获取持仓信息"""
    def GetPos(self, symbol=0, side=0, all=0):
        if not symbol:
            data = self.go("GET","/fapi/v2/positionRisk")
        else:
            data = self.go("GET","/fapi/v2/positionRisk", {'symbol': symbol})

        if data:
            okdata = []
            for v in data:
                if not isinstance(v,dict) or 'entryPrice' not in v.keys():
                    print('获取持仓失败', data)
                    time.sleep(0.2)
                    return self.GetPos(symbol, side)
                ok = {}
                ok['symbol'] = v['symbol']
                x = X(ok['symbol'])
                ok['liang'] = abs(float(v['positionAmt'])) * x
                if ok['liang'] == 0:
                    continue

                ok['side'] = 'BUY' if float(v['positionAmt']) > 0 else 'SELL' #持仓方向
                ok['side2'] = 'SELL' if float(v['positionAmt']) > 0 else 'BUY' #卖出方向
                ok['posSide'] = 'long' if ok['side'] == 'BUY' else 'short'
                ok['jiage'] = float(v['entryPrice']) / x
                ok['nowJiage'] = float(v['markPrice']) / x
                ok['qiangJiage'] = float(v['liquidationPrice']) / x
                ok['yingkui'] = N(float(v['unRealizedProfit']),2)
                #保证金计算：持仓数量 * 最新价格 / 杠杆倍数
                ok['bzj'] = N(abs(ok['liang']) / x * float(v['markPrice']) / int(v['leverage']),2)
                ok['bzj'] = ok['bzj'] if ok['bzj'] else 0.01
                #回报率计算：盈亏 / 保证金 * 100
                ok['roe'] = N(ok['yingkui'] / ok['bzj'] * 100,2) if ok['bzj'] else 0.01
                ok['update_time'] = v['updateTime']
                
                if not symbol:
                    okdata.append(ok)

                elif not side or side == ok['side']: #持仓方向判断
                    return ok

            return okdata

        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """k线数据"""
    def GetKlines(self,interval='5m',limit=200,xianhuo=0, startTime='', endTime='', xiufu=0, symbol=0, down=0, yuanshi=0):
        data = {
            'symbol': symbol,
            'interval':interval,
            'limit':limit,
        }
        if startTime:
            data['startTime'] = startTime
        if endTime:
            data['endTime'] = endTime
            
        for i in range(10):
            if xianhuo:
                r = self.go("GET","/api/v3/klines", data, needKey=0, url='https://api.binance.com')
            else:
                r = self.go("GET","/fapi/v1/klines", data)

            try:
                Ktime = int(r[-1][0])
                endKtime = int(r[-1][6])
            except:
                r = False

            if not r:
                time.sleep(0.5)
                continue
                
            #K线时间不对 当前时间减去K线时间大于5m  
            # 9-9 13:33：用的秒判断，当前为0秒就失效了！改用毫秒
            # 9-10 16:27：自己补数据很多假信号...
            # 10-9 8:23：测试一下修复
            #min = int(interval.replace('m','')) * 60 * 1000
            nowTime = NowTime_ms()

            if nowTime > endKtime and \
            (not down or GetTime(nowTime, "%m-%d-%H-%M") != GetTime(endKtime, "%m-%d-%H-%M") ):
                tlog('K线数据异常 当前时间',
                     [symbol , GetTime(nowTime, echoMs=1), 'K线时间', GetTime(Ktime, echoMs=1), '执行修复' if xiufu else ''], 5)
                if xiufu:
                    min = endKtime - Ktime + 1
                    r.append(r[-1].copy())
                    r[-1][0] = Ktime+min
                    r[-1][6] = endKtime+min
                    break
                time.sleep(0.5)
                continue
            break

        okdata = []
        if r:
            if yuanshi:
                return r
            for v in r:
                okdata.append({
                    'Time': int(v[0]),
                    'Open': float(v[1]),
                    'High': float(v[2]),
                    'Low': float(v[3]),
                    'Close': float(v[4]),
                    'Volume': float(v[5]),
                    'endTime': int(v[6]),
                })
        return okdata



    def GetData(self, jsonPath, isFile=0):
        jsonPath = self.klines_dir+jsonPath
        if not os.path.exists(jsonPath):
            return []
        
        if isFile:
            return True

        with open(jsonPath) as file_obj:
            return json.load(file_obj)


    def SaveData(self, jsonPath, okdata):
        if not os.path.exists(self.klines_dir):
            os.makedirs(self.klines_dir)

        jsonPath = self.klines_dir+jsonPath
        with open(jsonPath,'w') as file_obj:
            json.dump(okdata,file_obj)

        
    def GetKlines2(self, symbol, startTimeSet=0, endTimeSet=0, k_time=0, xianhuo=0, xiufu=0):
        timeStr = '%Y-%m-%d %H:%M:%S'
        startTime = int(time.mktime(time.strptime(startTimeSet,'%Y-%m-%d %H:%M:%S')))
        endTime = int(time.mktime(time.strptime(endTimeSet,'%Y-%m-%d %H:%M:%S')))

        okdata = []
        startTime2 = 0
        limit = 1000
        stop = 1
        while stop:
            startTimeTmp = startTime2 if startTime2 else startTime*1000
            jsonPath2 = symbol+'_'+str(startTimeTmp)+'_'+str(xianhuo)+'_'+k_time+'.json'
            
            data = self.GetData(jsonPath2)
            save = 0
            if not data:
                data = self.GetKlines(k_time, limit=limit, startTime=startTimeTmp, xianhuo=xianhuo, symbol=symbol, down=1, xiufu=xiufu)
                save = 1

            print(symbol, 
                'startTime', GetTime(data[0]['Time'],timeStr),
                'endTime',GetTime(data[-1]['Time'],timeStr))

            
            for v in data:
                okdata.append(v)
                startTime2 = data[-1]['Time'] + 60 * 1000   # +1m
                vTime = int(str(v['endTime'])[:10])
                if vTime >= endTime or vTime >= time.time():
                    stop = 0
                    break

            if not stop:
                print(symbol, 'Stop1')
                break
            
            if len(data) < limit:
                print(symbol, 'Stop2')
                break
            
            if save:
                self.SaveData(jsonPath2, data)  #上面有break

            time.sleep(0.1)
        
        print(symbol, "ok loading...")
        return okdata


    """
        MAIN_C2C 现货钱包转向C2C钱包
        MAIN_UMFUTURE 现货钱包转向U本位合约钱包
        MAIN_CMFUTURE 现货钱包转向币本位合约钱包
        MAIN_MARGIN 现货钱包转向杠杆全仓钱包
        MAIN_MINING 现货钱包转向矿池钱包
        C2C_MAIN C2C钱包转向现货钱包
        C2C_UMFUTURE C2C钱包转向U本位合约钱包
        C2C_MINING C2C钱包转向矿池钱包
        UMFUTURE_MAIN U本位合约钱包转向现货钱包
        UMFUTURE_C2C U本位合约钱包转向C2C钱包
        UMFUTURE_MARGIN U本位合约钱包转向杠杆全仓钱包
        CMFUTURE_MAIN 币本位合约钱包转向现货钱包
        MARGIN_MAIN 杠杆全仓钱包转向现货钱包
        MARGIN_UMFUTURE 杠杆全仓钱包转向U本位合约钱包
        MINING_MAIN 矿池钱包转向现货钱包
        MINING_UMFUTURE 矿池钱包转向U本位合约钱包
        MINING_C2C 矿池钱包转向C2C钱包
        MARGIN_CMFUTURE 杠杆全仓钱包转向币本位合约钱包
        CMFUTURE_MARGIN 币本位合约钱包转向杠杆全仓钱包
        MARGIN_C2C 杠杆全仓钱包转向C2C钱包
        C2C_MARGIN C2C钱包转向杠杆全仓钱包
        MARGIN_MINING 杠杆全仓钱包转向矿池钱包
        MINING_MARGIN 矿池钱包转向杠杆全仓钱包
        MAIN_PAY 现货钱包转向支付钱包
        PAY_MAIN 支付钱包转向现货钱包
    """
    def Huazhuan(self, liang, lType='UMFUTURE_MAIN', bi=0):
        #liang = N(liang,4)
        bi = self.ccyBi if not bi else bi
        log('划转 数量',liang,'类型',lType, '币种', bi)
        post = {
            'type': lType,   #U本位合约钱包转向现货钱包
            'asset': bi,
            'amount': liang,
        }
        data = self.go("POST", "/sapi/v1/asset/transfer", post, 'https://api.binance.com')
        
        if data and 'tranId' in data:
            log(lType, liang, Color('划转成功', 1), data)
            return True
        else:
            log(lType, liang, Color('划转失败', -1), data)
            return False
    

    """设置杠杆倍数"""
    def SetGangGan(self, symbol, beishu):
        beishu = str(int(beishu))
        fh = self.go("POST","/fapi/v1/leverage",{'symbol': symbol,'leverage': beishu})

        if not self.ips:
            time.sleep(0.1)

        if 'maxNotionalValue' not in fh:
            log(symbol, '币安设置杠杆失败！', beishu, fh, Color('', -1))
            if 'Huobi_1_球球_1' not in ServerName:
                time.sleep(5)
                return self.SetGangGan(symbol, beishu)
        
        else:
            log(symbol, '币安设置杠杠', beishu, fh)

    
    """获取现货资产"""
    def GetXianhuo(self, bi=0):
        bi = self.ccyBi if not bi else bi
        data = self.go("POST", "/sapi/v3/asset/getUserAsset", url='https://api.binance.com')

        no = 0
        if not data or type(data) != list:
            no = 1
            
        else:
            ok = 0
            for info in data:
                if info['asset'] == bi: #USDT
                    ok = info
                    break
            if not ok:
                no = 1

        if no:
            print(bi, '获取现货余额失败', data)
            return 0

        return float(ok['free'])


    """获取可用余额"""
    def GetYuer(self, p=1, bi=0):
        bi = self.ccyBi if not bi else bi
        data = self.go("GET","/fapi/v2/balance")
        no = 0
        if not data or type(data) != list:
            no = 1
            
        else:
            ok = 0
            for info in data:
                if info['asset'] == bi: #USDT
                    ok = info
                    break
            if not ok:
                no = 1

        if no:
            print(bi, self.ApiKey, '获取余额失败', data)
            time.sleep(0.1)
            return self.GetYuer(p, bi)
            # os._exit(0)

        okk = {}
        okk['all'] = Si(float(ok['balance'])+float(ok['crossUnPnl']) if float(ok['balance']) > 0 or float(ok['balance']) < 0 else 0.01, 4)
        okk['keyong'] = Si(ok['maxWithdrawAmount'] if float(ok['maxWithdrawAmount']) > 0 else 0.01, 4)
        okk['yingkui'] = Si(ok['crossUnPnl'], 4)
        if p:
            log(p+'  币安余额'+bi if type(p) == str else '币安余额'+bi, okk['all'], '可用', N(okk['keyong'], 4),
                 '持仓盈亏', N(okk['yingkui'], 4), '时间', GetTime(ok['updateTime'], echoMs=1))

        # print(bi)
        # pprint(okk)
        return okk


    """ 获取子账户余额"""
    def GetZiYuer(self, email, bi=0):
        bi = self.ccyBi if not bi else bi
        data = self.go("GET","/sapi/v1/sub-account/futures/account", {'email': email}, url='https://api.binance.com')
        
        no = 0
        if not data or 'assets' not in data:
            no = 1
            
        else:
            ok = 0
            for info in data['assets']:
                if info['asset'] == bi: #USDT
                    ok = info
                    break
            if not ok:
                no = 1

        if no:
            print('获取余额失败', data)
            time.sleep(0.1)
            return self.GetYuer(email, bi)

        data = {}
        data['all'] = Si(ok['marginBalance'], 4)
        data['keyong'] = Si(ok['maxWithdrawAmount'], 4)
        data['yingkui'] = Si(ok['unrealizedProfit'], 4)
        return data

    """ 获取子账户余额"""
    def GetZiYuerSpot(self, email, bi=0):
        bi = self.ccyBi if not bi else bi
        data = self.go("GET","/sapi/v3/sub-account/assets", {'email': email}, url='https://api.binance.com')
        
        no = 0
        if not data or 'balances' not in data:
            no = 1
            
        else:
            ok = 0
            for info in data['balances']:
                if info['asset'] == bi: #USDT
                    return Si(info['free'], 6)
            if not ok:
                no = 1

        if no:
            return 0
            print('获取余额失败', data)
            time.sleep(0.1)
            return self.GetZiYuerSpot(email, bi)
        


    """"true": 双向持仓模式；"false": 单向持仓模式"""
    def SetChicang(self, side=False):
        self.chicangModel = side
        fh = self.go("POST","/fapi/v1/positionSide/dual", {'dualSidePosition':side})
        log("设置持仓模式", side, fh)
        if 'code' in fh and fh['code'] == -4068:
            log('设置持仓失败！！！')
            log('设置持仓失败！！！')
            self.chicangModel = False if side else True


    """"true": 开启站内提现；"false": 关闭站内提现"""
    def SetTixian(self,side=True):
        if side:
            log('开启站内提现')
            url = '/sapi/v1/account/enableFastWithdrawSwitch'
        else:
            log('关闭站内提现')
            url = '/sapi/v1/account/disableFastWithdrawSwitch'
        return self.go("POST", url, url='https://api.binance.com')


    """提现"""
    def Tixian(self, dizhi, liang, bi='', net='TRX'):
        bi = bi if bi else self.ccyBi
        data = self.go("POST","/sapi/v1/capital/withdraw/apply",{'coin':bi, 'address':dizhi, 'network': net, 'amount':liang, 'transactionFeeFlag': True}, url='https://api.binance.com')
        log('币安提现 地址', dizhi, '网络', net, '数量', liang, '币种', bi, '结果', data)
        return data


    """创建子账户"""
    def CreateNumber(self, username):
        data = self.go("POST","/sapi/v1/sub-account/virtualSubAccount", {'subAccountString':username}, url='https://api.binance.com')
        log('创建子账户', username, '返回', data)
        
        data = self.go("POST","/sapi/v1/sub-account/futures/enable", {'email': data['email']}, url='https://api.binance.com')
        return data


    """ 子母账户合约相互转账"""
    def ziTixian(self, fa, jie, liang, bi=''):
        bi = bi if bi else self.ccyBi
        data = {
            'fromEmail': fa,
            'toEmail': jie,
            'futuresType': '1', 
            'asset': bi,
            'amount': liang,
        }
        data = self.go("POST","/sapi/v1/sub-account/futures/internalTransfer", data, url='https://api.binance.com')

        log('[集群均衡]', '发送者', fa, '接受者', jie, bi, liang, '结果', data, Color('', 1))
        return data
    

    """ 子母账户相互转账"""
    def ziHua(self, liang, faWallet, jieWallet, fa='', jie='', bi=''):

        for i in range(5):
            bi = bi if bi else self.ccyBi
            data = {
                'fromAccountType': faWallet,    #"SPOT","USDT_FUTURE","COIN_FUTURE","MARGIN"(Cross),"ISOLATED_MARGIN"
                'toAccountType': jieWallet, 
                'asset': bi,
                'amount': liang,
            }
            if fa:
                data['fromEmail'] = fa
            if jie:
                data['toEmail'] = jie

            data = self.go("POST","/sapi/v1/sub-account/universalTransfer", data, url='https://api.binance.com')
            msg = ['[万向划转]', '发送者', fa, faWallet, '接受者', jie, bi, jieWallet, '币种', bi, liang, '结果', data]
            log(*msg)

            if 'tranId' in data:
                return data['tranId']

            uploadError('币安万向划转失败！'+str(msg))
            time.sleep(2)

        return 0

    """ 子母账户相互转账"""
    def ziTi(self, side, mail, liang, bi=''):
        bi = bi if bi else self.ccyBi

        for i in range(5):
            data = {
                'asset': bi,
                'amount': liang,
            }
            if side == 'in':
                data['toEmail'] = mail
                fh = self.go("POST", "/sapi/v1/managed-subaccount/deposit", data, url='https://api.binance.com')

            else:
                data['fromEmail'] = mail
                fh = self.go("POST", "/sapi/v1/managed-subaccount/withdraw", data, url='https://api.binance.com')


            msg = ['[托管子账户]', mail, '充值' if side == 'in' else '提现', '币种', bi, liang, '结果', fh]
            log(*msg)

            if 'tranId' in fh:
                return fh['tranId']

            uploadError('币安托管子账户划转失败！'+str(msg))
            time.sleep(2)

        return 0
        

    """ 获取Userid"""
    def GetEmail(self):
        data = self.go("GET", "/api/spot/v1/account/getInfo")
        return data['data']['user_id']


    """获取当前挂单"""
    def GetOrders(self, symbol=0):
        data = self.go("GET","/fapi/v1/openOrders",{'symbol': symbol})
        return data

    """获取所有挂单"""
    def GetAllOrders(self):
        data = self.go("GET","/fapi/v1/openOrders")
        return data


    """合约最新价格"""
    def GetNowJiage(self, symbol=0):
        
        data = self.go("GET","/fapi/v1/ticker/price",{'symbol': symbol})
        if not isinstance(data, dict) or 'price' not in data:
            print(symbol, '获取最新价格失败',data)
            time.sleep(0.11)
            return self.GetNowJiage(symbol)
        return float(data['price'])


    """获取开平仓的价格"""
    def GetSideJiage(self, side, symbol=0):
        
        
        newDepth = self.GetDepth(10, symbol)
        jiage = newDepth['bids'][0][0] if side == 'BUY' else newDepth['asks'][0][0]
        return jiage

    """ Websockt监听账户需要的Key"""
    def GetlistenKey(self):
        data = self.go("POST","/fapi/v1/listenKey")
        if not data or 'listenKey' not in data.keys():
            log('获取listenKey失败',data)
            time.sleep(0.1)
            return self.GetlistenKey()
        return data['listenKey']

    """ 延长Key"""
    def PutlistenKey(self):
        return self.go("PUT","/fapi/v1/listenKey")


    """币安服务器时间"""
    def GetTime(self):
        return self.go('GET', '/fapi/v1/time')['serverTime']

    """充值地址"""
    def GetAddress(self, bi='', net='TRX'):
        bi = bi if bi else self.ccyBi
        data = self.go('GET', '/sapi/v1/capital/deposit/address', {'coin': bi, 'network': net}, url='https://api.binance.com')
        if 'address' in data:
            log(bi, net, '币安充值地址', data['address'])
            return data['address']

        else:
            log(bi, net, '获取地址失败', data)
            return ''

    """充值地址"""
    def GetNet(self, bi=''):
        bi = bi if bi else self.ccyBi
        data = self.go('GET', '/sapi/v1/capital/config/getall', {}, url='https://api.binance.com')
        for vv in data:
            for v in vv['networkList']:
                if v['coin'] == bi:
                    pprint(v['network'])
        

    """获取所有交易对信息"""
    def GetSymbols(self):
        data = self.go("GET","/fapi/v1/exchangeInfo")

        if not data or 'symbols' not in data.keys():
            log('获取交易对信息失败',data)
            uploadLog(isExit=1)
            return self.GetSymbols()

        for v in data['symbols']:
            try:
                L = WeiShu(v['filters'][1]['stepSize'])
                self.setsData[v['symbol']] = {
                    'J': WeiShu(v['filters'][0]['tickSize']),
                    'L': L,
                    'M': N(float(v['filters'][2]['maxQty']), L),
                }
                
            except:
                log('Bian解析Symbols出错', v)
            
        return data['symbols']


    """获取所有交易对信息"""
    def GetGanggan(self, symbol=''):
        post = {}
        if symbol:
            post['symbol'] = symbol

        data = self.go("GET","/fapi/v1/leverageBracket", post)
        return data
        

    """查询订单信息"""
    def GetOrder(self, orderId, symbol=0):
        

        data = self.go("GET","/fapi/v1/order", {'symbol': symbol, 'orderId': orderId})

        if not data or 'avgPrice' not in data.keys():
            tlog(symbol+' 获取订单信息失败', [str(orderId), data], 2)
            return False
            
        return {
            'symbol': symbol,            #交易对
            'status': data['status'],            #状态
            'jiage': float(data['avgPrice']),      #价格
            'cJiage': float(data['stopPrice']),      #触发价格
            'liang': float(data['executedQty']),      #数量
            'yingkui': 0, #占位
            'side': data['side'],              #方向
            'time': data['time'],              #推送时间
        }


    """获取特殊费率的币"""
    def Get4HoursSymbol(self):
        return self.go("GET", "/fapi/v1/fundingInfo")

    """获取用户信息"""
    def GetUserInfo(self):
        return self.go("GET", "/fapi/v2/account")

    """获取近期成交历史"""
    def GetTrades(self, symbol):
        data = self.go("GET", "/fapi/v1/trades", {'symbol': symbol})
        return data

    """获取成交历史"""
    def GetUserTrades(self):
        data = self.go("GET", "/fapi/v1/userTrades")
        log(Color('上传成交历史', 1))
        logTrades(data)
        return data
    

    """获取用户费率"""
    def GetVip(self, symbol=0):
        
        return self.go("GET", "/fapi/v1/commissionRate", {'symbol': symbol})

    """获取历史订单"""
    def GetAllOrders(self, symbol=0):
        
        return self.go("GET", "/fapi/v1/allOrders", {'symbol': symbol})


    """获取交易对价格与价格之间的差距"""
    def GetChaju(self, symbol, ws):
        minws = 1
        for i in range(ws):
            minws /= 10

        depth = self.GetDepth(10, symbol)
        chaju = depth['bids'][0][0] - depth['bids'][1][0]
        chaju = minws if chaju < minws else chaju
        chaju = round(chaju, ws)
        return chaju


    """根据价格X 生成系列价格"""
    def MakeJiage(self, jiage, chaju, ws, symbol=0, max=6):
        
        
        jiages = [jiage]
        for i in range(1, max+1):
            jiages.append( round(jiage - (chaju * i), ws) )
            jiages.append( round(jiage + (chaju * i), ws) )

        log(symbol, "生成价格", jiage, "循环"+str(max), "小数"+str(ws), "位移", round(chaju, ws), "结果", jiages)
        return jiages
    

    """获取子账户列表"""
    def GetZiList(self):
        data = self.go("GET", "/sapi/v1/sub-account/list", url='https://api.binance.com')
        return [v['email'] for v in data['subAccounts']]

    """ 获取子账户充值地址"""
    def GetZiAddress(self, email, net='BSC'):
        data = self.go("GET", "/sapi/v1/capital/deposit/subAddress", {'email': email, 'coin': 'USDT', 'network': net}, url='https://api.binance.com')
        return data['address']

    """ 获取子账户充值地址"""
